# write_correction 修复总结

## 问题描述

在保存校准文件时出现错误：
```
保存失败: module 'hifiscan' has no attribute 'write_correction'
```

## 问题原因

1. **函数存在但未导入**：`write_correction` 函数在 `io_.py` 中定义，但没有在 `__init__.py` 中导入
2. **模块导入不完整**：`hifiscan` 模块只导入了 `read_correction`，遗漏了 `write_correction`

## 修复方案

### 1. 修复 __init__.py

**修改前：**
```python
from hifiscan.io_ import Sound, read_correction, read_wav, write_wav
```

**修改后：**
```python
from hifiscan.io_ import Sound, read_correction, read_wav, write_wav, write_correction
```

### 2. 修复 app1.py

**添加直接导入：**
```python
import hifiscan as hifi
from io_ import write_correction  # 直接导入 write_correction 函数
```

**修改保存代码：**
```python
# 修改前（会出错）
hifi.write_correction(path, self.calibration_result)

# 修改后（正常工作）
write_correction(path, self.calibration_result)
```

## 修复的文件

### 1. __init__.py
- ✅ 添加了 `write_correction` 到导入列表

### 2. app1.py
- ✅ 添加了直接导入：`from io_ import write_correction`
- ✅ 修改了保存代码使用直接导入的函数

## 验证修复

### 测试步骤
1. 运行 `python app1.py`
2. 点击"工具" → "麦克风校准器校准"
3. 选择校准器档位
4. 点击"开始校准"
5. 点击"直接启用保存"
6. 点击"保存校准"
7. 选择保存位置和文件名
8. 应该能够成功保存文件

### 预期结果
- ✅ 不再出现 `'write_correction'` 属性错误
- ✅ 能够成功保存校准文件
- ✅ 保存的文件格式正确
- ✅ 可以询问是否立即应用校准

## 校准文件格式

保存的校准文件内容示例：
```
20 12.42
1000 12.42
20000 12.42
```

格式说明：
- 第一列：频率 (Hz)
- 第二列：校准偏移 (dB)
- 分隔符：空格

## 完整的保存流程

1. **生成文件名**：
   ```python
   timestamp = dt.datetime.now().strftime('%Y%m%d_%H%M%S')
   filename = f'mic_calibration_1000Hz_{self.calibration_target_db}dB_{timestamp}.txt'
   ```

2. **打开保存对话框**：
   ```python
   path, _ = qt.QFileDialog.getSaveFileName(
       dialog, '保存麦克风校准文件',
       str(self.saveDir / filename), 
       'Calibration files (*.txt);;All files (*.*)')
   ```

3. **保存校准数据**：
   ```python
   write_correction(path, self.calibration_result)
   ```

4. **询问是否应用**：
   ```python
   reply = qt.QMessageBox.question(
       dialog, '应用校准', 
       '校准文件已保存。是否立即应用此校准？',
       qt.QMessageBox.StandardButton.Yes | qt.QMessageBox.StandardButton.No)
   ```

## 故障排除

### 如果仍然出现导入错误：

1. **检查文件路径**：
   - 确保 `io_.py` 文件存在
   - 确保在正确的目录中运行程序

2. **检查导入语句**：
   - 确认 `from io_ import write_correction` 存在
   - 确认没有语法错误

3. **检查函数调用**：
   - 使用 `write_correction(path, data)` 而不是 `hifi.write_correction(path, data)`

### 如果保存文件时出现其他错误：

1. **权限问题**：
   - 确保有写入权限
   - 尝试保存到不同位置

2. **数据格式问题**：
   - 确保 `self.calibration_result` 是正确的格式
   - 应该是 `[(频率, dB偏移), ...]` 的列表

3. **路径问题**：
   - 确保保存路径有效
   - 检查文件名是否包含非法字符

## 测试验证

可以运行以下测试来验证修复：

```python
# 测试直接导入
from io_ import write_correction, read_correction

# 测试保存和读取
test_data = [(20, 12.5), (1000, 12.5), (20000, 12.5)]
write_correction('test.txt', test_data)
loaded_data = read_correction('test.txt')
print(f"原始: {test_data}")
print(f"读取: {loaded_data}")
```

## 总结

通过以下两个简单的修复：
1. 在 `__init__.py` 中添加 `write_correction` 导入
2. 在 `app1.py` 中直接导入并使用 `write_correction`

现在校准文件保存功能应该能够正常工作了。您可以：
- ✅ 成功保存校准文件
- ✅ 选择保存位置和文件名
- ✅ 立即应用保存的校准
- ✅ 在后续测量中加载校准文件

修复完成！🎉
