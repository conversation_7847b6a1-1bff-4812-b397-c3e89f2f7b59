('/Users/<USER>/Local/zip/build/app0/app0.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz', '/Users/<USER>/Local/zip/build/app0/PYZ-00.pyz', 'PYZ'),
  ('lib-dynload/_struct.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_struct.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/zlib.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/zlib.cpython-312-darwin.so',
   'EXTENSION'),
  ('struct',
   '/Users/<USER>/Local/zip/build/app0/localpycs/struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   '/Users/<USER>/Local/zip/build/app0/localpycs/pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   '/Users/<USER>/Local/zip/build/app0/localpycs/pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   '/Users/<USER>/Local/zip/build/app0/localpycs/pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyInstaller/loader/pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyInstaller/hooks/rthooks/pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyInstaller/hooks/rthooks/pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyInstaller/hooks/rthooks/pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_pyqtgraph_multiprocess',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/_pyinstaller_hooks_contrib/rthooks/pyi_rth_pyqtgraph_multiprocess.py',
   'PYSOURCE'),
  ('pyi_rth_mplconfig',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyInstaller/hooks/rthooks/pyi_rth_mplconfig.py',
   'PYSOURCE'),
  ('pyi_rth_cryptography_openssl',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/_pyinstaller_hooks_contrib/rthooks/pyi_rth_cryptography_openssl.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt6',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pyqt6.py',
   'PYSOURCE'),
  ('app0', '/Users/<USER>/Local/zip/app0.py', 'PYSOURCE'),
  ('torch/bin/protoc',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/bin/protoc',
   'BINARY'),
  ('torch/lib/libomp.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/lib/libomp.dylib',
   'BINARY'),
  ('torch/lib/libshm.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/lib/libshm.dylib',
   'BINARY'),
  ('torch/lib/libtorch.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/lib/libtorch.dylib',
   'BINARY'),
  ('torch/lib/libc10.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/lib/libc10.dylib',
   'BINARY'),
  ('torch/bin/torch_shm_manager',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/bin/torch_shm_manager',
   'BINARY'),
  ('torch/bin/protoc-********',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/bin/protoc-********',
   'BINARY'),
  ('torch/lib/libtorch_python.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/lib/libtorch_python.dylib',
   'BINARY'),
  ('torch/lib/libtorch_cpu.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/lib/libtorch_cpu.dylib',
   'BINARY'),
  ('torch/lib/libtorch_global_deps.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/lib/libtorch_global_deps.dylib',
   'BINARY'),
  ('libpython3.12.dylib',
   '/Users/<USER>/miniconda3/lib/libpython3.12.dylib',
   'BINARY'),
  ('llvmlite/binding/libllvmlite.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/llvmlite/binding/libllvmlite.dylib',
   'BINARY'),
  ('_sounddevice_data/portaudio-binaries/libportaudio.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/_sounddevice_data/portaudio-binaries/libportaudio.dylib',
   'BINARY'),
  ('torchaudio/lib/libtorchaudio.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/lib/libtorchaudio.so',
   'BINARY'),
  ('torchaudio/.dylibs/libc++.1.0.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/.dylibs/libc++.1.0.dylib',
   'BINARY'),
  ('torchaudio/lib/libtorchaudio_sox.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/lib/libtorchaudio_sox.so',
   'BINARY'),
  ('_soundfile_data/libsndfile_arm64.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/_soundfile_data/libsndfile_arm64.dylib',
   'BINARY'),
  ('ossl-modules/legacy.dylib',
   '/Users/<USER>/miniconda3/lib/ossl-modules/legacy.dylib',
   'BINARY'),
  ('onnxruntime/capi/libonnxruntime.1.20.1.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/onnxruntime/capi/libonnxruntime.1.20.1.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/styles/libqmacstyle.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/plugins/styles/libqmacstyle.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/imageformats/libqmacheif.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/plugins/imageformats/libqmacheif.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/platforms/libqminimal.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/plugins/platforms/libqminimal.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/iconengines/libqsvgicon.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/plugins/iconengines/libqsvgicon.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/imageformats/libqsvg.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/plugins/imageformats/libqsvg.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/imageformats/libqwebp.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/plugins/imageformats/libqwebp.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/imageformats/libqpdf.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/plugins/imageformats/libqpdf.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/imageformats/libqgif.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/plugins/imageformats/libqgif.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/platforms/libqoffscreen.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/plugins/platforms/libqoffscreen.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/imageformats/libqicns.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/plugins/imageformats/libqicns.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/imageformats/libqtga.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/plugins/imageformats/libqtga.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/imageformats/libqmacjp2.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/plugins/imageformats/libqmacjp2.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/imageformats/libqico.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/plugins/imageformats/libqico.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/imageformats/libqtiff.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/plugins/imageformats/libqtiff.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/platforms/libqcocoa.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/plugins/platforms/libqcocoa.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/imageformats/libqjpeg.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/plugins/imageformats/libqjpeg.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/imageformats/libqwbmp.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/plugins/imageformats/libqwbmp.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/generic/libqtuiotouchplugin.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/plugins/generic/libqtuiotouchplugin.dylib',
   'BINARY'),
  ('lib-dynload/_statistics.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_statistics.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_contextvars.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_contextvars.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_decimal.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_decimal.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_hashlib.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_hashlib.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha3.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_sha3.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_blake2.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_blake2.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_md5.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_md5.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha1.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_sha1.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha2.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_sha2.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_random.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_random.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_bisect.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_bisect.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/math.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/math.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/grp.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/grp.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_lzma.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_lzma.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_bz2.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_bz2.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/unicodedata.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/unicodedata.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/array.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/array.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/select.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/select.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_socket.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_socket.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/binascii.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/binascii.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_opcode.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_opcode.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_csv.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_csv.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/resource.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/resource.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_posixsubprocess.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_posixsubprocess.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/fcntl.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/fcntl.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_posixshmem.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_posixshmem.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_multiprocessing.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_multiprocessing.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/pyexpat.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/pyexpat.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_scproxy.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_scproxy.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/termios.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/termios.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_ssl.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_ssl.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/mmap.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/mmap.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_ctypes.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_ctypes.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_queue.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_queue.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/syslog.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/syslog.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/readline.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/readline.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_json.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_json.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_heapq.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_heapq.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_multibytecodec.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_multibytecodec.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_jp.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_codecs_jp.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_kr.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_codecs_kr.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_iso2022.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_codecs_iso2022.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_cn.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_codecs_cn.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_tw.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_codecs_tw.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_hk.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_codecs_hk.cpython-312-darwin.so',
   'EXTENSION'),
  ('numpy/_core/_multiarray_umath.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/_core/_multiarray_umath.cpython-312-darwin.so',
   'EXTENSION'),
  ('numpy/linalg/_umath_linalg.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/linalg/_umath_linalg.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_uuid.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_uuid.cpython-312-darwin.so',
   'EXTENSION'),
  ('numba/core/typeconv/_typeconv.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/typeconv/_typeconv.cpython-312-darwin.so',
   'EXTENSION'),
  ('markupsafe/_speedups.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/markupsafe/_speedups.cpython-312-darwin.so',
   'EXTENSION'),
  ('_cffi_backend.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/_cffi_backend.cpython-312-darwin.so',
   'EXTENSION'),
  ('numpy/random/bit_generator.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/random/bit_generator.cpython-312-darwin.so',
   'EXTENSION'),
  ('numpy/random/mtrand.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/random/mtrand.cpython-312-darwin.so',
   'EXTENSION'),
  ('numpy/random/_sfc64.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/random/_sfc64.cpython-312-darwin.so',
   'EXTENSION'),
  ('numpy/random/_philox.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/random/_philox.cpython-312-darwin.so',
   'EXTENSION'),
  ('numpy/random/_pcg64.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/random/_pcg64.cpython-312-darwin.so',
   'EXTENSION'),
  ('numpy/random/_mt19937.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/random/_mt19937.cpython-312-darwin.so',
   'EXTENSION'),
  ('numpy/random/_generator.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/random/_generator.cpython-312-darwin.so',
   'EXTENSION'),
  ('numpy/random/_bounded_integers.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/random/_bounded_integers.cpython-312-darwin.so',
   'EXTENSION'),
  ('numpy/random/_common.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/random/_common.cpython-312-darwin.so',
   'EXTENSION'),
  ('numba/np/ufunc/workqueue.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/np/ufunc/workqueue.cpython-312-darwin.so',
   'EXTENSION'),
  ('numba/np/ufunc/omppool.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/np/ufunc/omppool.cpython-312-darwin.so',
   'EXTENSION'),
  ('numba/np/ufunc/_internal.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/np/ufunc/_internal.cpython-312-darwin.so',
   'EXTENSION'),
  ('numba/core/runtime/_nrt_python.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/runtime/_nrt_python.cpython-312-darwin.so',
   'EXTENSION'),
  ('numba/experimental/jitclass/_box.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/experimental/jitclass/_box.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/cmath.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/cmath.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_elementtree.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_elementtree.cpython-312-darwin.so',
   'EXTENSION'),
  ('numba/cuda/cudadrv/_extras.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cuda/cudadrv/_extras.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_lsprof.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_lsprof.cpython-312-darwin.so',
   'EXTENSION'),
  ('numba/_devicearray.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/_devicearray.cpython-312-darwin.so',
   'EXTENSION'),
  ('numba/mviewbuf.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/mviewbuf.cpython-312-darwin.so',
   'EXTENSION'),
  ('numba/_dispatcher.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/_dispatcher.cpython-312-darwin.so',
   'EXTENSION'),
  ('numba/_helperlib.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/_helperlib.cpython-312-darwin.so',
   'EXTENSION'),
  ('numba/_dynfunc.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/_dynfunc.cpython-312-darwin.so',
   'EXTENSION'),
  ('numpy/fft/_pocketfft_umath.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/fft/_pocketfft_umath.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/sparse/csgraph/_tools.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/sparse/csgraph/_tools.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/sparse/csgraph/_reordering.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/sparse/csgraph/_reordering.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/sparse/csgraph/_matching.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/sparse/csgraph/_matching.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/sparse/csgraph/_flow.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/sparse/csgraph/_flow.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/sparse/csgraph/_min_spanning_tree.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/sparse/csgraph/_min_spanning_tree.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/sparse/csgraph/_traversal.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/sparse/csgraph/_traversal.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/sparse/csgraph/_shortest_path.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/sparse/csgraph/_shortest_path.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/linalg/_fblas.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/linalg/_fblas.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/linalg/_flapack.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/linalg/_flapack.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/linalg/cython_lapack.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/linalg/cython_lapack.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/linalg/cython_blas.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/linalg/cython_blas.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/linalg/_decomp_update.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/linalg/_decomp_update.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/special/_ellip_harm_2.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/special/_ellip_harm_2.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/optimize/_highs/_highs_constants.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/optimize/_highs/_highs_constants.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/optimize/_highs/_highs_wrapper.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/optimize/_highs/_highs_wrapper.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/optimize/_direct.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/optimize/_direct.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/stats/_qmc_cy.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/stats/_qmc_cy.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/stats/_sobol.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/stats/_sobol.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/spatial/_distance_pybind.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/spatial/_distance_pybind.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/spatial/_hausdorff.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/spatial/_hausdorff.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/spatial/_distance_wrap.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/spatial/_distance_wrap.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/ndimage/_nd_image.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/ndimage/_nd_image.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/ndimage/_ni_label.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/ndimage/_ni_label.cpython-312-darwin.so',
   'EXTENSION'),
  ('matplotlib/_path.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/_path.cpython-312-darwin.so',
   'EXTENSION'),
  ('matplotlib/ft2font.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/ft2font.cpython-312-darwin.so',
   'EXTENSION'),
  ('matplotlib/backends/_backend_agg.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/backends/_backend_agg.cpython-312-darwin.so',
   'EXTENSION'),
  ('contourpy/_contourpy.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/contourpy/_contourpy.cpython-312-darwin.so',
   'EXTENSION'),
  ('PIL/_imaging.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PIL/_imaging.cpython-312-darwin.so',
   'EXTENSION'),
  ('PIL/_webp.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PIL/_webp.cpython-312-darwin.so',
   'EXTENSION'),
  ('PIL/_imagingtk.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PIL/_imagingtk.cpython-312-darwin.so',
   'EXTENSION'),
  ('PIL/_imagingft.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PIL/_imagingft.cpython-312-darwin.so',
   'EXTENSION'),
  ('PIL/_imagingcms.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PIL/_imagingcms.cpython-312-darwin.so',
   'EXTENSION'),
  ('PIL/_imagingmath.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PIL/_imagingmath.cpython-312-darwin.so',
   'EXTENSION'),
  ('matplotlib/_tri.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/_tri.cpython-312-darwin.so',
   'EXTENSION'),
  ('matplotlib/_qhull.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/_qhull.cpython-312-darwin.so',
   'EXTENSION'),
  ('kiwisolver/_cext.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/kiwisolver/_cext.cpython-312-darwin.so',
   'EXTENSION'),
  ('matplotlib/_c_internal_utils.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/_c_internal_utils.cpython-312-darwin.so',
   'EXTENSION'),
  ('matplotlib/_image.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/_image.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/interpolate/_rgi_cython.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/interpolate/_rgi_cython.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/interpolate/interpnd.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/interpolate/interpnd.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/interpolate/_rbfinterp_pythran.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/interpolate/_rbfinterp_pythran.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/interpolate/_ppoly.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/interpolate/_ppoly.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/interpolate/_bspl.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/interpolate/_bspl.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/interpolate/_dfitpack.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/interpolate/_dfitpack.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/interpolate/_fitpack.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/interpolate/_fitpack.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/stats/_unuran/unuran_wrapper.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/stats/_unuran/unuran_wrapper.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/stats/_stats_pythran.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/stats/_stats_pythran.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/fft/_pocketfft/pypocketfft.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/fft/_pocketfft/pypocketfft.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/_lib/_uarray/_uarray.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/_lib/_uarray/_uarray.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/stats/_rcont/rcont.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/stats/_rcont/rcont.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/stats/_mvn.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/stats/_mvn.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/stats/_ansari_swilk_statistics.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/stats/_ansari_swilk_statistics.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/stats/_levy_stable/levyst.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/stats/_levy_stable/levyst.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/stats/_biasedurn.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/stats/_biasedurn.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/stats/_stats.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/stats/_stats.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/special/cython_special.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/special/cython_special.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/spatial/transform/_rotation.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/spatial/transform/_rotation.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/spatial/_voronoi.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/spatial/_voronoi.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/spatial/_qhull.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/spatial/_qhull.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/spatial/_ckdtree.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/spatial/_ckdtree.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/optimize/_pava_pybind.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/optimize/_pava_pybind.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/optimize/_lsq/givens_elimination.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/optimize/_lsq/givens_elimination.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/optimize/_lsap.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/optimize/_lsap.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/linalg/_interpolative.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/linalg/_interpolative.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/optimize/_bglu_dense.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/optimize/_bglu_dense.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/optimize/_slsqp.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/optimize/_slsqp.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/optimize/_zeros.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/optimize/_zeros.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/optimize/_minpack.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/optimize/_minpack.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/optimize/_trlib/_trlib.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/optimize/_trlib/_trlib.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/optimize/_cobyla.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/optimize/_cobyla.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/optimize/_moduleTNC.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/optimize/_moduleTNC.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/optimize/_lbfgsb.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/optimize/_lbfgsb.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/optimize/_group_columns.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/optimize/_group_columns.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/integrate/_lsoda.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/integrate/_lsoda.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/integrate/_dop.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/integrate/_dop.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/integrate/_vode.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/integrate/_vode.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/integrate/_quadpack.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/integrate/_quadpack.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/integrate/_odepack.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/integrate/_odepack.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/special/_comb.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/special/_comb.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/special/_gufuncs.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/special/_gufuncs.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/special/_specfun.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/special/_specfun.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/special/_ufuncs.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/special/_ufuncs.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/special/_special_ufuncs.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/special/_special_ufuncs.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/special/_ufuncs_cxx.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/special/_ufuncs_cxx.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/linalg/_matfuncs_expm.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/linalg/_matfuncs_expm.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/linalg/_matfuncs_sqrtm_triu.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/linalg/_matfuncs_sqrtm_triu.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/linalg/_decomp_lu_cython.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/linalg/_decomp_lu_cython.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/linalg/_solve_toeplitz.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/linalg/_solve_toeplitz.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/linalg/_cythonized_array_utils.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/linalg/_cythonized_array_utils.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/sparse/linalg/_propack/_zpropack.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/sparse/linalg/_propack/_zpropack.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/sparse/linalg/_propack/_cpropack.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/sparse/linalg/_propack/_cpropack.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/sparse/linalg/_propack/_dpropack.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/sparse/linalg/_propack/_dpropack.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/sparse/linalg/_propack/_spropack.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/sparse/linalg/_propack/_spropack.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/sparse/linalg/_eigen/arpack/_arpack.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/sparse/linalg/_eigen/arpack/_arpack.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/sparse/linalg/_dsolve/_superlu.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/sparse/linalg/_dsolve/_superlu.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/sparse/_csparsetools.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/sparse/_csparsetools.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/sparse/_sparsetools.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/sparse/_sparsetools.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/io/_fast_matrix_market/_fmm_core.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/io/_fast_matrix_market/_fmm_core.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/io/matlab/_streams.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/io/matlab/_streams.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/io/matlab/_mio5_utils.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/io/matlab/_mio5_utils.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/io/matlab/_mio_utils.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/io/matlab/_mio_utils.cpython-312-darwin.so',
   'EXTENSION'),
  ('torchaudio/lib/_torchaudio_sox.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/lib/_torchaudio_sox.so',
   'EXTENSION'),
  ('torchaudio/lib/_torchaudio.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/lib/_torchaudio.so',
   'EXTENSION'),
  ('zstandard/_cffi.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/zstandard/_cffi.cpython-312-darwin.so',
   'EXTENSION'),
  ('zstandard/backend_c.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/zstandard/backend_c.cpython-312-darwin.so',
   'EXTENSION'),
  ('_brotli.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/_brotli.cpython-312-darwin.so',
   'EXTENSION'),
  ('cryptography/hazmat/bindings/_rust.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/cryptography/hazmat/bindings/_rust.cpython-312-darwin.so',
   'EXTENSION'),
  ('pycosat.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pycosat.cpython-312-darwin.so',
   'EXTENSION'),
  ('onnxruntime/capi/onnxruntime_pybind11_state.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/onnxruntime/capi/onnxruntime_pybind11_state.so',
   'EXTENSION'),
  ('google/_upb/_message.abi3.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/google/_upb/_message.abi3.so',
   'EXTENSION'),
  ('lib-dynload/_asyncio.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_asyncio.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sqlite3.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_sqlite3.cpython-312-darwin.so',
   'EXTENSION'),
  ('torch/_C.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_C.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/_lib/_ccallback_c.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/_lib/_ccallback_c.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/signal/_peak_finding_utils.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/signal/_peak_finding_utils.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/signal/_spectral.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/signal/_spectral.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/signal/_sosfilt.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/signal/_sosfilt.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/_lib/_fpumode.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/_lib/_fpumode.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/_lib/messagestream.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/_lib/messagestream.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/signal/_spline.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/signal/_spline.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/signal/_upfirdn_apply.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/signal/_upfirdn_apply.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/signal/_max_len_seq_inner.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/signal/_max_len_seq_inner.cpython-312-darwin.so',
   'EXTENSION'),
  ('scipy/signal/_sigtools.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/signal/_sigtools.cpython-312-darwin.so',
   'EXTENSION'),
  ('PyQt6/sip.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/sip.cpython-312-darwin.so',
   'EXTENSION'),
  ('PyQt6/QtTest.abi3.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/QtTest.abi3.so',
   'EXTENSION'),
  ('PyQt6/QtOpenGLWidgets.abi3.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/QtOpenGLWidgets.abi3.so',
   'EXTENSION'),
  ('PyQt6/QtOpenGL.abi3.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/QtOpenGL.abi3.so',
   'EXTENSION'),
  ('PyQt6/QtSvg.abi3.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/QtSvg.abi3.so',
   'EXTENSION'),
  ('numpy/_core/_multiarray_tests.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/_core/_multiarray_tests.cpython-312-darwin.so',
   'EXTENSION'),
  ('PyQt6/QtWidgets.abi3.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/QtWidgets.abi3.so',
   'EXTENSION'),
  ('PyQt6/QtGui.abi3.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/QtGui.abi3.so',
   'EXTENSION'),
  ('PyQt6/QtDBus.abi3.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/QtDBus.abi3.so',
   'EXTENSION'),
  ('PyQt6/QtCore.abi3.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/QtCore.abi3.so',
   'EXTENSION'),
  ('lib-dynload/_pickle.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_pickle.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_datetime.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_datetime.cpython-312-darwin.so',
   'EXTENSION'),
  ('libz.1.dylib', '/Users/<USER>/miniconda3/lib/libz.1.dylib', 'BINARY'),
  ('libcrypto.3.dylib',
   '/Users/<USER>/miniconda3/lib/libcrypto.3.dylib',
   'BINARY'),
  ('PyQt6/Qt6/lib/QtWidgets.framework/Versions/A/QtWidgets',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/lib/QtWidgets.framework/Versions/A/QtWidgets',
   'BINARY'),
  ('PyQt6/Qt6/lib/QtGui.framework/Versions/A/QtGui',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/lib/QtGui.framework/Versions/A/QtGui',
   'BINARY'),
  ('PyQt6/Qt6/lib/QtCore.framework/Versions/A/QtCore',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/lib/QtCore.framework/Versions/A/QtCore',
   'BINARY'),
  ('PyQt6/Qt6/lib/QtSvg.framework/Versions/A/QtSvg',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/lib/QtSvg.framework/Versions/A/QtSvg',
   'BINARY'),
  ('PyQt6/Qt6/lib/QtPdf.framework/Versions/A/QtPdf',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/lib/QtPdf.framework/Versions/A/QtPdf',
   'BINARY'),
  ('PyQt6/Qt6/lib/QtNetwork.framework/Versions/A/QtNetwork',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/lib/QtNetwork.framework/Versions/A/QtNetwork',
   'BINARY'),
  ('libbz2.dylib', '/Users/<USER>/miniconda3/lib/libbz2.dylib', 'BINARY'),
  ('libexpat.1.dylib',
   '/Users/<USER>/miniconda3/lib/libexpat.1.dylib',
   'BINARY'),
  ('libssl.3.dylib', '/Users/<USER>/miniconda3/lib/libssl.3.dylib', 'BINARY'),
  ('libffi.8.dylib', '/Users/<USER>/miniconda3/lib/libffi.8.dylib', 'BINARY'),
  ('libreadline.8.dylib',
   '/Users/<USER>/miniconda3/lib/libreadline.8.dylib',
   'BINARY'),
  ('scipy/special/libsf_error_state.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/special/libsf_error_state.dylib',
   'BINARY'),
  ('PIL/.dylibs/libopenjp2.2.5.2.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PIL/.dylibs/libopenjp2.2.5.2.dylib',
   'BINARY'),
  ('PIL/.dylibs/libxcb.1.1.0.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PIL/.dylibs/libxcb.1.1.0.dylib',
   'BINARY'),
  ('PIL/.dylibs/libtiff.6.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PIL/.dylibs/libtiff.6.dylib',
   'BINARY'),
  ('PIL/.dylibs/libjpeg.62.4.0.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PIL/.dylibs/libjpeg.62.4.0.dylib',
   'BINARY'),
  ('PIL/.dylibs/libz.1.3.1.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PIL/.dylibs/libz.1.3.1.dylib',
   'BINARY'),
  ('PIL/.dylibs/libwebp.7.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PIL/.dylibs/libwebp.7.dylib',
   'BINARY'),
  ('PIL/.dylibs/libwebpmux.3.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PIL/.dylibs/libwebpmux.3.dylib',
   'BINARY'),
  ('PIL/.dylibs/libwebpdemux.2.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PIL/.dylibs/libwebpdemux.2.dylib',
   'BINARY'),
  ('PIL/.dylibs/libharfbuzz.0.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PIL/.dylibs/libharfbuzz.0.dylib',
   'BINARY'),
  ('PIL/.dylibs/libfreetype.6.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PIL/.dylibs/libfreetype.6.dylib',
   'BINARY'),
  ('PIL/.dylibs/liblcms2.2.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PIL/.dylibs/liblcms2.2.dylib',
   'BINARY'),
  ('scipy/.dylibs/libgfortran.5.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/.dylibs/libgfortran.5.dylib',
   'BINARY'),
  ('libzstd.1.dylib',
   '/Users/<USER>/miniconda3/lib/libzstd.1.dylib',
   'BINARY'),
  ('libsqlite3.0.dylib',
   '/Users/<USER>/miniconda3/lib/libsqlite3.0.dylib',
   'BINARY'),
  ('PyQt6/Qt6/lib/QtTest.framework/Versions/A/QtTest',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/lib/QtTest.framework/Versions/A/QtTest',
   'BINARY'),
  ('PyQt6/Qt6/lib/QtOpenGL.framework/Versions/A/QtOpenGL',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/lib/QtOpenGL.framework/Versions/A/QtOpenGL',
   'BINARY'),
  ('PyQt6/Qt6/lib/QtOpenGLWidgets.framework/Versions/A/QtOpenGLWidgets',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/lib/QtOpenGLWidgets.framework/Versions/A/QtOpenGLWidgets',
   'BINARY'),
  ('PyQt6/Qt6/lib/QtDBus.framework/Versions/A/QtDBus',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/lib/QtDBus.framework/Versions/A/QtDBus',
   'BINARY'),
  ('libncursesw.6.dylib',
   '/Users/<USER>/miniconda3/lib/libncursesw.6.dylib',
   'BINARY'),
  ('PIL/.dylibs/libXau.6.0.0.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PIL/.dylibs/libXau.6.0.0.dylib',
   'BINARY'),
  ('PIL/.dylibs/liblzma.5.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PIL/.dylibs/liblzma.5.dylib',
   'BINARY'),
  ('PIL/.dylibs/libsharpyuv.0.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PIL/.dylibs/libsharpyuv.0.dylib',
   'BINARY'),
  ('PIL/.dylibs/libpng16.16.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PIL/.dylibs/libpng16.16.dylib',
   'BINARY'),
  ('PIL/.dylibs/libbrotlidec.1.1.0.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PIL/.dylibs/libbrotlidec.1.1.0.dylib',
   'BINARY'),
  ('scipy/.dylibs/libgcc_s.1.1.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/.dylibs/libgcc_s.1.1.dylib',
   'BINARY'),
  ('scipy/.dylibs/libquadmath.0.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/scipy/.dylibs/libquadmath.0.dylib',
   'BINARY'),
  ('libtinfow.6.dylib',
   '/Users/<USER>/miniconda3/lib/libtinfow.6.dylib',
   'BINARY'),
  ('PIL/.dylibs/libbrotlicommon.1.1.0.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PIL/.dylibs/libbrotlicommon.1.1.0.dylib',
   'BINARY'),
  ('base_library.zip',
   '/Users/<USER>/Local/zip/build/app0/base_library.zip',
   'DATA'),
  ('_sounddevice_data/portaudio-binaries/README.md',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/_sounddevice_data/portaudio-binaries/README.md',
   'DATA'),
  ('pyqtgraph/Qt/QtSvg.pyi',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/Qt/QtSvg.pyi',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-L5.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-L5.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-L18.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-L18.csv',
   'DATA'),
  ('pyqtgraph/icons/lock.png',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/icons/lock.png',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-C2s.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-C2s.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-D8.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-D8.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-D2.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-D2.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-L12.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-L12.csv',
   'DATA'),
  ('pyqtgraph/Qt/QtGui/__init__.pyi',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/Qt/QtGui/__init__.pyi',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-R4.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-R4.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-L13.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-L13.csv',
   'DATA'),
  ('pyqtgraph/icons/peegee/peegee_512px.png',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/icons/peegee/peegee_512px.png',
   'DATA'),
  ('pyqtgraph/icons/peegee/<EMAIL>',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/icons/peegee/<EMAIL>',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-CBL1.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-CBL1.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CC0 legal code - applies to virids, magma, plasma, '
   'inferno and cividis.txt',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CC0 '
   'legal code - applies to virids, magma, plasma, inferno and cividis.txt',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-D3.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-D3.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-L19.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-L19.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-D1A.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-D1A.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-L9.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-L9.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/plasma.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/plasma.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-CBTD1.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-CBTD1.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-C4.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-C4.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-L16.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-L16.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-C6.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-C6.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-D11.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-D11.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-C2.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-C2.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-L4.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-L4.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-C5s.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-C5s.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-CBTC2.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-CBTC2.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-C7.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-C7.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-C3.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-C3.csv',
   'DATA'),
  ('pyqtgraph/Qt/__init__.pyi',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/Qt/__init__.pyi',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-C6s.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-C6s.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-L14.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-L14.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-I1.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-I1.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-D9.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-D9.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-D1.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-D1.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-D13.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-D13.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-C7s.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-C7s.csv',
   'DATA'),
  ('pyqtgraph/icons/ctrl.png',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/icons/ctrl.png',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-CBTL1.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-CBTL1.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-L7.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-L7.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-L3.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-L3.csv',
   'DATA'),
  ('pyqtgraph/icons/invisibleEye.svg',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/icons/invisibleEye.svg',
   'DATA'),
  ('pyqtgraph/icons/peegee/peegee_256px.png',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/icons/peegee/peegee_256px.png',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-L8.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-L8.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-CBTC1.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-CBTC1.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-D7.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-D7.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/cividis.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/cividis.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-CBTL2.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-CBTL2.csv',
   'DATA'),
  ('pyqtgraph/icons/default.png',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/icons/default.png',
   'DATA'),
  ('pyqtgraph/Qt/QtTest.pyi',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/Qt/QtTest.pyi',
   'DATA'),
  ('pyqtgraph/colors/maps/CC-BY license - applies to CET color map data.txt',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CC-BY '
   'license - applies to CET color map data.txt',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-CBC2.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-CBC2.csv',
   'DATA'),
  ('pyqtgraph/icons/peegee/peegee.svg',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/icons/peegee/peegee.svg',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-R2.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-R2.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-C1s.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-C1s.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-D6.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-D6.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-C3s.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-C3s.csv',
   'DATA'),
  ('pyqtgraph/Qt/QtWidgets/__init__.pyi',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/Qt/QtWidgets/__init__.pyi',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-CBD1.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-CBD1.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-D12.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-D12.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-C5.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-C5.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-L15.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-L15.csv',
   'DATA'),
  ('pyqtgraph/icons/auto.png',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/icons/auto.png',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-D10.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-D10.csv',
   'DATA'),
  ('pyqtgraph/icons/peegee/<EMAIL>',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/icons/peegee/<EMAIL>',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-D4.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-D4.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/PAL-relaxed.hex',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/PAL-relaxed.hex',
   'DATA'),
  ('pyqtgraph/colors/maps/magma.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/magma.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/viridis.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/viridis.csv',
   'DATA'),
  ('pyqtgraph/Qt/QtCore/__init__.pyi',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/Qt/QtCore/__init__.pyi',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-R3.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-R3.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-L10.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-L10.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-L11.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-L11.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-L17.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-L17.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-C1.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-C1.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-CBC1.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-CBC1.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/turbo.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/turbo.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-C4s.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-C4s.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-I3.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-I3.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-L1.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-L1.csv',
   'DATA'),
  ('pyqtgraph/icons/peegee/peegee_192px.png',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/icons/peegee/peegee_192px.png',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-R1.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-R1.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-L6.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-L6.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-L2.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-L2.csv',
   'DATA'),
  ('pyqtgraph/icons/peegee/<EMAIL>',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/icons/peegee/<EMAIL>',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-I2.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-I2.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/inferno.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/inferno.csv',
   'DATA'),
  ('pyqtgraph/icons/icons.svg',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/icons/icons.svg',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-CBL2.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-CBL2.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/PAL-relaxed_bright.hex',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/PAL-relaxed_bright.hex',
   'DATA'),
  ('pyqtgraph/icons/peegee/peegee_128px.png',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/icons/peegee/peegee_128px.png',
   'DATA'),
  ('torch/utils/model_dump/skeleton.html',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/model_dump/skeleton.html',
   'DATA'),
  ('torch/py.typed',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/py.typed',
   'DATA'),
  ('torch/utils/model_dump/htm.mjs',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/model_dump/htm.mjs',
   'DATA'),
  ('torch/utils/model_dump/preact.mjs',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/model_dump/preact.mjs',
   'DATA'),
  ('torch/utils/model_dump/code.js',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/model_dump/code.js',
   'DATA'),
  ('torch/_export/serde/schema.yaml',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/serde/schema.yaml',
   'DATA'),
  ('matplotlib/mpl-data/plot_directive/plot_directive.css',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/plot_directive/plot_directive.css',
   'DATA'),
  ('matplotlib/mpl-data/sample_data/goog.npz',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/sample_data/goog.npz',
   'DATA'),
  ('matplotlib/mpl-data/images/matplotlib_large.png',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/images/matplotlib_large.png',
   'DATA'),
  ('matplotlib/mpl-data/fonts/pdfcorefonts/ZapfDingbats.afm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/pdfcorefonts/ZapfDingbats.afm',
   'DATA'),
  ('matplotlib/mpl-data/images/hand.svg',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/images/hand.svg',
   'DATA'),
  ('matplotlib/mpl-data/fonts/ttf/DejaVuSans.ttf',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans.ttf',
   'DATA'),
  ('matplotlib/mpl-data/fonts/ttf/STIXSizOneSymReg.ttf',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/ttf/STIXSizOneSymReg.ttf',
   'DATA'),
  ('matplotlib/mpl-data/images/zoom_to_rect-symbolic.svg',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/images/zoom_to_rect-symbolic.svg',
   'DATA'),
  ('matplotlib/mpl-data/images/subplots.pdf',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/images/subplots.pdf',
   'DATA'),
  ('matplotlib/mpl-data/stylelib/seaborn-v0_8-dark.mplstyle',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/stylelib/seaborn-v0_8-dark.mplstyle',
   'DATA'),
  ('matplotlib/mpl-data/stylelib/seaborn-v0_8-ticks.mplstyle',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/stylelib/seaborn-v0_8-ticks.mplstyle',
   'DATA'),
  ('matplotlib/mpl-data/images/back.svg',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/images/back.svg',
   'DATA'),
  ('matplotlib/mpl-data/fonts/ttf/STIXSizOneSymBol.ttf',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/ttf/STIXSizOneSymBol.ttf',
   'DATA'),
  ('matplotlib/mpl-data/images/home.pdf',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/images/home.pdf',
   'DATA'),
  ('matplotlib/mpl-data/fonts/afm/ptmb8a.afm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/afm/ptmb8a.afm',
   'DATA'),
  ('matplotlib/mpl-data/sample_data/s1045.ima.gz',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/sample_data/s1045.ima.gz',
   'DATA'),
  ('matplotlib/mpl-data/fonts/pdfcorefonts/Courier-Bold.afm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/pdfcorefonts/Courier-Bold.afm',
   'DATA'),
  ('matplotlib/mpl-data/sample_data/embedding_in_wx3.xrc',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/sample_data/embedding_in_wx3.xrc',
   'DATA'),
  ('matplotlib/mpl-data/stylelib/seaborn-v0_8-white.mplstyle',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/stylelib/seaborn-v0_8-white.mplstyle',
   'DATA'),
  ('matplotlib/mpl-data/images/subplots-symbolic.svg',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/images/subplots-symbolic.svg',
   'DATA'),
  ('matplotlib/mpl-data/images/matplotlib.pdf',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/images/matplotlib.pdf',
   'DATA'),
  ('matplotlib/mpl-data/fonts/ttf/DejaVuSans-Oblique.ttf',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans-Oblique.ttf',
   'DATA'),
  ('matplotlib/mpl-data/images/qt4_editor_options.png',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/images/qt4_editor_options.png',
   'DATA'),
  ('matplotlib/mpl-data/fonts/afm/pagdo8a.afm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/afm/pagdo8a.afm',
   'DATA'),
  ('matplotlib/mpl-data/sample_data/axes_grid/bivariate_normal.npy',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/sample_data/axes_grid/bivariate_normal.npy',
   'DATA'),
  ('matplotlib/mpl-data/fonts/afm/cmex10.afm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/afm/cmex10.afm',
   'DATA'),
  ('matplotlib/mpl-data/fonts/afm/pcrro8a.afm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/afm/pcrro8a.afm',
   'DATA'),
  ('matplotlib/mpl-data/stylelib/_classic_test_patch.mplstyle',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/stylelib/_classic_test_patch.mplstyle',
   'DATA'),
  ('matplotlib/mpl-data/stylelib/seaborn-v0_8-darkgrid.mplstyle',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/stylelib/seaborn-v0_8-darkgrid.mplstyle',
   'DATA'),
  ('matplotlib/mpl-data/fonts/afm/putr8a.afm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/afm/putr8a.afm',
   'DATA'),
  ('matplotlib/mpl-data/images/matplotlib.png',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/images/matplotlib.png',
   'DATA'),
  ('matplotlib/mpl-data/fonts/ttf/DejaVuSansMono-Oblique.ttf',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSansMono-Oblique.ttf',
   'DATA'),
  ('matplotlib/mpl-data/images/move.pdf',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/images/move.pdf',
   'DATA'),
  ('matplotlib/mpl-data/images/forward.pdf',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/images/forward.pdf',
   'DATA'),
  ('matplotlib/mpl-data/images/forward_large.png',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/images/forward_large.png',
   'DATA'),
  ('matplotlib/mpl-data/fonts/afm/phvbo8an.afm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/afm/phvbo8an.afm',
   'DATA'),
  ('matplotlib/mpl-data/sample_data/Minduka_Present_Blue_Pack.png',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/sample_data/Minduka_Present_Blue_Pack.png',
   'DATA'),
  ('matplotlib/mpl-data/stylelib/seaborn-v0_8-muted.mplstyle',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/stylelib/seaborn-v0_8-muted.mplstyle',
   'DATA'),
  ('matplotlib/mpl-data/stylelib/seaborn-v0_8-colorblind.mplstyle',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/stylelib/seaborn-v0_8-colorblind.mplstyle',
   'DATA'),
  ('matplotlib/mpl-data/fonts/afm/pplri8a.afm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/afm/pplri8a.afm',
   'DATA'),
  ('matplotlib/mpl-data/stylelib/Solarize_Light2.mplstyle',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/stylelib/Solarize_Light2.mplstyle',
   'DATA'),
  ('matplotlib/mpl-data/fonts/ttf/cmss10.ttf',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/ttf/cmss10.ttf',
   'DATA'),
  ('matplotlib/mpl-data/stylelib/ggplot.mplstyle',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/stylelib/ggplot.mplstyle',
   'DATA'),
  ('matplotlib/mpl-data/images/filesave.png',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/images/filesave.png',
   'DATA'),
  ('matplotlib/mpl-data/fonts/pdfcorefonts/readme.txt',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/pdfcorefonts/readme.txt',
   'DATA'),
  ('matplotlib/mpl-data/images/back.png',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/images/back.png',
   'DATA'),
  ('matplotlib/mpl-data/images/forward.png',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/images/forward.png',
   'DATA'),
  ('matplotlib/mpl-data/images/back.pdf',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/images/back.pdf',
   'DATA'),
  ('matplotlib/mpl-data/fonts/pdfcorefonts/Courier-BoldOblique.afm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/pdfcorefonts/Courier-BoldOblique.afm',
   'DATA'),
  ('matplotlib/mpl-data/images/forward.svg',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/images/forward.svg',
   'DATA'),
  ('matplotlib/mpl-data/fonts/pdfcorefonts/Helvetica.afm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/pdfcorefonts/Helvetica.afm',
   'DATA'),
  ('matplotlib/mpl-data/stylelib/fast.mplstyle',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/stylelib/fast.mplstyle',
   'DATA'),
  ('matplotlib/mpl-data/fonts/ttf/DejaVuSansMono-BoldOblique.ttf',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSansMono-BoldOblique.ttf',
   'DATA'),
  ('matplotlib/mpl-data/fonts/pdfcorefonts/Courier-Oblique.afm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/pdfcorefonts/Courier-Oblique.afm',
   'DATA'),
  ('matplotlib/mpl-data/fonts/pdfcorefonts/Courier.afm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/pdfcorefonts/Courier.afm',
   'DATA'),
  ('matplotlib/mpl-data/fonts/afm/pagd8a.afm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/afm/pagd8a.afm',
   'DATA'),
  ('matplotlib/mpl-data/fonts/pdfcorefonts/Times-Roman.afm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/pdfcorefonts/Times-Roman.afm',
   'DATA'),
  ('matplotlib/mpl-data/images/subplots.svg',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/images/subplots.svg',
   'DATA'),
  ('matplotlib/mpl-data/images/home_large.png',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/images/home_large.png',
   'DATA'),
  ('matplotlib/mpl-data/fonts/afm/phvro8an.afm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/afm/phvro8an.afm',
   'DATA'),
  ('matplotlib/mpl-data/images/filesave.pdf',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/images/filesave.pdf',
   'DATA'),
  ('matplotlib/mpl-data/images/zoom_to_rect.pdf',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/images/zoom_to_rect.pdf',
   'DATA'),
  ('matplotlib/mpl-data/fonts/ttf/DejaVuSans-Bold.ttf',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans-Bold.ttf',
   'DATA'),
  ('matplotlib/mpl-data/fonts/ttf/STIXSizFourSymBol.ttf',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/ttf/STIXSizFourSymBol.ttf',
   'DATA'),
  ('matplotlib/mpl-data/fonts/afm/phvro8a.afm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/afm/phvro8a.afm',
   'DATA'),
  ('matplotlib/mpl-data/fonts/pdfcorefonts/Helvetica-Oblique.afm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/pdfcorefonts/Helvetica-Oblique.afm',
   'DATA'),
  ('matplotlib/mpl-data/fonts/afm/pplr8a.afm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/afm/pplr8a.afm',
   'DATA'),
  ('matplotlib/mpl-data/images/help.svg',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/images/help.svg',
   'DATA'),
  ('matplotlib/mpl-data/sample_data/Stocks.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/sample_data/Stocks.csv',
   'DATA'),
  ('matplotlib/mpl-data/images/back_large.png',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/images/back_large.png',
   'DATA'),
  ('matplotlib/mpl-data/fonts/pdfcorefonts/Helvetica-BoldOblique.afm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/pdfcorefonts/Helvetica-BoldOblique.afm',
   'DATA'),
  ('matplotlib/mpl-data/fonts/ttf/DejaVuSans-BoldOblique.ttf',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans-BoldOblique.ttf',
   'DATA'),
  ('matplotlib/mpl-data/stylelib/fivethirtyeight.mplstyle',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/stylelib/fivethirtyeight.mplstyle',
   'DATA'),
  ('matplotlib/mpl-data/stylelib/seaborn-v0_8-dark-palette.mplstyle',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/stylelib/seaborn-v0_8-dark-palette.mplstyle',
   'DATA'),
  ('matplotlib/mpl-data/images/filesave-symbolic.svg',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/images/filesave-symbolic.svg',
   'DATA'),
  ('matplotlib/mpl-data/stylelib/classic.mplstyle',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/stylelib/classic.mplstyle',
   'DATA'),
  ('matplotlib/mpl-data/stylelib/seaborn-v0_8-whitegrid.mplstyle',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/stylelib/seaborn-v0_8-whitegrid.mplstyle',
   'DATA'),
  ('matplotlib/mpl-data/fonts/afm/pncr8a.afm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/afm/pncr8a.afm',
   'DATA'),
  ('matplotlib/mpl-data/fonts/ttf/STIXGeneralBol.ttf',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/ttf/STIXGeneralBol.ttf',
   'DATA'),
  ('matplotlib/mpl-data/images/help-symbolic.svg',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/images/help-symbolic.svg',
   'DATA'),
  ('matplotlib/mpl-data/fonts/afm/psyr.afm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/afm/psyr.afm',
   'DATA'),
  ('matplotlib/mpl-data/fonts/afm/pbkd8a.afm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/afm/pbkd8a.afm',
   'DATA'),
  ('matplotlib/mpl-data/fonts/afm/phvb8a.afm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/afm/phvb8a.afm',
   'DATA'),
  ('matplotlib/mpl-data/fonts/ttf/DejaVuSerif.ttf',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSerif.ttf',
   'DATA'),
  ('matplotlib/mpl-data/fonts/ttf/STIXNonUniIta.ttf',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/ttf/STIXNonUniIta.ttf',
   'DATA'),
  ('matplotlib/mpl-data/sample_data/msft.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/sample_data/msft.csv',
   'DATA'),
  ('matplotlib/mpl-data/sample_data/membrane.dat',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/sample_data/membrane.dat',
   'DATA'),
  ('matplotlib/mpl-data/images/move.png',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/images/move.png',
   'DATA'),
  ('matplotlib/mpl-data/stylelib/seaborn-v0_8-notebook.mplstyle',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/stylelib/seaborn-v0_8-notebook.mplstyle',
   'DATA'),
  ('matplotlib/mpl-data/fonts/ttf/STIXSizTwoSymReg.ttf',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/ttf/STIXSizTwoSymReg.ttf',
   'DATA'),
  ('matplotlib/mpl-data/matplotlibrc',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/matplotlibrc',
   'DATA'),
  ('matplotlib/mpl-data/fonts/ttf/STIXNonUni.ttf',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/ttf/STIXNonUni.ttf',
   'DATA'),
  ('matplotlib/mpl-data/images/home-symbolic.svg',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/images/home-symbolic.svg',
   'DATA'),
  ('matplotlib/mpl-data/fonts/afm/pbkli8a.afm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/afm/pbkli8a.afm',
   'DATA'),
  ('matplotlib/mpl-data/fonts/ttf/STIXSizFiveSymReg.ttf',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/ttf/STIXSizFiveSymReg.ttf',
   'DATA'),
  ('matplotlib/mpl-data/fonts/ttf/cmex10.ttf',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/ttf/cmex10.ttf',
   'DATA'),
  ('matplotlib/mpl-data/fonts/afm/ptmbi8a.afm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/afm/ptmbi8a.afm',
   'DATA'),
  ('matplotlib/mpl-data/fonts/afm/putri8a.afm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/afm/putri8a.afm',
   'DATA'),
  ('matplotlib/mpl-data/images/help_large.png',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/images/help_large.png',
   'DATA'),
  ('matplotlib/mpl-data/fonts/ttf/STIXSizThreeSymBol.ttf',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/ttf/STIXSizThreeSymBol.ttf',
   'DATA'),
  ('matplotlib/mpl-data/fonts/pdfcorefonts/Symbol.afm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/pdfcorefonts/Symbol.afm',
   'DATA'),
  ('matplotlib/mpl-data/sample_data/README.txt',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/sample_data/README.txt',
   'DATA'),
  ('matplotlib/mpl-data/fonts/pdfcorefonts/Times-Bold.afm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/pdfcorefonts/Times-Bold.afm',
   'DATA'),
  ('matplotlib/mpl-data/fonts/afm/pagko8a.afm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/afm/pagko8a.afm',
   'DATA'),
  ('matplotlib/mpl-data/images/home.png',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/images/home.png',
   'DATA'),
  ('matplotlib/mpl-data/fonts/ttf/STIXSizFourSymReg.ttf',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/ttf/STIXSizFourSymReg.ttf',
   'DATA'),
  ('matplotlib/mpl-data/fonts/ttf/LICENSE_DEJAVU',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/ttf/LICENSE_DEJAVU',
   'DATA'),
  ('matplotlib/mpl-data/fonts/afm/phvbo8a.afm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/afm/phvbo8a.afm',
   'DATA'),
  ('matplotlib/mpl-data/fonts/afm/pagk8a.afm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/afm/pagk8a.afm',
   'DATA'),
  ('matplotlib/mpl-data/fonts/ttf/DejaVuSerif-Bold.ttf',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSerif-Bold.ttf',
   'DATA'),
  ('matplotlib/mpl-data/fonts/afm/pncbi8a.afm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/afm/pncbi8a.afm',
   'DATA'),
  ('matplotlib/mpl-data/images/home.svg',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/images/home.svg',
   'DATA'),
  ('matplotlib/mpl-data/fonts/afm/phvr8a.afm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/afm/phvr8a.afm',
   'DATA'),
  ('matplotlib/mpl-data/fonts/afm/putb8a.afm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/afm/putb8a.afm',
   'DATA'),
  ('matplotlib/mpl-data/images/subplots.png',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/images/subplots.png',
   'DATA'),
  ('matplotlib/mpl-data/stylelib/bmh.mplstyle',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/stylelib/bmh.mplstyle',
   'DATA'),
  ('matplotlib/mpl-data/sample_data/eeg.dat',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/sample_data/eeg.dat',
   'DATA'),
  ('matplotlib/mpl-data/fonts/afm/ptmr8a.afm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/afm/ptmr8a.afm',
   'DATA'),
  ('matplotlib/mpl-data/fonts/afm/phvl8a.afm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/afm/phvl8a.afm',
   'DATA'),
  ('matplotlib/mpl-data/fonts/afm/pbkdi8a.afm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/afm/pbkdi8a.afm',
   'DATA'),
  ('matplotlib/mpl-data/fonts/ttf/STIXGeneralBolIta.ttf',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/ttf/STIXGeneralBolIta.ttf',
   'DATA'),
  ('matplotlib/mpl-data/stylelib/grayscale.mplstyle',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/stylelib/grayscale.mplstyle',
   'DATA'),
  ('matplotlib/mpl-data/fonts/afm/pzdr.afm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/afm/pzdr.afm',
   'DATA'),
  ('matplotlib/mpl-data/fonts/afm/cmr10.afm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/afm/cmr10.afm',
   'DATA'),
  ('matplotlib/mpl-data/images/hand.png',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/images/hand.png',
   'DATA'),
  ('matplotlib/mpl-data/images/help.pdf',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/images/help.pdf',
   'DATA'),
  ('matplotlib/mpl-data/fonts/ttf/STIXGeneral.ttf',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/ttf/STIXGeneral.ttf',
   'DATA'),
  ('matplotlib/mpl-data/stylelib/seaborn-v0_8-bright.mplstyle',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/stylelib/seaborn-v0_8-bright.mplstyle',
   'DATA'),
  ('matplotlib/mpl-data/sample_data/data_x_x2_x3.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/sample_data/data_x_x2_x3.csv',
   'DATA'),
  ('matplotlib/mpl-data/sample_data/grace_hopper.jpg',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/sample_data/grace_hopper.jpg',
   'DATA'),
  ('matplotlib/mpl-data/images/move.svg',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/images/move.svg',
   'DATA'),
  ('matplotlib/mpl-data/fonts/ttf/DejaVuSansMono.ttf',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSansMono.ttf',
   'DATA'),
  ('matplotlib/mpl-data/images/move_large.png',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/images/move_large.png',
   'DATA'),
  ('matplotlib/mpl-data/images/forward-symbolic.svg',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/images/forward-symbolic.svg',
   'DATA'),
  ('matplotlib/mpl-data/images/qt4_editor_options.svg',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/images/qt4_editor_options.svg',
   'DATA'),
  ('matplotlib/mpl-data/fonts/pdfcorefonts/Times-BoldItalic.afm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/pdfcorefonts/Times-BoldItalic.afm',
   'DATA'),
  ('matplotlib/mpl-data/images/filesave.svg',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/images/filesave.svg',
   'DATA'),
  ('matplotlib/mpl-data/fonts/ttf/STIXNonUniBolIta.ttf',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/ttf/STIXNonUniBolIta.ttf',
   'DATA'),
  ('matplotlib/mpl-data/fonts/ttf/cmr10.ttf',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/ttf/cmr10.ttf',
   'DATA'),
  ('matplotlib/mpl-data/fonts/ttf/DejaVuSansDisplay.ttf',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSansDisplay.ttf',
   'DATA'),
  ('matplotlib/mpl-data/images/qt4_editor_options.pdf',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/images/qt4_editor_options.pdf',
   'DATA'),
  ('matplotlib/mpl-data/fonts/ttf/LICENSE_STIX',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/ttf/LICENSE_STIX',
   'DATA'),
  ('matplotlib/mpl-data/images/matplotlib.svg',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/images/matplotlib.svg',
   'DATA'),
  ('matplotlib/mpl-data/stylelib/_mpl-gallery.mplstyle',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/stylelib/_mpl-gallery.mplstyle',
   'DATA'),
  ('matplotlib/mpl-data/images/filesave_large.png',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/images/filesave_large.png',
   'DATA'),
  ('matplotlib/mpl-data/sample_data/topobathy.npz',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/sample_data/topobathy.npz',
   'DATA'),
  ('matplotlib/mpl-data/stylelib/seaborn-v0_8-deep.mplstyle',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/stylelib/seaborn-v0_8-deep.mplstyle',
   'DATA'),
  ('matplotlib/mpl-data/fonts/ttf/DejaVuSerifDisplay.ttf',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSerifDisplay.ttf',
   'DATA'),
  ('matplotlib/mpl-data/fonts/ttf/DejaVuSerif-Italic.ttf',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSerif-Italic.ttf',
   'DATA'),
  ('matplotlib/mpl-data/fonts/ttf/DejaVuSerif-BoldItalic.ttf',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSerif-BoldItalic.ttf',
   'DATA'),
  ('matplotlib/mpl-data/fonts/afm/pbkl8a.afm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/afm/pbkl8a.afm',
   'DATA'),
  ('matplotlib/mpl-data/fonts/ttf/STIXGeneralItalic.ttf',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/ttf/STIXGeneralItalic.ttf',
   'DATA'),
  ('matplotlib/mpl-data/stylelib/seaborn-v0_8-paper.mplstyle',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/stylelib/seaborn-v0_8-paper.mplstyle',
   'DATA'),
  ('matplotlib/mpl-data/fonts/afm/cmtt10.afm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/afm/cmtt10.afm',
   'DATA'),
  ('matplotlib/mpl-data/fonts/afm/pplbi8a.afm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/afm/pplbi8a.afm',
   'DATA'),
  ('matplotlib/mpl-data/fonts/afm/pcrb8a.afm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/afm/pcrb8a.afm',
   'DATA'),
  ('matplotlib/mpl-data/fonts/afm/putbi8a.afm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/afm/putbi8a.afm',
   'DATA'),
  ('matplotlib/mpl-data/stylelib/seaborn-v0_8-poster.mplstyle',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/stylelib/seaborn-v0_8-poster.mplstyle',
   'DATA'),
  ('matplotlib/mpl-data/stylelib/_mpl-gallery-nogrid.mplstyle',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/stylelib/_mpl-gallery-nogrid.mplstyle',
   'DATA'),
  ('matplotlib/mpl-data/kpsewhich.lua',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/kpsewhich.lua',
   'DATA'),
  ('matplotlib/mpl-data/stylelib/seaborn-v0_8-pastel.mplstyle',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/stylelib/seaborn-v0_8-pastel.mplstyle',
   'DATA'),
  ('matplotlib/mpl-data/fonts/pdfcorefonts/Helvetica-Bold.afm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/pdfcorefonts/Helvetica-Bold.afm',
   'DATA'),
  ('matplotlib/mpl-data/images/subplots_large.png',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/images/subplots_large.png',
   'DATA'),
  ('matplotlib/mpl-data/images/back-symbolic.svg',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/images/back-symbolic.svg',
   'DATA'),
  ('matplotlib/mpl-data/images/qt4_editor_options_large.png',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/images/qt4_editor_options_large.png',
   'DATA'),
  ('matplotlib/mpl-data/fonts/afm/pncb8a.afm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/afm/pncb8a.afm',
   'DATA'),
  ('matplotlib/mpl-data/fonts/ttf/cmb10.ttf',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/ttf/cmb10.ttf',
   'DATA'),
  ('matplotlib/mpl-data/sample_data/logo2.png',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/sample_data/logo2.png',
   'DATA'),
  ('matplotlib/mpl-data/images/zoom_to_rect.svg',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/images/zoom_to_rect.svg',
   'DATA'),
  ('matplotlib/mpl-data/stylelib/dark_background.mplstyle',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/stylelib/dark_background.mplstyle',
   'DATA'),
  ('matplotlib/mpl-data/fonts/afm/phvr8an.afm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/afm/phvr8an.afm',
   'DATA'),
  ('matplotlib/mpl-data/fonts/afm/phvb8an.afm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/afm/phvb8an.afm',
   'DATA'),
  ('matplotlib/mpl-data/fonts/ttf/cmtt10.ttf',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/ttf/cmtt10.ttf',
   'DATA'),
  ('matplotlib/mpl-data/images/zoom_to_rect_large.png',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/images/zoom_to_rect_large.png',
   'DATA'),
  ('matplotlib/mpl-data/fonts/afm/phvlo8a.afm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/afm/phvlo8a.afm',
   'DATA'),
  ('matplotlib/mpl-data/fonts/afm/pplb8a.afm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/afm/pplb8a.afm',
   'DATA'),
  ('matplotlib/mpl-data/fonts/afm/ptmri8a.afm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/afm/ptmri8a.afm',
   'DATA'),
  ('matplotlib/mpl-data/sample_data/jacksboro_fault_dem.npz',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/sample_data/jacksboro_fault_dem.npz',
   'DATA'),
  ('matplotlib/mpl-data/fonts/afm/pcrbo8a.afm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/afm/pcrbo8a.afm',
   'DATA'),
  ('matplotlib/mpl-data/stylelib/seaborn-v0_8.mplstyle',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/stylelib/seaborn-v0_8.mplstyle',
   'DATA'),
  ('matplotlib/mpl-data/fonts/ttf/DejaVuSansMono-Bold.ttf',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSansMono-Bold.ttf',
   'DATA'),
  ('matplotlib/mpl-data/fonts/afm/cmsy10.afm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/afm/cmsy10.afm',
   'DATA'),
  ('matplotlib/mpl-data/fonts/afm/pzcmi8a.afm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/afm/pzcmi8a.afm',
   'DATA'),
  ('matplotlib/mpl-data/fonts/ttf/STIXSizTwoSymBol.ttf',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/ttf/STIXSizTwoSymBol.ttf',
   'DATA'),
  ('matplotlib/mpl-data/stylelib/tableau-colorblind10.mplstyle',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/stylelib/tableau-colorblind10.mplstyle',
   'DATA'),
  ('matplotlib/mpl-data/fonts/ttf/STIXNonUniBol.ttf',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/ttf/STIXNonUniBol.ttf',
   'DATA'),
  ('matplotlib/mpl-data/images/zoom_to_rect.png',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/images/zoom_to_rect.png',
   'DATA'),
  ('matplotlib/mpl-data/images/hand.pdf',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/images/hand.pdf',
   'DATA'),
  ('matplotlib/mpl-data/stylelib/seaborn-v0_8-talk.mplstyle',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/stylelib/seaborn-v0_8-talk.mplstyle',
   'DATA'),
  ('matplotlib/mpl-data/fonts/ttf/cmsy10.ttf',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/ttf/cmsy10.ttf',
   'DATA'),
  ('matplotlib/mpl-data/fonts/afm/pncri8a.afm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/afm/pncri8a.afm',
   'DATA'),
  ('matplotlib/mpl-data/fonts/pdfcorefonts/Times-Italic.afm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/pdfcorefonts/Times-Italic.afm',
   'DATA'),
  ('matplotlib/mpl-data/fonts/ttf/STIXSizThreeSymReg.ttf',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/ttf/STIXSizThreeSymReg.ttf',
   'DATA'),
  ('matplotlib/mpl-data/fonts/afm/cmmi10.afm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/afm/cmmi10.afm',
   'DATA'),
  ('matplotlib/mpl-data/fonts/ttf/cmmi10.ttf',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/ttf/cmmi10.ttf',
   'DATA'),
  ('matplotlib/mpl-data/fonts/afm/pcrr8a.afm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/afm/pcrr8a.afm',
   'DATA'),
  ('matplotlib/mpl-data/images/move-symbolic.svg',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/images/move-symbolic.svg',
   'DATA'),
  ('matplotlib/mpl-data/images/help.png',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/matplotlib/mpl-data/images/help.png',
   'DATA'),
  ('certifi/cacert.pem',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/certifi/cacert.pem',
   'DATA'),
  ('certifi/py.typed',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/certifi/py.typed',
   'DATA'),
  ('_soundfile_data/COPYING',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/_soundfile_data/COPYING',
   'DATA'),
  ('cryptography-42.0.5.dist-info/RECORD',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/cryptography-42.0.5.dist-info/RECORD',
   'DATA'),
  ('cryptography-42.0.5.dist-info/top_level.txt',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/cryptography-42.0.5.dist-info/top_level.txt',
   'DATA'),
  ('cryptography-42.0.5.dist-info/REQUESTED',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/cryptography-42.0.5.dist-info/REQUESTED',
   'DATA'),
  ('cryptography-42.0.5.dist-info/LICENSE.APACHE',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/cryptography-42.0.5.dist-info/LICENSE.APACHE',
   'DATA'),
  ('cryptography-42.0.5.dist-info/direct_url.json',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/cryptography-42.0.5.dist-info/direct_url.json',
   'DATA'),
  ('cryptography-42.0.5.dist-info/LICENSE',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/cryptography-42.0.5.dist-info/LICENSE',
   'DATA'),
  ('cryptography-42.0.5.dist-info/METADATA',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/cryptography-42.0.5.dist-info/METADATA',
   'DATA'),
  ('cryptography-42.0.5.dist-info/INSTALLER',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/cryptography-42.0.5.dist-info/INSTALLER',
   'DATA'),
  ('cryptography-42.0.5.dist-info/LICENSE.BSD',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/cryptography-42.0.5.dist-info/LICENSE.BSD',
   'DATA'),
  ('cryptography-42.0.5.dist-info/WHEEL',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/cryptography-42.0.5.dist-info/WHEEL',
   'DATA'),
  ('PyQt6/uic/widget-plugins/qtwebenginewidgets.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/uic/widget-plugins/qtwebenginewidgets.py',
   'DATA'),
  ('PyQt6/uic/widget-plugins/qtprintsupport.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/uic/widget-plugins/qtprintsupport.py',
   'DATA'),
  ('PyQt6/uic/widget-plugins/qtquickwidgets.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/uic/widget-plugins/qtquickwidgets.py',
   'DATA'),
  ('PyQt6/uic/widget-plugins/qaxcontainer.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/uic/widget-plugins/qaxcontainer.py',
   'DATA'),
  ('PyQt6/uic/widget-plugins/qtcharts.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/uic/widget-plugins/qtcharts.py',
   'DATA'),
  ('PyQt6/uic/widget-plugins/qtopenglwidgets.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/uic/widget-plugins/qtopenglwidgets.py',
   'DATA'),
  ('PyQt6/uic/widget-plugins/qscintilla.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/uic/widget-plugins/qscintilla.py',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_ar.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_ar.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_it.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_it.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_bg.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_bg.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_zh_TW.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_ja.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_ja.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_hu.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_hu.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_ja.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_ja.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_hr.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_hr.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_pl.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_pl.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_gd.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_gd.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_hu.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_hu.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_da.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_da.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_sl.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_sl.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_ar.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_ar.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_nn.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_nn.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_ko.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_ko.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_sv.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_sv.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_ru.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_ru.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_zh_CN.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_zh_CN.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_fi.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_fi.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_it.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_it.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_tr.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_tr.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_lv.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_lv.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_gd.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_gd.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_ru.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_ru.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_tr.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_tr.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_ru.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_ru.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_pt_PT.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_pt_PT.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_sl.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_sl.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_uk.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_uk.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_uk.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_uk.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_en.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_en.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_zh_TW.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_sk.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_sk.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_de.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_de.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_bg.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_bg.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_sk.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_sk.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_es.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_es.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_cs.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_cs.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_uk.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_uk.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_lv.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_lv.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_fr.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_fr.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_ca.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_ca.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_ko.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_ko.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_ar.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_ar.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_lt.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_lt.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_zh_CN.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_fr.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_fr.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_nl.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_nl.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_da.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_da.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_ka.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_ka.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_zh_CN.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_zh_CN.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_pt_BR.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_pt_BR.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_ka.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_ka.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_sk.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_sk.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_ja.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_ja.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_pt_BR.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_pt_BR.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_gl.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_gl.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_cs.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_cs.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_fa.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_fa.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_it.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_it.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_fr.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_fr.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_ca.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_ca.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_hr.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_hr.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_ka.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_ka.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_pt_BR.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_pt_BR.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_fa.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_fa.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_ko.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_ko.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_bg.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_bg.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_nn.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_nn.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_he.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_he.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_gl.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_gl.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_zh_TW.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_zh_TW.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_en.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_en.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_hu.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_hu.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_ca.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_ca.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_hr.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_hr.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_tr.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_tr.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_nn.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_nn.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_da.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_da.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_nl.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_nl.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_es.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_es.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_pl.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_pl.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_fi.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_fi.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_es.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_es.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_en.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_en.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_de.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_de.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_de.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_de.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_pl.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_pl.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_he.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_he.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_cs.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_cs.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_nl.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_nl.qm',
   'DATA'),
  ('wheel-0.43.0.dist-info/METADATA',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/wheel-0.43.0.dist-info/METADATA',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info/INSTALLER',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/MarkupSafe-3.0.2.dist-info/INSTALLER',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info/LICENSE.txt',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/MarkupSafe-3.0.2.dist-info/LICENSE.txt',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info/top_level.txt',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/MarkupSafe-3.0.2.dist-info/top_level.txt',
   'DATA'),
  ('wheel-0.43.0.dist-info/LICENSE.txt',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/wheel-0.43.0.dist-info/LICENSE.txt',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info/RECORD',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/MarkupSafe-3.0.2.dist-info/RECORD',
   'DATA'),
  ('wheel-0.43.0.dist-info/WHEEL',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/wheel-0.43.0.dist-info/WHEEL',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info/WHEEL',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/MarkupSafe-3.0.2.dist-info/WHEEL',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info/METADATA',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/MarkupSafe-3.0.2.dist-info/METADATA',
   'DATA'),
  ('wheel-0.43.0.dist-info/entry_points.txt',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/wheel-0.43.0.dist-info/entry_points.txt',
   'DATA'),
  ('wheel-0.43.0.dist-info/RECORD',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/wheel-0.43.0.dist-info/RECORD',
   'DATA'),
  ('torch/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/__init__.py',
   'DATA'),
  ('torch/xpu/streams.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/xpu/streams.py',
   'DATA'),
  ('torch/xpu/random.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/xpu/random.py',
   'DATA'),
  ('torch/xpu/memory.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/xpu/memory.py',
   'DATA'),
  ('torch/xpu/_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/xpu/_utils.py',
   'DATA'),
  ('torch/xpu/_gpu_trace.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/xpu/_gpu_trace.py',
   'DATA'),
  ('torch/utils/weak.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/weak.py',
   'DATA'),
  ('torch/utils/viz/_cycles.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/viz/_cycles.py',
   'DATA'),
  ('torch/utils/viz/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/viz/__init__.py',
   'DATA'),
  ('torch/utils/throughput_benchmark.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/throughput_benchmark.py',
   'DATA'),
  ('torch/utils/show_pickle.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/show_pickle.py',
   'DATA'),
  ('torch/utils/module_tracker.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/module_tracker.py',
   'DATA'),
  ('torch/utils/model_zoo.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/model_zoo.py',
   'DATA'),
  ('torch/utils/model_dump/__main__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/model_dump/__main__.py',
   'DATA'),
  ('torch/utils/model_dump/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/model_dump/__init__.py',
   'DATA'),
  ('torch/utils/mobile_optimizer.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/mobile_optimizer.py',
   'DATA'),
  ('torch/utils/mkldnn.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/mkldnn.py',
   'DATA'),
  ('torch/utils/jit/log_extract.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/jit/log_extract.py',
   'DATA'),
  ('torch/utils/jit/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/jit/__init__.py',
   'DATA'),
  ('torch/utils/hooks.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/hooks.py',
   'DATA'),
  ('torch/utils/hipify/version.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/hipify/version.py',
   'DATA'),
  ('torch/utils/hipify/hipify_python.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/hipify/hipify_python.py',
   'DATA'),
  ('torch/utils/hipify/cuda_to_hip_mappings.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/hipify/cuda_to_hip_mappings.py',
   'DATA'),
  ('torch/utils/hipify/constants.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/hipify/constants.py',
   'DATA'),
  ('torch/utils/hipify/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/hipify/__init__.py',
   'DATA'),
  ('torch/utils/flop_counter.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/flop_counter.py',
   'DATA'),
  ('torch/utils/file_baton.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/file_baton.py',
   'DATA'),
  ('torch/utils/deterministic.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/deterministic.py',
   'DATA'),
  ('torch/utils/data/sampler.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/data/sampler.py',
   'DATA'),
  ('torch/utils/data/graph_settings.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/data/graph_settings.py',
   'DATA'),
  ('torch/utils/data/graph.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/data/graph.py',
   'DATA'),
  ('torch/utils/data/distributed.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/data/distributed.py',
   'DATA'),
  ('torch/utils/data/dataset.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/data/dataset.py',
   'DATA'),
  ('torch/utils/data/datapipes/utils/snapshot.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/data/datapipes/utils/snapshot.py',
   'DATA'),
  ('torch/utils/data/datapipes/utils/decoder.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/data/datapipes/utils/decoder.py',
   'DATA'),
  ('torchaudio/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/__init__.py',
   'DATA'),
  ('torchaudio/lib/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/lib/__init__.py',
   'DATA'),
  ('torchaudio/version.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/version.py',
   'DATA'),
  ('torchaudio/backend/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/backend/__init__.py',
   'DATA'),
  ('torchaudio/backend/sox_io_backend.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/backend/sox_io_backend.py',
   'DATA'),
  ('torchaudio/backend/_sox_io_backend.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/backend/_sox_io_backend.py',
   'DATA'),
  ('torchaudio/backend/soundfile_backend.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/backend/soundfile_backend.py',
   'DATA'),
  ('torchaudio/_backend/soundfile_backend.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/_backend/soundfile_backend.py',
   'DATA'),
  ('torchaudio/_backend/common.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/_backend/common.py',
   'DATA'),
  ('torchaudio/_internal/module_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/_internal/module_utils.py',
   'DATA'),
  ('torchaudio/_internal/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/_internal/__init__.py',
   'DATA'),
  ('torchaudio/backend/no_backend.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/backend/no_backend.py',
   'DATA'),
  ('torchaudio/backend/_no_backend.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/backend/_no_backend.py',
   'DATA'),
  ('torchaudio/backend/common.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/backend/common.py',
   'DATA'),
  ('torchaudio/utils/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/utils/__init__.py',
   'DATA'),
  ('torchaudio/utils/download.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/utils/download.py',
   'DATA'),
  ('torchaudio/utils/sox_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/utils/sox_utils.py',
   'DATA'),
  ('torchaudio/transforms/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/transforms/__init__.py',
   'DATA'),
  ('torchaudio/transforms/_transforms.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/transforms/_transforms.py',
   'DATA'),
  ('torchaudio/functional/functional.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/functional/functional.py',
   'DATA'),
  ('torchaudio/functional/filtering.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/functional/filtering.py',
   'DATA'),
  ('torchaudio/transforms/_multi_channel.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/transforms/_multi_channel.py',
   'DATA'),
  ('torchaudio/sox_effects/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/sox_effects/__init__.py',
   'DATA'),
  ('torchaudio/sox_effects/sox_effects.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/sox_effects/sox_effects.py',
   'DATA'),
  ('torchaudio/pipelines/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/pipelines/__init__.py',
   'DATA'),
  ('torchaudio/pipelines/rnnt_pipeline.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/pipelines/rnnt_pipeline.py',
   'DATA'),
  ('torchaudio/pipelines/_wav2vec2/impl.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/pipelines/_wav2vec2/impl.py',
   'DATA'),
  ('torchaudio/pipelines/_wav2vec2/utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/pipelines/_wav2vec2/utils.py',
   'DATA'),
  ('torchaudio/pipelines/_wav2vec2/aligner.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/pipelines/_wav2vec2/aligner.py',
   'DATA'),
  ('torchaudio/pipelines/_wav2vec2/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/pipelines/_wav2vec2/__init__.py',
   'DATA'),
  ('torchaudio/pipelines/_tts/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/pipelines/_tts/__init__.py',
   'DATA'),
  ('torchaudio/pipelines/_tts/interface.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/pipelines/_tts/interface.py',
   'DATA'),
  ('torchaudio/pipelines/_tts/impl.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/pipelines/_tts/impl.py',
   'DATA'),
  ('torchaudio/pipelines/_tts/utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/pipelines/_tts/utils.py',
   'DATA'),
  ('torchaudio/pipelines/_squim_pipeline.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/pipelines/_squim_pipeline.py',
   'DATA'),
  ('torchaudio/pipelines/_source_separation_pipeline.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/pipelines/_source_separation_pipeline.py',
   'DATA'),
  ('torchaudio/models/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/models/__init__.py',
   'DATA'),
  ('torchaudio/models/wavernn.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/models/wavernn.py',
   'DATA'),
  ('torchaudio/models/wav2vec2/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/models/wav2vec2/__init__.py',
   'DATA'),
  ('torchaudio/models/wav2vec2/model.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/models/wav2vec2/model.py',
   'DATA'),
  ('torchaudio/models/wav2vec2/utils/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/models/wav2vec2/utils/__init__.py',
   'DATA'),
  ('torchaudio/models/wav2vec2/utils/import_huggingface.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/models/wav2vec2/utils/import_huggingface.py',
   'DATA'),
  ('torchaudio/models/wav2vec2/utils/import_fairseq.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/models/wav2vec2/utils/import_fairseq.py',
   'DATA'),
  ('torchaudio/models/wav2vec2/components.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/models/wav2vec2/components.py',
   'DATA'),
  ('torchaudio/models/wav2vec2/wavlm_attention.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/models/wav2vec2/wavlm_attention.py',
   'DATA'),
  ('torchaudio/models/wav2letter.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/models/wav2letter.py',
   'DATA'),
  ('torchaudio/models/tacotron2.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/models/tacotron2.py',
   'DATA'),
  ('torchaudio/models/squim/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/models/squim/__init__.py',
   'DATA'),
  ('torchaudio/models/squim/subjective.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/models/squim/subjective.py',
   'DATA'),
  ('torchaudio/models/squim/objective.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/models/squim/objective.py',
   'DATA'),
  ('torchaudio/models/rnnt_decoder.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/models/rnnt_decoder.py',
   'DATA'),
  ('torchaudio/models/rnnt.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/models/rnnt.py',
   'DATA'),
  ('torchaudio/models/emformer.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/models/emformer.py',
   'DATA'),
  ('torchaudio/models/deepspeech.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/models/deepspeech.py',
   'DATA'),
  ('torchaudio/models/conv_tasnet.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/models/conv_tasnet.py',
   'DATA'),
  ('torchaudio/models/conformer.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/models/conformer.py',
   'DATA'),
  ('torchaudio/models/_hdemucs.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/models/_hdemucs.py',
   'DATA'),
  ('torchaudio/kaldi_io.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/kaldi_io.py',
   'DATA'),
  ('torchaudio/io/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/io/__init__.py',
   'DATA'),
  ('torchaudio/io/_playback.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/io/_playback.py',
   'DATA'),
  ('torchaudio/io/_effector.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/io/_effector.py',
   'DATA'),
  ('torchaudio/functional/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/functional/__init__.py',
   'DATA'),
  ('torchaudio/functional/_alignment.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/functional/_alignment.py',
   'DATA'),
  ('torchaudio/datasets/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/datasets/__init__.py',
   'DATA'),
  ('torchaudio/datasets/yesno.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/datasets/yesno.py',
   'DATA'),
  ('torchaudio/datasets/utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/datasets/utils.py',
   'DATA'),
  ('torchaudio/datasets/voxceleb1.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/datasets/voxceleb1.py',
   'DATA'),
  ('torchaudio/datasets/vctk.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/datasets/vctk.py',
   'DATA'),
  ('torchaudio/datasets/tedlium.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/datasets/tedlium.py',
   'DATA'),
  ('torchaudio/datasets/speechcommands.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/datasets/speechcommands.py',
   'DATA'),
  ('torchaudio/datasets/snips.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/datasets/snips.py',
   'DATA'),
  ('torchaudio/datasets/quesst14.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/datasets/quesst14.py',
   'DATA'),
  ('torchaudio/datasets/musdb_hq.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/datasets/musdb_hq.py',
   'DATA'),
  ('torchaudio/datasets/ljspeech.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/datasets/ljspeech.py',
   'DATA'),
  ('torchaudio/datasets/libritts.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/datasets/libritts.py',
   'DATA'),
  ('torchaudio/datasets/librispeech_biasing.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/datasets/librispeech_biasing.py',
   'DATA'),
  ('torchaudio/datasets/librispeech.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/datasets/librispeech.py',
   'DATA'),
  ('torchaudio/datasets/librimix.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/datasets/librimix.py',
   'DATA'),
  ('torchaudio/datasets/librilight_limited.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/datasets/librilight_limited.py',
   'DATA'),
  ('torchaudio/datasets/iemocap.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/datasets/iemocap.py',
   'DATA'),
  ('torchaudio/datasets/gtzan.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/datasets/gtzan.py',
   'DATA'),
  ('torchaudio/datasets/fluentcommands.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/datasets/fluentcommands.py',
   'DATA'),
  ('torchaudio/datasets/dr_vctk.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/datasets/dr_vctk.py',
   'DATA'),
  ('torchaudio/datasets/commonvoice.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/datasets/commonvoice.py',
   'DATA'),
  ('torchaudio/datasets/cmudict.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/datasets/cmudict.py',
   'DATA'),
  ('torchaudio/datasets/cmuarctic.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/datasets/cmuarctic.py',
   'DATA'),
  ('torchaudio/compliance/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/compliance/__init__.py',
   'DATA'),
  ('torchaudio/compliance/kaldi.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/compliance/kaldi.py',
   'DATA'),
  ('torchaudio/_backend/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/_backend/__init__.py',
   'DATA'),
  ('torchaudio/_backend/utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/_backend/utils.py',
   'DATA'),
  ('torchaudio/_backend/sox.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/_backend/sox.py',
   'DATA'),
  ('torchaudio/_backend/soundfile.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/_backend/soundfile.py',
   'DATA'),
  ('torchaudio/_backend/ffmpeg.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/_backend/ffmpeg.py',
   'DATA'),
  ('torchaudio/_backend/backend.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/_backend/backend.py',
   'DATA'),
  ('torchaudio/_extension/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/_extension/__init__.py',
   'DATA'),
  ('torchaudio/_extension/utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torchaudio/_extension/utils.py',
   'DATA'),
  ('torch/utils/data/datapipes/utils/common.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/data/datapipes/utils/common.py',
   'DATA'),
  ('torch/utils/data/datapipes/utils/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/data/datapipes/utils/__init__.py',
   'DATA'),
  ('torch/utils/data/datapipes/map/utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/data/datapipes/map/utils.py',
   'DATA'),
  ('torch/utils/data/datapipes/map/grouping.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/data/datapipes/map/grouping.py',
   'DATA'),
  ('torch/utils/data/datapipes/map/combining.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/data/datapipes/map/combining.py',
   'DATA'),
  ('torch/utils/data/datapipes/map/combinatorics.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/data/datapipes/map/combinatorics.py',
   'DATA'),
  ('torch/utils/data/datapipes/map/callable.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/data/datapipes/map/callable.py',
   'DATA'),
  ('torch/utils/data/datapipes/map/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/data/datapipes/map/__init__.py',
   'DATA'),
  ('torch/utils/data/datapipes/iter/utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/data/datapipes/iter/utils.py',
   'DATA'),
  ('torch/utils/data/datapipes/iter/streamreader.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/data/datapipes/iter/streamreader.py',
   'DATA'),
  ('torch/utils/data/datapipes/iter/sharding.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/data/datapipes/iter/sharding.py',
   'DATA'),
  ('torch/utils/data/datapipes/iter/selecting.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/data/datapipes/iter/selecting.py',
   'DATA'),
  ('torch/utils/data/datapipes/iter/routeddecoder.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/data/datapipes/iter/routeddecoder.py',
   'DATA'),
  ('torch/utils/data/datapipes/iter/grouping.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/data/datapipes/iter/grouping.py',
   'DATA'),
  ('torch/utils/data/datapipes/iter/fileopener.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/data/datapipes/iter/fileopener.py',
   'DATA'),
  ('torch/utils/data/datapipes/iter/filelister.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/data/datapipes/iter/filelister.py',
   'DATA'),
  ('torch/utils/data/datapipes/iter/combining.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/data/datapipes/iter/combining.py',
   'DATA'),
  ('torch/utils/data/datapipes/iter/combinatorics.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/data/datapipes/iter/combinatorics.py',
   'DATA'),
  ('torch/utils/data/datapipes/iter/callable.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/data/datapipes/iter/callable.py',
   'DATA'),
  ('torch/utils/data/datapipes/iter/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/data/datapipes/iter/__init__.py',
   'DATA'),
  ('torch/utils/data/datapipes/gen_pyi.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/data/datapipes/gen_pyi.py',
   'DATA'),
  ('torch/utils/data/datapipes/datapipe.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/data/datapipes/datapipe.py',
   'DATA'),
  ('torch/utils/data/datapipes/dataframe/structures.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/data/datapipes/dataframe/structures.py',
   'DATA'),
  ('torch/utils/data/datapipes/dataframe/datapipes.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/data/datapipes/dataframe/datapipes.py',
   'DATA'),
  ('torch/utils/data/datapipes/dataframe/dataframes.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/data/datapipes/dataframe/dataframes.py',
   'DATA'),
  ('torch/utils/data/datapipes/dataframe/dataframe_wrapper.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/data/datapipes/dataframe/dataframe_wrapper.py',
   'DATA'),
  ('torch/utils/data/datapipes/dataframe/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/data/datapipes/dataframe/__init__.py',
   'DATA'),
  ('torch/utils/data/datapipes/_typing.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/data/datapipes/_typing.py',
   'DATA'),
  ('torch/utils/data/datapipes/_hook_iterator.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/data/datapipes/_hook_iterator.py',
   'DATA'),
  ('torch/utils/data/datapipes/_decorator.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/data/datapipes/_decorator.py',
   'DATA'),
  ('torch/utils/data/datapipes/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/data/datapipes/__init__.py',
   'DATA'),
  ('torch/utils/data/dataloader.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/data/dataloader.py',
   'DATA'),
  ('torch/utils/data/backward_compatibility.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/data/backward_compatibility.py',
   'DATA'),
  ('torch/utils/data/_utils/worker.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/data/_utils/worker.py',
   'DATA'),
  ('torch/utils/data/_utils/signal_handling.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/data/_utils/signal_handling.py',
   'DATA'),
  ('torch/utils/data/_utils/pin_memory.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/data/_utils/pin_memory.py',
   'DATA'),
  ('torch/utils/data/_utils/fetch.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/data/_utils/fetch.py',
   'DATA'),
  ('torch/utils/data/_utils/collate.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/data/_utils/collate.py',
   'DATA'),
  ('torch/utils/data/_utils/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/data/_utils/__init__.py',
   'DATA'),
  ('torch/utils/data/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/data/__init__.py',
   'DATA'),
  ('torch/utils/cpp_extension.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/cpp_extension.py',
   'DATA'),
  ('torch/utils/cpp_backtrace.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/cpp_backtrace.py',
   'DATA'),
  ('torch/utils/collect_env.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/collect_env.py',
   'DATA'),
  ('torch/utils/checkpoint.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/checkpoint.py',
   'DATA'),
  ('torch/utils/bundled_inputs.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/bundled_inputs.py',
   'DATA'),
  ('torch/utils/bottleneck/__main__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/bottleneck/__main__.py',
   'DATA'),
  ('torch/utils/bottleneck/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/bottleneck/__init__.py',
   'DATA'),
  ('torch/utils/benchmark/utils/valgrind_wrapper/timer_interface.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/benchmark/utils/valgrind_wrapper/timer_interface.py',
   'DATA'),
  ('torch/utils/benchmark/utils/valgrind_wrapper/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/benchmark/utils/valgrind_wrapper/__init__.py',
   'DATA'),
  ('torch/utils/benchmark/utils/timer.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/benchmark/utils/timer.py',
   'DATA'),
  ('torch/utils/benchmark/utils/sparse_fuzzer.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/benchmark/utils/sparse_fuzzer.py',
   'DATA'),
  ('torch/utils/benchmark/utils/fuzzer.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/benchmark/utils/fuzzer.py',
   'DATA'),
  ('torch/utils/benchmark/utils/cpp_jit.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/benchmark/utils/cpp_jit.py',
   'DATA'),
  ('torch/utils/benchmark/utils/compile.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/benchmark/utils/compile.py',
   'DATA'),
  ('torch/utils/benchmark/utils/compare.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/benchmark/utils/compare.py',
   'DATA'),
  ('torch/utils/benchmark/utils/common.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/benchmark/utils/common.py',
   'DATA'),
  ('torch/utils/benchmark/utils/_stubs.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/benchmark/utils/_stubs.py',
   'DATA'),
  ('torch/utils/benchmark/utils/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/benchmark/utils/__init__.py',
   'DATA'),
  ('torch/utils/benchmark/op_fuzzers/unary.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/benchmark/op_fuzzers/unary.py',
   'DATA'),
  ('torch/utils/benchmark/op_fuzzers/spectral.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/benchmark/op_fuzzers/spectral.py',
   'DATA'),
  ('torch/utils/benchmark/op_fuzzers/sparse_unary.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/benchmark/op_fuzzers/sparse_unary.py',
   'DATA'),
  ('torch/utils/benchmark/op_fuzzers/sparse_binary.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/benchmark/op_fuzzers/sparse_binary.py',
   'DATA'),
  ('torch/utils/benchmark/op_fuzzers/binary.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/benchmark/op_fuzzers/binary.py',
   'DATA'),
  ('torch/utils/benchmark/op_fuzzers/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/benchmark/op_fuzzers/__init__.py',
   'DATA'),
  ('torch/utils/benchmark/examples/spectral_ops_fuzz_test.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/benchmark/examples/spectral_ops_fuzz_test.py',
   'DATA'),
  ('torch/utils/benchmark/examples/simple_timeit.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/benchmark/examples/simple_timeit.py',
   'DATA'),
  ('torch/utils/benchmark/examples/op_benchmark.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/benchmark/examples/op_benchmark.py',
   'DATA'),
  ('torch/utils/benchmark/examples/fuzzer.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/benchmark/examples/fuzzer.py',
   'DATA'),
  ('torch/utils/benchmark/examples/compare.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/benchmark/examples/compare.py',
   'DATA'),
  ('torch/utils/benchmark/examples/blas_compare_setup.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/benchmark/examples/blas_compare_setup.py',
   'DATA'),
  ('torch/utils/benchmark/examples/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/benchmark/examples/__init__.py',
   'DATA'),
  ('torch/utils/benchmark/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/benchmark/__init__.py',
   'DATA'),
  ('torch/utils/backend_registration.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/backend_registration.py',
   'DATA'),
  ('torch/utils/backcompat/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/backcompat/__init__.py',
   'DATA'),
  ('torch/utils/_zip.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/_zip.py',
   'DATA'),
  ('torch/utils/_typing_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/_typing_utils.py',
   'DATA'),
  ('torch/utils/_triton.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/_triton.py',
   'DATA'),
  ('torch/utils/_traceback.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/_traceback.py',
   'DATA'),
  ('torch/utils/_thunk.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/_thunk.py',
   'DATA'),
  ('torch/utils/_sympy/value_ranges.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/_sympy/value_ranges.py',
   'DATA'),
  ('torch/utils/_sympy/symbol.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/_sympy/symbol.py',
   'DATA'),
  ('torch/utils/_sympy/solve.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/_sympy/solve.py',
   'DATA'),
  ('torch/utils/_sympy/singleton_int.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/_sympy/singleton_int.py',
   'DATA'),
  ('torch/utils/_sympy/reference.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/_sympy/reference.py',
   'DATA'),
  ('torch/utils/_sympy/numbers.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/_sympy/numbers.py',
   'DATA'),
  ('torch/utils/_sympy/interp.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/_sympy/interp.py',
   'DATA'),
  ('torch/utils/_sympy/functions.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/_sympy/functions.py',
   'DATA'),
  ('torch/utils/_sympy/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/_sympy/__init__.py',
   'DATA'),
  ('torch/utils/_strobelight/cli_function_profiler.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/_strobelight/cli_function_profiler.py',
   'DATA'),
  ('torch/utils/_strobelight/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/_strobelight/__init__.py',
   'DATA'),
  ('torch/utils/_stats.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/_stats.py',
   'DATA'),
  ('torch/utils/_pytree.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/_pytree.py',
   'DATA'),
  ('torch/utils/_python_dispatch.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/_python_dispatch.py',
   'DATA'),
  ('torch/utils/_ordered_set.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/_ordered_set.py',
   'DATA'),
  ('torch/utils/_mode_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/_mode_utils.py',
   'DATA'),
  ('torch/utils/_import_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/_import_utils.py',
   'DATA'),
  ('torch/utils/_get_clean_triton.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/_get_clean_triton.py',
   'DATA'),
  ('torch/utils/_freeze.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/_freeze.py',
   'DATA'),
  ('torch/utils/_foreach_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/_foreach_utils.py',
   'DATA'),
  ('torch/utils/_exposed_in.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/_exposed_in.py',
   'DATA'),
  ('torch/utils/_cxx_pytree.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/_cxx_pytree.py',
   'DATA'),
  ('torch/utils/_cpp_extension_versioner.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/_cpp_extension_versioner.py',
   'DATA'),
  ('torch/utils/_contextlib.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/_contextlib.py',
   'DATA'),
  ('torch/utils/_content_store.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/_content_store.py',
   'DATA'),
  ('torch/utils/_config_module.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/_config_module.py',
   'DATA'),
  ('torch/utils/_backport_slots.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/_backport_slots.py',
   'DATA'),
  ('torch/testing/_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_utils.py',
   'DATA'),
  ('torch/testing/_internal/two_tensor.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/two_tensor.py',
   'DATA'),
  ('torch/testing/_internal/triton_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/triton_utils.py',
   'DATA'),
  ('torch/testing/_internal/torchbind_impls.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/torchbind_impls.py',
   'DATA'),
  ('torch/testing/_internal/test_module/no_future_div.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/test_module/no_future_div.py',
   'DATA'),
  ('torch/testing/_internal/test_module/future_div.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/test_module/future_div.py',
   'DATA'),
  ('torch/testing/_internal/test_module/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/test_module/__init__.py',
   'DATA'),
  ('torch/testing/_internal/static_module.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/static_module.py',
   'DATA'),
  ('torch/testing/_internal/quantization_torch_package_models.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/quantization_torch_package_models.py',
   'DATA'),
  ('torch/testing/_internal/optests/make_fx.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/optests/make_fx.py',
   'DATA'),
  ('torch/testing/_internal/optests/generate_tests.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/optests/generate_tests.py',
   'DATA'),
  ('torch/testing/_internal/optests/fake_tensor.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/optests/fake_tensor.py',
   'DATA'),
  ('torch/testing/_internal/optests/autograd_registration.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/optests/autograd_registration.py',
   'DATA'),
  ('torch/testing/_internal/optests/aot_autograd.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/optests/aot_autograd.py',
   'DATA'),
  ('torch/testing/_internal/optests/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/optests/__init__.py',
   'DATA'),
  ('torch/testing/_internal/logging_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/logging_utils.py',
   'DATA'),
  ('torch/testing/_internal/logging_tensor.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/logging_tensor.py',
   'DATA'),
  ('torch/testing/_internal/jit_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/jit_utils.py',
   'DATA'),
  ('torch/testing/_internal/jit_metaprogramming_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/jit_metaprogramming_utils.py',
   'DATA'),
  ('torch/testing/_internal/inductor_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/inductor_utils.py',
   'DATA'),
  ('torch/testing/_internal/hypothesis_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/hypothesis_utils.py',
   'DATA'),
  ('torch/testing/_internal/hop_db.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/hop_db.py',
   'DATA'),
  ('torch/testing/_internal/opinfo/core.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/opinfo/core.py',
   'DATA'),
  ('torch/testing/_internal/opinfo/utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/opinfo/utils.py',
   'DATA'),
  ('torch/testing/_internal/opinfo/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/opinfo/__init__.py',
   'DATA'),
  ('torch/testing/_internal/opinfo/definitions/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/opinfo/definitions/__init__.py',
   'DATA'),
  ('torch/testing/_internal/opinfo/definitions/special.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/opinfo/definitions/special.py',
   'DATA'),
  ('torch/testing/_internal/opinfo/refs.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/opinfo/refs.py',
   'DATA'),
  ('torch/testing/_internal/opinfo/definitions/signal.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/opinfo/definitions/signal.py',
   'DATA'),
  ('torch/testing/_internal/opinfo/definitions/linalg.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/opinfo/definitions/linalg.py',
   'DATA'),
  ('torch/testing/_internal/opinfo/definitions/fft.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/opinfo/definitions/fft.py',
   'DATA'),
  ('torch/testing/_internal/opinfo/definitions/_masked.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/opinfo/definitions/_masked.py',
   'DATA'),
  ('torch/testing/_internal/generated/annotated_fn_args.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/generated/annotated_fn_args.py',
   'DATA'),
  ('torch/testing/_internal/generated/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/generated/__init__.py',
   'DATA'),
  ('torch/testing/_internal/dynamo_test_failures.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/dynamo_test_failures.py',
   'DATA'),
  ('torch/testing/_internal/distributed/rpc_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/distributed/rpc_utils.py',
   'DATA'),
  ('torch/testing/_internal/distributed/rpc/tensorpipe_rpc_agent_test_fixture.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/distributed/rpc/tensorpipe_rpc_agent_test_fixture.py',
   'DATA'),
  ('torch/testing/_internal/distributed/rpc/rpc_test.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/distributed/rpc/rpc_test.py',
   'DATA'),
  ('torch/testing/_internal/distributed/rpc/rpc_agent_test_fixture.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/distributed/rpc/rpc_agent_test_fixture.py',
   'DATA'),
  ('torch/testing/_internal/distributed/rpc/jit/rpc_test_faulty.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/distributed/rpc/jit/rpc_test_faulty.py',
   'DATA'),
  ('torch/testing/_internal/distributed/rpc/jit/rpc_test.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/distributed/rpc/jit/rpc_test.py',
   'DATA'),
  ('torch/testing/_internal/distributed/rpc/jit/dist_autograd_test.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/distributed/rpc/jit/dist_autograd_test.py',
   'DATA'),
  ('torch/testing/_internal/distributed/rpc/jit/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/distributed/rpc/jit/__init__.py',
   'DATA'),
  ('torch/testing/_internal/distributed/rpc/faulty_rpc_agent_test_fixture.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/distributed/rpc/faulty_rpc_agent_test_fixture.py',
   'DATA'),
  ('torch/testing/_internal/distributed/rpc/faulty_agent_rpc_test.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/distributed/rpc/faulty_agent_rpc_test.py',
   'DATA'),
  ('torch/testing/_internal/distributed/rpc/examples/reinforcement_learning_rpc_test.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/distributed/rpc/examples/reinforcement_learning_rpc_test.py',
   'DATA'),
  ('torch/testing/_internal/distributed/rpc/examples/parameter_server_test.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/distributed/rpc/examples/parameter_server_test.py',
   'DATA'),
  ('torch/testing/_internal/distributed/rpc/examples/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/distributed/rpc/examples/__init__.py',
   'DATA'),
  ('torch/testing/_internal/distributed/rpc/dist_optimizer_test.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/distributed/rpc/dist_optimizer_test.py',
   'DATA'),
  ('torch/testing/_internal/distributed/rpc/dist_autograd_test.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/distributed/rpc/dist_autograd_test.py',
   'DATA'),
  ('torch/testing/_internal/distributed/rpc/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/distributed/rpc/__init__.py',
   'DATA'),
  ('torch/testing/_internal/distributed/nn/api/remote_module_test.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/distributed/nn/api/remote_module_test.py',
   'DATA'),
  ('torch/testing/_internal/distributed/nn/api/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/distributed/nn/api/__init__.py',
   'DATA'),
  ('torch/testing/_internal/distributed/nn/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/distributed/nn/__init__.py',
   'DATA'),
  ('torch/testing/_internal/distributed/multi_threaded_pg.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/distributed/multi_threaded_pg.py',
   'DATA'),
  ('torch/testing/_internal/distributed/fake_pg.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/distributed/fake_pg.py',
   'DATA'),
  ('torch/testing/_internal/distributed/distributed_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/distributed/distributed_utils.py',
   'DATA'),
  ('torch/testing/_internal/distributed/distributed_test.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/distributed/distributed_test.py',
   'DATA'),
  ('torch/testing/_internal/distributed/ddp_under_dist_autograd_test.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/distributed/ddp_under_dist_autograd_test.py',
   'DATA'),
  ('torch/testing/_internal/distributed/common_state_dict.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/distributed/common_state_dict.py',
   'DATA'),
  ('torch/testing/_internal/distributed/checkpoint_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/distributed/checkpoint_utils.py',
   'DATA'),
  ('torch/testing/_internal/distributed/_tensor/common_dtensor.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/distributed/_tensor/common_dtensor.py',
   'DATA'),
  ('torch/testing/_internal/distributed/_tensor/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/distributed/_tensor/__init__.py',
   'DATA'),
  ('torch/testing/_internal/distributed/_shard/test_common.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/distributed/_shard/test_common.py',
   'DATA'),
  ('torch/testing/_internal/distributed/_shard/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/distributed/_shard/__init__.py',
   'DATA'),
  ('torch/testing/_internal/distributed/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/distributed/__init__.py',
   'DATA'),
  ('torch/testing/_internal/dist_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/dist_utils.py',
   'DATA'),
  ('torch/testing/_internal/data/network2.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/data/network2.py',
   'DATA'),
  ('torch/testing/_internal/data/network1.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/data/network1.py',
   'DATA'),
  ('torch/testing/_internal/data/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/data/__init__.py',
   'DATA'),
  ('torch/testing/_internal/custom_tensor.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/custom_tensor.py',
   'DATA'),
  ('torch/testing/_internal/custom_op_db.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/custom_op_db.py',
   'DATA'),
  ('torch/testing/_internal/composite_compliance.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/composite_compliance.py',
   'DATA'),
  ('torch/testing/_internal/common_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/common_utils.py',
   'DATA'),
  ('torch/testing/_internal/common_subclass.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/common_subclass.py',
   'DATA'),
  ('torch/testing/_internal/common_quantized.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/common_quantized.py',
   'DATA'),
  ('torch/testing/_internal/common_quantization.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/common_quantization.py',
   'DATA'),
  ('torch/testing/_internal/common_pruning.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/common_pruning.py',
   'DATA'),
  ('torch/testing/_internal/common_optimizers.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/common_optimizers.py',
   'DATA'),
  ('torch/testing/_internal/common_nn.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/common_nn.py',
   'DATA'),
  ('torch/testing/_internal/common_modules.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/common_modules.py',
   'DATA'),
  ('torch/testing/_internal/common_mkldnn.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/common_mkldnn.py',
   'DATA'),
  ('torch/testing/_internal/common_methods_invocations.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/common_methods_invocations.py',
   'DATA'),
  ('torch/testing/_internal/opinfo/definitions/sparse.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/opinfo/definitions/sparse.py',
   'DATA'),
  ('torch/testing/_internal/common_jit.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/common_jit.py',
   'DATA'),
  ('torch/testing/_internal/common_fsdp.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/common_fsdp.py',
   'DATA'),
  ('torch/testing/_internal/common_dtype.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/common_dtype.py',
   'DATA'),
  ('torch/testing/_internal/common_distributed.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/common_distributed.py',
   'DATA'),
  ('torch/testing/_internal/common_dist_composable.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/common_dist_composable.py',
   'DATA'),
  ('torch/testing/_internal/common_device_type.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/common_device_type.py',
   'DATA'),
  ('torch/testing/_internal/common_cuda.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/common_cuda.py',
   'DATA'),
  ('torch/testing/_internal/codegen/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/codegen/__init__.py',
   'DATA'),
  ('torch/testing/_internal/check_kernel_launches.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/check_kernel_launches.py',
   'DATA'),
  ('torch/testing/_internal/autograd_function_db.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/autograd_function_db.py',
   'DATA'),
  ('torch/testing/_internal/autocast_test_lists.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/autocast_test_lists.py',
   'DATA'),
  ('torch/testing/_internal/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_internal/__init__.py',
   'DATA'),
  ('torch/testing/_creation.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_creation.py',
   'DATA'),
  ('torch/testing/_comparison.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/_comparison.py',
   'DATA'),
  ('torch/sparse/semi_structured.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/sparse/semi_structured.py',
   'DATA'),
  ('torch/sparse/_triton_ops_meta.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/sparse/_triton_ops_meta.py',
   'DATA'),
  ('torch/sparse/_triton_ops.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/sparse/_triton_ops.py',
   'DATA'),
  ('torch/sparse/_semi_structured_ops.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/sparse/_semi_structured_ops.py',
   'DATA'),
  ('torch/sparse/_semi_structured_conversions.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/sparse/_semi_structured_conversions.py',
   'DATA'),
  ('torch/signal/windows/windows.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/signal/windows/windows.py',
   'DATA'),
  ('torch/quantization/utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/quantization/utils.py',
   'DATA'),
  ('torch/quantization/stubs.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/quantization/stubs.py',
   'DATA'),
  ('torch/quantization/quantize_jit.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/quantization/quantize_jit.py',
   'DATA'),
  ('torch/quantization/quantize_fx.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/quantization/quantize_fx.py',
   'DATA'),
  ('torch/quantization/quantize.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/quantization/quantize.py',
   'DATA'),
  ('torch/quantization/quantization_mappings.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/quantization/quantization_mappings.py',
   'DATA'),
  ('torch/quantization/quant_type.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/quantization/quant_type.py',
   'DATA'),
  ('torch/quantization/qconfig.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/quantization/qconfig.py',
   'DATA'),
  ('torch/quantization/observer.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/quantization/observer.py',
   'DATA'),
  ('torch/quantization/fx/utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/quantization/fx/utils.py',
   'DATA'),
  ('torch/quantization/fx/quantization_types.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/quantization/fx/quantization_types.py',
   'DATA'),
  ('torch/quantization/fx/quantization_patterns.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/quantization/fx/quantization_patterns.py',
   'DATA'),
  ('torch/quantization/fx/prepare.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/quantization/fx/prepare.py',
   'DATA'),
  ('torch/quantization/fx/pattern_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/quantization/fx/pattern_utils.py',
   'DATA'),
  ('torch/quantization/fx/match_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/quantization/fx/match_utils.py',
   'DATA'),
  ('torch/quantization/fx/graph_module.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/quantization/fx/graph_module.py',
   'DATA'),
  ('torch/quantization/fx/fusion_patterns.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/quantization/fx/fusion_patterns.py',
   'DATA'),
  ('torch/quantization/fx/fuse.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/quantization/fx/fuse.py',
   'DATA'),
  ('torch/quantization/fx/convert.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/quantization/fx/convert.py',
   'DATA'),
  ('torch/quantization/fx/_equalize.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/quantization/fx/_equalize.py',
   'DATA'),
  ('torch/quantization/fx/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/quantization/fx/__init__.py',
   'DATA'),
  ('torch/quantization/fuser_method_mappings.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/quantization/fuser_method_mappings.py',
   'DATA'),
  ('torch/quantization/fuse_modules.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/quantization/fuse_modules.py',
   'DATA'),
  ('torch/quantization/fake_quantize.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/quantization/fake_quantize.py',
   'DATA'),
  ('torch/quantization/_quantized_conversions.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/quantization/_quantized_conversions.py',
   'DATA'),
  ('torch/quantization/_numeric_suite_fx.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/quantization/_numeric_suite_fx.py',
   'DATA'),
  ('torch/quantization/_numeric_suite.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/quantization/_numeric_suite.py',
   'DATA'),
  ('torch/profiler/python_tracer.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/profiler/python_tracer.py',
   'DATA'),
  ('torch/profiler/profiler.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/profiler/profiler.py',
   'DATA'),
  ('torch/profiler/itt.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/profiler/itt.py',
   'DATA'),
  ('torch/profiler/_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/profiler/_utils.py',
   'DATA'),
  ('torch/profiler/_pattern_matcher.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/profiler/_pattern_matcher.py',
   'DATA'),
  ('torch/profiler/_memory_profiler.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/profiler/_memory_profiler.py',
   'DATA'),
  ('torch/package/package_importer.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/package/package_importer.py',
   'DATA'),
  ('torch/package/package_exporter.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/package/package_exporter.py',
   'DATA'),
  ('torch/package/importer.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/package/importer.py',
   'DATA'),
  ('torch/package/glob_group.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/package/glob_group.py',
   'DATA'),
  ('torch/package/find_file_dependencies.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/package/find_file_dependencies.py',
   'DATA'),
  ('torch/package/file_structure_representation.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/package/file_structure_representation.py',
   'DATA'),
  ('torch/package/analyze/trace_dependencies.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/package/analyze/trace_dependencies.py',
   'DATA'),
  ('torch/package/analyze/is_from_package.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/package/analyze/is_from_package.py',
   'DATA'),
  ('torch/package/analyze/find_first_use_of_broken_modules.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/package/analyze/find_first_use_of_broken_modules.py',
   'DATA'),
  ('torch/package/analyze/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/package/analyze/__init__.py',
   'DATA'),
  ('torch/package/_stdlib.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/package/_stdlib.py',
   'DATA'),
  ('torch/package/_package_unpickler.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/package/_package_unpickler.py',
   'DATA'),
  ('torch/package/_package_pickler.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/package/_package_pickler.py',
   'DATA'),
  ('torch/package/_mock.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/package/_mock.py',
   'DATA'),
  ('torch/package/_mangling.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/package/_mangling.py',
   'DATA'),
  ('torch/package/_importlib.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/package/_importlib.py',
   'DATA'),
  ('torch/package/_directory_reader.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/package/_directory_reader.py',
   'DATA'),
  ('torch/package/_digraph.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/package/_digraph.py',
   'DATA'),
  ('torch/package/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/package/__init__.py',
   'DATA'),
  ('torch/optim/swa_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/optim/swa_utils.py',
   'DATA'),
  ('torch/optim/sparse_adam.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/optim/sparse_adam.py',
   'DATA'),
  ('torch/optim/sgd.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/optim/sgd.py',
   'DATA'),
  ('torch/optim/rprop.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/optim/rprop.py',
   'DATA'),
  ('torch/optim/rmsprop.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/optim/rmsprop.py',
   'DATA'),
  ('torch/optim/radam.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/optim/radam.py',
   'DATA'),
  ('torch/optim/optimizer.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/optim/optimizer.py',
   'DATA'),
  ('torch/optim/nadam.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/optim/nadam.py',
   'DATA'),
  ('torch/optim/lr_scheduler.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/optim/lr_scheduler.py',
   'DATA'),
  ('torch/optim/lbfgs.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/optim/lbfgs.py',
   'DATA'),
  ('torch/optim/asgd.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/optim/asgd.py',
   'DATA'),
  ('torch/optim/adamw.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/optim/adamw.py',
   'DATA'),
  ('torch/optim/adamax.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/optim/adamax.py',
   'DATA'),
  ('torch/optim/adam.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/optim/adam.py',
   'DATA'),
  ('torch/optim/adagrad.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/optim/adagrad.py',
   'DATA'),
  ('torch/optim/adadelta.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/optim/adadelta.py',
   'DATA'),
  ('torch/optim/_multi_tensor/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/optim/_multi_tensor/__init__.py',
   'DATA'),
  ('torch/optim/_functional.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/optim/_functional.py',
   'DATA'),
  ('torch/optim/_adafactor.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/optim/_adafactor.py',
   'DATA'),
  ('torch/onnx/verification.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/verification.py',
   'DATA'),
  ('torch/onnx/utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/utils.py',
   'DATA'),
  ('torch/onnx/symbolic_opset9.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/symbolic_opset9.py',
   'DATA'),
  ('torch/onnx/symbolic_opset8.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/symbolic_opset8.py',
   'DATA'),
  ('torch/onnx/symbolic_opset7.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/symbolic_opset7.py',
   'DATA'),
  ('torch/onnx/symbolic_opset20.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/symbolic_opset20.py',
   'DATA'),
  ('torch/onnx/symbolic_opset19.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/symbolic_opset19.py',
   'DATA'),
  ('torch/onnx/symbolic_opset18.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/symbolic_opset18.py',
   'DATA'),
  ('torch/onnx/symbolic_opset17.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/symbolic_opset17.py',
   'DATA'),
  ('torch/onnx/symbolic_opset16.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/symbolic_opset16.py',
   'DATA'),
  ('torch/onnx/symbolic_opset15.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/symbolic_opset15.py',
   'DATA'),
  ('torch/onnx/symbolic_opset14.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/symbolic_opset14.py',
   'DATA'),
  ('torch/onnx/symbolic_opset13.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/symbolic_opset13.py',
   'DATA'),
  ('torch/onnx/symbolic_opset12.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/symbolic_opset12.py',
   'DATA'),
  ('torch/onnx/symbolic_opset11.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/symbolic_opset11.py',
   'DATA'),
  ('torch/onnx/symbolic_opset10.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/symbolic_opset10.py',
   'DATA'),
  ('torch/onnx/symbolic_helper.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/symbolic_helper.py',
   'DATA'),
  ('torch/onnx/symbolic_caffe2.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/symbolic_caffe2.py',
   'DATA'),
  ('torch/onnx/operators.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/operators.py',
   'DATA'),
  ('torch/onnx/errors.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/errors.py',
   'DATA'),
  ('torch/onnx/_type_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_type_utils.py',
   'DATA'),
  ('torch/onnx/_onnx_supported_ops.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_onnx_supported_ops.py',
   'DATA'),
  ('torch/onnx/_internal/registration.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/registration.py',
   'DATA'),
  ('torch/onnx/_internal/onnxruntime.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/onnxruntime.py',
   'DATA'),
  ('torch/onnx/_internal/fx/passes/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/fx/passes/__init__.py',
   'DATA'),
  ('torch/onnx/_internal/fx/passes/virtualization.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/fx/passes/virtualization.py',
   'DATA'),
  ('torch/onnx/_internal/fx/passes/type_promotion.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/fx/passes/type_promotion.py',
   'DATA'),
  ('torch/onnx/_internal/fx/passes/readability.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/fx/passes/readability.py',
   'DATA'),
  ('torch/onnx/_internal/fx/passes/modularization.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/fx/passes/modularization.py',
   'DATA'),
  ('torch/onnx/_internal/fx/passes/functionalization.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/fx/passes/functionalization.py',
   'DATA'),
  ('torch/onnx/_internal/fx/passes/decomp.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/fx/passes/decomp.py',
   'DATA'),
  ('torch/onnx/_internal/fx/passes/_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/fx/passes/_utils.py',
   'DATA'),
  ('torch/onnx/_internal/onnx_proto_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/onnx_proto_utils.py',
   'DATA'),
  ('torch/onnx/_internal/jit_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/jit_utils.py',
   'DATA'),
  ('torch/onnx/_internal/io_adapter.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/io_adapter.py',
   'DATA'),
  ('torch/onnx/_internal/fx/type_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/fx/type_utils.py',
   'DATA'),
  ('torch/onnx/_internal/fx/serialization.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/fx/serialization.py',
   'DATA'),
  ('torch/onnx/_internal/fx/registration.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/fx/registration.py',
   'DATA'),
  ('torch/onnx/_internal/fx/patcher.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/fx/patcher.py',
   'DATA'),
  ('torch/onnx/_internal/fx/onnxfunction_dispatcher.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/fx/onnxfunction_dispatcher.py',
   'DATA'),
  ('torch/onnx/_internal/fx/fx_symbolic_graph_extractor.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/fx/fx_symbolic_graph_extractor.py',
   'DATA'),
  ('torch/onnx/_internal/fx/fx_onnx_interpreter.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/fx/fx_onnx_interpreter.py',
   'DATA'),
  ('torch/onnx/_internal/fx/dynamo_graph_extractor.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/fx/dynamo_graph_extractor.py',
   'DATA'),
  ('torch/onnx/_internal/fx/diagnostics.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/fx/diagnostics.py',
   'DATA'),
  ('torch/onnx/_internal/fx/decomposition_table.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/fx/decomposition_table.py',
   'DATA'),
  ('torch/onnx/_internal/fx/decomposition_skip.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/fx/decomposition_skip.py',
   'DATA'),
  ('torch/onnx/_internal/fx/_pass.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/fx/_pass.py',
   'DATA'),
  ('torch/onnx/_internal/fx/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/fx/__init__.py',
   'DATA'),
  ('torch/onnx/_internal/fx/analysis/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/fx/analysis/__init__.py',
   'DATA'),
  ('torch/onnx/_internal/fx/analysis/unsupported_nodes.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/fx/analysis/unsupported_nodes.py',
   'DATA'),
  ('torch/onnx/_internal/diagnostics/infra/utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/diagnostics/infra/utils.py',
   'DATA'),
  ('torch/onnx/_internal/diagnostics/infra/sarif/version.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/diagnostics/infra/sarif/version.py',
   'DATA'),
  ('torch/onnx/_internal/diagnostics/infra/sarif/_web_response.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/diagnostics/infra/sarif/_web_response.py',
   'DATA'),
  ('torch/onnx/_internal/diagnostics/infra/sarif/_web_request.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/diagnostics/infra/sarif/_web_request.py',
   'DATA'),
  ('torch/onnx/_internal/diagnostics/infra/sarif/_version_control_details.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/diagnostics/infra/sarif/_version_control_details.py',
   'DATA'),
  ('torch/onnx/_internal/diagnostics/infra/sarif/_translation_metadata.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/diagnostics/infra/sarif/_translation_metadata.py',
   'DATA'),
  ('torch/onnx/_internal/diagnostics/infra/sarif/_tool_component_reference.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/diagnostics/infra/sarif/_tool_component_reference.py',
   'DATA'),
  ('torch/onnx/_internal/diagnostics/infra/sarif/_tool_component.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/diagnostics/infra/sarif/_tool_component.py',
   'DATA'),
  ('torch/onnx/_internal/diagnostics/infra/sarif/_tool.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/diagnostics/infra/sarif/_tool.py',
   'DATA'),
  ('torch/onnx/_internal/diagnostics/infra/sarif/_thread_flow_location.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/diagnostics/infra/sarif/_thread_flow_location.py',
   'DATA'),
  ('torch/onnx/_internal/diagnostics/infra/sarif/_thread_flow.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/diagnostics/infra/sarif/_thread_flow.py',
   'DATA'),
  ('torch/onnx/_internal/diagnostics/infra/sarif/_suppression.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/diagnostics/infra/sarif/_suppression.py',
   'DATA'),
  ('torch/onnx/_internal/diagnostics/infra/sarif/_stack_frame.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/diagnostics/infra/sarif/_stack_frame.py',
   'DATA'),
  ('torch/onnx/_internal/diagnostics/infra/sarif/_stack.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/diagnostics/infra/sarif/_stack.py',
   'DATA'),
  ('torch/onnx/_internal/diagnostics/infra/sarif/_special_locations.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/diagnostics/infra/sarif/_special_locations.py',
   'DATA'),
  ('torch/onnx/_internal/diagnostics/infra/sarif/_sarif_log.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/diagnostics/infra/sarif/_sarif_log.py',
   'DATA'),
  ('torch/onnx/_internal/diagnostics/infra/sarif/_run_automation_details.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/diagnostics/infra/sarif/_run_automation_details.py',
   'DATA'),
  ('torch/onnx/_internal/diagnostics/infra/sarif/_run.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/diagnostics/infra/sarif/_run.py',
   'DATA'),
  ('torch/onnx/_internal/diagnostics/infra/sarif/_result_provenance.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/diagnostics/infra/sarif/_result_provenance.py',
   'DATA'),
  ('torch/onnx/_internal/diagnostics/infra/sarif/_result.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/diagnostics/infra/sarif/_result.py',
   'DATA'),
  ('torch/onnx/_internal/diagnostics/infra/sarif/_reporting_descriptor_relationship.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/diagnostics/infra/sarif/_reporting_descriptor_relationship.py',
   'DATA'),
  ('torch/onnx/_internal/diagnostics/infra/sarif/_reporting_descriptor_reference.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/diagnostics/infra/sarif/_reporting_descriptor_reference.py',
   'DATA'),
  ('torch/onnx/_internal/diagnostics/infra/sarif/_reporting_descriptor.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/diagnostics/infra/sarif/_reporting_descriptor.py',
   'DATA'),
  ('torch/onnx/_internal/diagnostics/infra/sarif/_reporting_configuration.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/diagnostics/infra/sarif/_reporting_configuration.py',
   'DATA'),
  ('torch/onnx/_internal/diagnostics/infra/sarif/_replacement.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/diagnostics/infra/sarif/_replacement.py',
   'DATA'),
  ('torch/onnx/_internal/diagnostics/infra/sarif/_region.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/diagnostics/infra/sarif/_region.py',
   'DATA'),
  ('torch/onnx/_internal/diagnostics/infra/sarif/_rectangle.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/diagnostics/infra/sarif/_rectangle.py',
   'DATA'),
  ('torch/onnx/_internal/diagnostics/infra/sarif/_property_bag.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/diagnostics/infra/sarif/_property_bag.py',
   'DATA'),
  ('torch/onnx/_internal/diagnostics/infra/sarif/_physical_location.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/diagnostics/infra/sarif/_physical_location.py',
   'DATA'),
  ('torch/onnx/_internal/diagnostics/infra/sarif/_notification.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/diagnostics/infra/sarif/_notification.py',
   'DATA'),
  ('torch/onnx/_internal/diagnostics/infra/sarif/_node.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/diagnostics/infra/sarif/_node.py',
   'DATA'),
  ('torch/onnx/_internal/diagnostics/infra/sarif/_multiformat_message_string.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/diagnostics/infra/sarif/_multiformat_message_string.py',
   'DATA'),
  ('torch/onnx/_internal/diagnostics/infra/sarif/_message.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/diagnostics/infra/sarif/_message.py',
   'DATA'),
  ('torch/onnx/_internal/diagnostics/infra/sarif/_logical_location.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/diagnostics/infra/sarif/_logical_location.py',
   'DATA'),
  ('torch/onnx/_internal/diagnostics/infra/sarif/_location_relationship.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/diagnostics/infra/sarif/_location_relationship.py',
   'DATA'),
  ('torch/onnx/_internal/diagnostics/infra/sarif/_location.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/diagnostics/infra/sarif/_location.py',
   'DATA'),
  ('torch/onnx/_internal/diagnostics/infra/sarif/_invocation.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/diagnostics/infra/sarif/_invocation.py',
   'DATA'),
  ('torch/onnx/_internal/diagnostics/infra/sarif/_graph_traversal.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/diagnostics/infra/sarif/_graph_traversal.py',
   'DATA'),
  ('torch/onnx/_internal/diagnostics/infra/sarif/_graph.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/diagnostics/infra/sarif/_graph.py',
   'DATA'),
  ('torch/onnx/_internal/diagnostics/infra/sarif/_fix.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/diagnostics/infra/sarif/_fix.py',
   'DATA'),
  ('torch/onnx/_internal/diagnostics/infra/sarif/_external_property_file_references.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/diagnostics/infra/sarif/_external_property_file_references.py',
   'DATA'),
  ('torch/onnx/_internal/diagnostics/infra/sarif/_external_property_file_reference.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/diagnostics/infra/sarif/_external_property_file_reference.py',
   'DATA'),
  ('torch/onnx/_internal/diagnostics/infra/sarif/_external_properties.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/diagnostics/infra/sarif/_external_properties.py',
   'DATA'),
  ('torch/onnx/_internal/diagnostics/infra/sarif/_exception.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/diagnostics/infra/sarif/_exception.py',
   'DATA'),
  ('torch/onnx/_internal/diagnostics/infra/sarif/_edge_traversal.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/diagnostics/infra/sarif/_edge_traversal.py',
   'DATA'),
  ('torch/onnx/_internal/diagnostics/infra/sarif/_edge.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/diagnostics/infra/sarif/_edge.py',
   'DATA'),
  ('torch/onnx/_internal/diagnostics/infra/sarif/_conversion.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/diagnostics/infra/sarif/_conversion.py',
   'DATA'),
  ('torch/onnx/_internal/diagnostics/infra/sarif/_configuration_override.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/diagnostics/infra/sarif/_configuration_override.py',
   'DATA'),
  ('torch/onnx/_internal/diagnostics/infra/sarif/_code_flow.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/diagnostics/infra/sarif/_code_flow.py',
   'DATA'),
  ('torch/onnx/_internal/diagnostics/infra/sarif/_attachment.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/diagnostics/infra/sarif/_attachment.py',
   'DATA'),
  ('torch/onnx/_internal/diagnostics/infra/sarif/_artifact_location.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/diagnostics/infra/sarif/_artifact_location.py',
   'DATA'),
  ('torch/onnx/_internal/diagnostics/infra/sarif/_artifact_content.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/diagnostics/infra/sarif/_artifact_content.py',
   'DATA'),
  ('torch/onnx/_internal/diagnostics/infra/sarif/_artifact_change.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/diagnostics/infra/sarif/_artifact_change.py',
   'DATA'),
  ('torch/onnx/_internal/diagnostics/infra/sarif/_artifact.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/diagnostics/infra/sarif/_artifact.py',
   'DATA'),
  ('torch/onnx/_internal/diagnostics/infra/sarif/_address.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/diagnostics/infra/sarif/_address.py',
   'DATA'),
  ('torch/onnx/_internal/diagnostics/infra/sarif/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/diagnostics/infra/sarif/__init__.py',
   'DATA'),
  ('torch/onnx/_internal/diagnostics/infra/formatter.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/diagnostics/infra/formatter.py',
   'DATA'),
  ('torch/onnx/_internal/diagnostics/infra/decorator.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/diagnostics/infra/decorator.py',
   'DATA'),
  ('torch/onnx/_internal/diagnostics/infra/context.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/diagnostics/infra/context.py',
   'DATA'),
  ('torch/onnx/_internal/diagnostics/infra/_infra.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/diagnostics/infra/_infra.py',
   'DATA'),
  ('torch/onnx/_internal/diagnostics/infra/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/diagnostics/infra/__init__.py',
   'DATA'),
  ('torch/onnx/_internal/diagnostics/_rules.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/diagnostics/_rules.py',
   'DATA'),
  ('torch/onnx/_internal/diagnostics/_diagnostic.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/diagnostics/_diagnostic.py',
   'DATA'),
  ('torch/onnx/_internal/diagnostics/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/diagnostics/__init__.py',
   'DATA'),
  ('torch/onnx/_internal/_lazy_import.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/_lazy_import.py',
   'DATA'),
  ('torch/onnx/_internal/_exporter_legacy.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/_exporter_legacy.py',
   'DATA'),
  ('torch/onnx/_internal/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/__init__.py',
   'DATA'),
  ('torch/onnx/_internal/exporter/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/exporter/__init__.py',
   'DATA'),
  ('torch/onnx/_internal/exporter/_compat.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/exporter/_compat.py',
   'DATA'),
  ('torch/onnx/_internal/exporter/_testing.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/exporter/_testing.py',
   'DATA'),
  ('torch/onnx/_internal/exporter/_core.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/exporter/_core.py',
   'DATA'),
  ('torch/onnx/_internal/exporter/_reporting.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/exporter/_reporting.py',
   'DATA'),
  ('torch/onnx/_internal/exporter/_verification.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/exporter/_verification.py',
   'DATA'),
  ('torch/onnx/_internal/exporter/_onnx_program.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/exporter/_onnx_program.py',
   'DATA'),
  ('torch/onnx/_internal/exporter/_ir_passes.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/exporter/_ir_passes.py',
   'DATA'),
  ('torch/onnx/_internal/exporter/_fx_passes.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/exporter/_fx_passes.py',
   'DATA'),
  ('torch/onnx/_internal/exporter/_decomp.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/exporter/_decomp.py',
   'DATA'),
  ('torch/onnx/_internal/exporter/_capture_strategies.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/exporter/_capture_strategies.py',
   'DATA'),
  ('torch/onnx/_internal/exporter/_building.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/exporter/_building.py',
   'DATA'),
  ('torch/onnx/_internal/exporter/_tensors.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/exporter/_tensors.py',
   'DATA'),
  ('torch/onnx/_internal/exporter/_errors.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/exporter/_errors.py',
   'DATA'),
  ('torch/onnx/_internal/exporter/_analysis.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/exporter/_analysis.py',
   'DATA'),
  ('torch/onnx/_internal/exporter/_dispatching.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/exporter/_dispatching.py',
   'DATA'),
  ('torch/onnx/_internal/exporter/_registration.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/exporter/_registration.py',
   'DATA'),
  ('torch/onnx/_internal/exporter/_schemas.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_internal/exporter/_schemas.py',
   'DATA'),
  ('torch/onnx/_globals.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_globals.py',
   'DATA'),
  ('torch/onnx/_flags.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_flags.py',
   'DATA'),
  ('torch/onnx/_exporter_states.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_exporter_states.py',
   'DATA'),
  ('torch/onnx/_experimental.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_experimental.py',
   'DATA'),
  ('torch/onnx/_deprecation.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_deprecation.py',
   'DATA'),
  ('torch/onnx/_constants.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/_constants.py',
   'DATA'),
  ('torch/nn/utils/weight_norm.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/utils/weight_norm.py',
   'DATA'),
  ('torch/nn/utils/stateless.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/utils/stateless.py',
   'DATA'),
  ('torch/nn/utils/spectral_norm.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/utils/spectral_norm.py',
   'DATA'),
  ('torch/nn/utils/rnn.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/utils/rnn.py',
   'DATA'),
  ('torch/nn/utils/prune.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/utils/prune.py',
   'DATA'),
  ('torch/nn/utils/parametrize.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/utils/parametrize.py',
   'DATA'),
  ('torch/nn/utils/parametrizations.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/utils/parametrizations.py',
   'DATA'),
  ('torch/nn/utils/memory_format.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/utils/memory_format.py',
   'DATA'),
  ('torch/nn/utils/init.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/utils/init.py',
   'DATA'),
  ('torch/nn/utils/fusion.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/utils/fusion.py',
   'DATA'),
  ('torch/nn/utils/convert_parameters.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/utils/convert_parameters.py',
   'DATA'),
  ('torch/nn/utils/clip_grad.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/utils/clip_grad.py',
   'DATA'),
  ('torch/nn/utils/_per_sample_grad.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/utils/_per_sample_grad.py',
   'DATA'),
  ('torch/nn/utils/_named_member_accessor.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/utils/_named_member_accessor.py',
   'DATA'),
  ('torch/nn/utils/_expanded_weights/linear_expanded_weights.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/utils/_expanded_weights/linear_expanded_weights.py',
   'DATA'),
  ('torch/nn/utils/_expanded_weights/layer_norm_expanded_weights.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/utils/_expanded_weights/layer_norm_expanded_weights.py',
   'DATA'),
  ('torch/nn/utils/_expanded_weights/instance_norm_expanded_weights.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/utils/_expanded_weights/instance_norm_expanded_weights.py',
   'DATA'),
  ('torch/nn/utils/_expanded_weights/group_norm_expanded_weights.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/utils/_expanded_weights/group_norm_expanded_weights.py',
   'DATA'),
  ('torch/nn/utils/_expanded_weights/expanded_weights_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/utils/_expanded_weights/expanded_weights_utils.py',
   'DATA'),
  ('torch/nn/utils/_expanded_weights/expanded_weights_impl.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/utils/_expanded_weights/expanded_weights_impl.py',
   'DATA'),
  ('torch/nn/utils/_expanded_weights/embedding_expanded_weights.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/utils/_expanded_weights/embedding_expanded_weights.py',
   'DATA'),
  ('torch/nn/utils/_expanded_weights/conv_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/utils/_expanded_weights/conv_utils.py',
   'DATA'),
  ('torch/nn/utils/_expanded_weights/conv_expanded_weights.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/utils/_expanded_weights/conv_expanded_weights.py',
   'DATA'),
  ('torch/nn/utils/_expanded_weights/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/utils/_expanded_weights/__init__.py',
   'DATA'),
  ('torch/nn/utils/_deprecation_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/utils/_deprecation_utils.py',
   'DATA'),
  ('torch/nn/utils/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/utils/__init__.py',
   'DATA'),
  ('torch/nn/quantized/modules/utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/quantized/modules/utils.py',
   'DATA'),
  ('torch/nn/quantized/modules/rnn.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/quantized/modules/rnn.py',
   'DATA'),
  ('torch/nn/quantized/modules/normalization.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/quantized/modules/normalization.py',
   'DATA'),
  ('torch/nn/quantized/modules/linear.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/quantized/modules/linear.py',
   'DATA'),
  ('torch/nn/quantized/modules/functional_modules.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/quantized/modules/functional_modules.py',
   'DATA'),
  ('torch/nn/quantized/modules/embedding_ops.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/quantized/modules/embedding_ops.py',
   'DATA'),
  ('torch/nn/quantized/modules/dropout.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/quantized/modules/dropout.py',
   'DATA'),
  ('torch/nn/quantized/modules/conv.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/quantized/modules/conv.py',
   'DATA'),
  ('torch/nn/quantized/modules/batchnorm.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/quantized/modules/batchnorm.py',
   'DATA'),
  ('torch/nn/quantized/modules/activation.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/quantized/modules/activation.py',
   'DATA'),
  ('torch/nn/quantized/modules/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/quantized/modules/__init__.py',
   'DATA'),
  ('torch/nn/quantized/functional.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/quantized/functional.py',
   'DATA'),
  ('torch/nn/quantized/dynamic/modules/rnn.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/quantized/dynamic/modules/rnn.py',
   'DATA'),
  ('torch/nn/quantized/dynamic/modules/linear.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/quantized/dynamic/modules/linear.py',
   'DATA'),
  ('torch/nn/quantized/dynamic/modules/conv.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/quantized/dynamic/modules/conv.py',
   'DATA'),
  ('torch/nn/quantized/dynamic/modules/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/quantized/dynamic/modules/__init__.py',
   'DATA'),
  ('torch/nn/quantized/dynamic/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/quantized/dynamic/__init__.py',
   'DATA'),
  ('torch/nn/quantized/_reference/modules/utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/quantized/_reference/modules/utils.py',
   'DATA'),
  ('torch/nn/quantized/_reference/modules/sparse.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/quantized/_reference/modules/sparse.py',
   'DATA'),
  ('torch/nn/quantized/_reference/modules/rnn.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/quantized/_reference/modules/rnn.py',
   'DATA'),
  ('torch/nn/quantized/_reference/modules/linear.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/quantized/_reference/modules/linear.py',
   'DATA'),
  ('torch/nn/quantized/_reference/modules/conv.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/quantized/_reference/modules/conv.py',
   'DATA'),
  ('torch/nn/quantized/_reference/modules/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/quantized/_reference/modules/__init__.py',
   'DATA'),
  ('torch/nn/quantized/_reference/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/quantized/_reference/__init__.py',
   'DATA'),
  ('torch/nn/quantizable/modules/rnn.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/quantizable/modules/rnn.py',
   'DATA'),
  ('torch/nn/quantizable/modules/activation.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/quantizable/modules/activation.py',
   'DATA'),
  ('torch/nn/quantizable/modules/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/quantizable/modules/__init__.py',
   'DATA'),
  ('torch/nn/qat/modules/linear.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/qat/modules/linear.py',
   'DATA'),
  ('torch/nn/qat/modules/embedding_ops.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/qat/modules/embedding_ops.py',
   'DATA'),
  ('torch/nn/qat/modules/conv.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/qat/modules/conv.py',
   'DATA'),
  ('torch/nn/qat/modules/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/qat/modules/__init__.py',
   'DATA'),
  ('torch/nn/qat/dynamic/modules/linear.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/qat/dynamic/modules/linear.py',
   'DATA'),
  ('torch/nn/qat/dynamic/modules/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/qat/dynamic/modules/__init__.py',
   'DATA'),
  ('torch/nn/qat/dynamic/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/qat/dynamic/__init__.py',
   'DATA'),
  ('torch/nn/parameter.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/parameter.py',
   'DATA'),
  ('torch/nn/parallel/scatter_gather.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/parallel/scatter_gather.py',
   'DATA'),
  ('torch/nn/parallel/replicate.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/parallel/replicate.py',
   'DATA'),
  ('torch/nn/parallel/parallel_apply.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/parallel/parallel_apply.py',
   'DATA'),
  ('torch/nn/parallel/distributed.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/parallel/distributed.py',
   'DATA'),
  ('torch/nn/parallel/data_parallel.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/parallel/data_parallel.py',
   'DATA'),
  ('torch/nn/parallel/comm.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/parallel/comm.py',
   'DATA'),
  ('torch/nn/parallel/_functions.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/parallel/_functions.py',
   'DATA'),
  ('torch/nn/parallel/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/parallel/__init__.py',
   'DATA'),
  ('torch/nn/modules/utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/modules/utils.py',
   'DATA'),
  ('torch/nn/modules/upsampling.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/modules/upsampling.py',
   'DATA'),
  ('torch/nn/modules/transformer.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/modules/transformer.py',
   'DATA'),
  ('torch/nn/modules/sparse.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/modules/sparse.py',
   'DATA'),
  ('torch/nn/modules/rnn.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/modules/rnn.py',
   'DATA'),
  ('torch/nn/modules/pooling.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/modules/pooling.py',
   'DATA'),
  ('torch/nn/modules/pixelshuffle.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/modules/pixelshuffle.py',
   'DATA'),
  ('torch/nn/modules/padding.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/modules/padding.py',
   'DATA'),
  ('torch/nn/modules/normalization.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/modules/normalization.py',
   'DATA'),
  ('torch/nn/modules/module.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/modules/module.py',
   'DATA'),
  ('torch/nn/modules/loss.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/modules/loss.py',
   'DATA'),
  ('torch/nn/modules/linear.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/modules/linear.py',
   'DATA'),
  ('torch/nn/modules/lazy.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/modules/lazy.py',
   'DATA'),
  ('torch/nn/modules/instancenorm.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/modules/instancenorm.py',
   'DATA'),
  ('torch/nn/modules/fold.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/modules/fold.py',
   'DATA'),
  ('torch/nn/modules/flatten.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/modules/flatten.py',
   'DATA'),
  ('torch/nn/modules/dropout.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/modules/dropout.py',
   'DATA'),
  ('torch/nn/modules/distance.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/modules/distance.py',
   'DATA'),
  ('torch/nn/modules/conv.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/modules/conv.py',
   'DATA'),
  ('torch/nn/modules/container.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/modules/container.py',
   'DATA'),
  ('torch/nn/modules/channelshuffle.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/modules/channelshuffle.py',
   'DATA'),
  ('torch/nn/modules/batchnorm.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/modules/batchnorm.py',
   'DATA'),
  ('torch/nn/modules/adaptive.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/modules/adaptive.py',
   'DATA'),
  ('torch/nn/modules/activation.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/modules/activation.py',
   'DATA'),
  ('torch/nn/modules/_functions.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/modules/_functions.py',
   'DATA'),
  ('torch/nn/modules/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/modules/__init__.py',
   'DATA'),
  ('torch/nn/intrinsic/quantized/modules/linear_relu.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/intrinsic/quantized/modules/linear_relu.py',
   'DATA'),
  ('torch/nn/intrinsic/quantized/modules/conv_relu.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/intrinsic/quantized/modules/conv_relu.py',
   'DATA'),
  ('torch/nn/intrinsic/quantized/modules/bn_relu.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/intrinsic/quantized/modules/bn_relu.py',
   'DATA'),
  ('torch/nn/intrinsic/quantized/modules/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/intrinsic/quantized/modules/__init__.py',
   'DATA'),
  ('torch/nn/intrinsic/quantized/dynamic/modules/linear_relu.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/intrinsic/quantized/dynamic/modules/linear_relu.py',
   'DATA'),
  ('torch/nn/intrinsic/quantized/dynamic/modules/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/intrinsic/quantized/dynamic/modules/__init__.py',
   'DATA'),
  ('torch/nn/intrinsic/quantized/dynamic/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/intrinsic/quantized/dynamic/__init__.py',
   'DATA'),
  ('torch/nn/intrinsic/quantized/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/intrinsic/quantized/__init__.py',
   'DATA'),
  ('torch/nn/intrinsic/qat/modules/linear_relu.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/intrinsic/qat/modules/linear_relu.py',
   'DATA'),
  ('torch/nn/intrinsic/qat/modules/linear_fused.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/intrinsic/qat/modules/linear_fused.py',
   'DATA'),
  ('torch/nn/intrinsic/qat/modules/conv_fused.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/intrinsic/qat/modules/conv_fused.py',
   'DATA'),
  ('torch/nn/intrinsic/qat/modules/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/intrinsic/qat/modules/__init__.py',
   'DATA'),
  ('torch/nn/intrinsic/qat/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/intrinsic/qat/__init__.py',
   'DATA'),
  ('torch/nn/intrinsic/modules/fused.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/intrinsic/modules/fused.py',
   'DATA'),
  ('torch/nn/intrinsic/modules/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/intrinsic/modules/__init__.py',
   'DATA'),
  ('torch/nn/init.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/init.py',
   'DATA'),
  ('torch/nn/grad.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/grad.py',
   'DATA'),
  ('torch/nn/functional.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/functional.py',
   'DATA'),
  ('torch/nn/cpp.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/cpp.py',
   'DATA'),
  ('torch/nn/common_types.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/common_types.py',
   'DATA'),
  ('torch/nn/backends/thnn.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/backends/thnn.py',
   'DATA'),
  ('torch/nn/backends/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/backends/__init__.py',
   'DATA'),
  ('torch/nn/attention/flex_attention.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/attention/flex_attention.py',
   'DATA'),
  ('torch/nn/attention/bias.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/attention/bias.py',
   'DATA'),
  ('torch/nn/attention/_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/attention/_utils.py',
   'DATA'),
  ('torch/nn/attention/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/attention/__init__.py',
   'DATA'),
  ('torch/nn/_reduction.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/_reduction.py',
   'DATA'),
  ('torch/nested/_internal/sdpa.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nested/_internal/sdpa.py',
   'DATA'),
  ('torch/nested/_internal/ops.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nested/_internal/ops.py',
   'DATA'),
  ('torch/nested/_internal/nested_tensor.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nested/_internal/nested_tensor.py',
   'DATA'),
  ('torch/nested/_internal/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nested/_internal/__init__.py',
   'DATA'),
  ('torch/multiprocessing/spawn.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/multiprocessing/spawn.py',
   'DATA'),
  ('torch/multiprocessing/reductions.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/multiprocessing/reductions.py',
   'DATA'),
  ('torch/multiprocessing/queue.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/multiprocessing/queue.py',
   'DATA'),
  ('torch/multiprocessing/pool.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/multiprocessing/pool.py',
   'DATA'),
  ('torch/mtia/_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/mtia/_utils.py',
   'DATA'),
  ('torch/mps/profiler.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/mps/profiler.py',
   'DATA'),
  ('torch/mps/event.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/mps/event.py',
   'DATA'),
  ('torch/monitor/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/monitor/__init__.py',
   'DATA'),
  ('torch/utils/tensorboard/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/tensorboard/__init__.py',
   'DATA'),
  ('torch/utils/tensorboard/writer.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/tensorboard/writer.py',
   'DATA'),
  ('torch/utils/tensorboard/summary.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/tensorboard/summary.py',
   'DATA'),
  ('torch/utils/tensorboard/_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/tensorboard/_utils.py',
   'DATA'),
  ('torch/utils/tensorboard/_pytorch_graph.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/tensorboard/_pytorch_graph.py',
   'DATA'),
  ('torch/utils/tensorboard/_proto_graph.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/tensorboard/_proto_graph.py',
   'DATA'),
  ('torch/utils/tensorboard/_onnx_graph.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/tensorboard/_onnx_graph.py',
   'DATA'),
  ('torch/utils/tensorboard/_embedding.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/tensorboard/_embedding.py',
   'DATA'),
  ('torch/utils/tensorboard/_convert_np.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/tensorboard/_convert_np.py',
   'DATA'),
  ('torch/masked/maskedtensor/unary.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/masked/maskedtensor/unary.py',
   'DATA'),
  ('torch/masked/maskedtensor/reductions.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/masked/maskedtensor/reductions.py',
   'DATA'),
  ('torch/masked/maskedtensor/passthrough.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/masked/maskedtensor/passthrough.py',
   'DATA'),
  ('torch/masked/maskedtensor/creation.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/masked/maskedtensor/creation.py',
   'DATA'),
  ('torch/masked/maskedtensor/core.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/masked/maskedtensor/core.py',
   'DATA'),
  ('torch/masked/maskedtensor/binary.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/masked/maskedtensor/binary.py',
   'DATA'),
  ('torch/masked/maskedtensor/_ops_refs.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/masked/maskedtensor/_ops_refs.py',
   'DATA'),
  ('torch/masked/maskedtensor/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/masked/maskedtensor/__init__.py',
   'DATA'),
  ('torch/masked/_ops.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/masked/_ops.py',
   'DATA'),
  ('torch/masked/_docs.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/masked/_docs.py',
   'DATA'),
  ('torch/jit/unsupported_tensor_ops.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/jit/unsupported_tensor_ops.py',
   'DATA'),
  ('torch/jit/supported_ops.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/jit/supported_ops.py',
   'DATA'),
  ('torch/jit/quantized.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/jit/quantized.py',
   'DATA'),
  ('torch/jit/mobile/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/jit/mobile/__init__.py',
   'DATA'),
  ('torch/jit/generate_bytecode.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/jit/generate_bytecode.py',
   'DATA'),
  ('torch/jit/frontend.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/jit/frontend.py',
   'DATA'),
  ('torch/jit/annotations.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/jit/annotations.py',
   'DATA'),
  ('torch/jit/_trace.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/jit/_trace.py',
   'DATA'),
  ('torch/jit/_state.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/jit/_state.py',
   'DATA'),
  ('torch/jit/_shape_functions.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/jit/_shape_functions.py',
   'DATA'),
  ('torch/jit/_serialization.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/jit/_serialization.py',
   'DATA'),
  ('torch/jit/_script.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/jit/_script.py',
   'DATA'),
  ('torch/jit/_recursive.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/jit/_recursive.py',
   'DATA'),
  ('torch/jit/_pickle.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/jit/_pickle.py',
   'DATA'),
  ('torch/jit/_passes/_property_propagation.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/jit/_passes/_property_propagation.py',
   'DATA'),
  ('torch/jit/_passes/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/jit/_passes/__init__.py',
   'DATA'),
  ('torch/jit/_monkeytype_config.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/jit/_monkeytype_config.py',
   'DATA'),
  ('torch/jit/_logging.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/jit/_logging.py',
   'DATA'),
  ('torch/jit/_ir_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/jit/_ir_utils.py',
   'DATA'),
  ('torch/jit/_fuser.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/jit/_fuser.py',
   'DATA'),
  ('torch/jit/_freeze.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/jit/_freeze.py',
   'DATA'),
  ('torch/jit/_decompositions.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/jit/_decompositions.py',
   'DATA'),
  ('torch/jit/_decomposition_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/jit/_decomposition_utils.py',
   'DATA'),
  ('torch/jit/_dataclass_impls.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/jit/_dataclass_impls.py',
   'DATA'),
  ('torch/jit/_check.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/jit/_check.py',
   'DATA'),
  ('torch/jit/_builtins.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/jit/_builtins.py',
   'DATA'),
  ('torch/jit/_await.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/jit/_await.py',
   'DATA'),
  ('torch/jit/_async.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/jit/_async.py',
   'DATA'),
  ('torch/fx/traceback.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/traceback.py',
   'DATA'),
  ('torch/fx/tensor_type.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/tensor_type.py',
   'DATA'),
  ('torch/fx/subgraph_rewriter.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/subgraph_rewriter.py',
   'DATA'),
  ('torch/fx/proxy.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/proxy.py',
   'DATA'),
  ('torch/fx/passes/utils/source_matcher_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/passes/utils/source_matcher_utils.py',
   'DATA'),
  ('torch/fx/passes/utils/matcher_with_name_node_map_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/passes/utils/matcher_with_name_node_map_utils.py',
   'DATA'),
  ('torch/fx/passes/utils/matcher_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/passes/utils/matcher_utils.py',
   'DATA'),
  ('torch/fx/passes/utils/fuser_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/passes/utils/fuser_utils.py',
   'DATA'),
  ('torch/fx/passes/utils/common.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/passes/utils/common.py',
   'DATA'),
  ('torch/fx/passes/utils/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/passes/utils/__init__.py',
   'DATA'),
  ('torch/fx/passes/tools_common.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/passes/tools_common.py',
   'DATA'),
  ('torch/fx/passes/tests/test_pass_manager.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/passes/tests/test_pass_manager.py',
   'DATA'),
  ('torch/fx/passes/tests/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/passes/tests/__init__.py',
   'DATA'),
  ('torch/fx/passes/splitter_base.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/passes/splitter_base.py',
   'DATA'),
  ('torch/fx/passes/split_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/passes/split_utils.py',
   'DATA'),
  ('torch/fx/passes/split_module.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/passes/split_module.py',
   'DATA'),
  ('torch/fx/passes/shape_prop.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/passes/shape_prop.py',
   'DATA'),
  ('torch/fx/passes/runtime_assert.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/passes/runtime_assert.py',
   'DATA'),
  ('torch/fx/passes/reinplace.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/passes/reinplace.py',
   'DATA'),
  ('torch/fx/passes/pass_manager.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/passes/pass_manager.py',
   'DATA'),
  ('torch/fx/passes/param_fetch.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/passes/param_fetch.py',
   'DATA'),
  ('torch/fx/passes/operator_support.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/passes/operator_support.py',
   'DATA'),
  ('torch/fx/passes/net_min_base.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/passes/net_min_base.py',
   'DATA'),
  ('torch/fx/passes/infra/pass_manager.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/passes/infra/pass_manager.py',
   'DATA'),
  ('torch/fx/passes/infra/pass_base.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/passes/infra/pass_base.py',
   'DATA'),
  ('torch/fx/passes/infra/partitioner.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/passes/infra/partitioner.py',
   'DATA'),
  ('torch/fx/passes/infra/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/passes/infra/__init__.py',
   'DATA'),
  ('torch/fx/passes/graph_transform_observer.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/passes/graph_transform_observer.py',
   'DATA'),
  ('torch/fx/passes/graph_manipulation.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/passes/graph_manipulation.py',
   'DATA'),
  ('torch/fx/passes/graph_drawer.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/passes/graph_drawer.py',
   'DATA'),
  ('torch/fx/passes/fake_tensor_prop.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/passes/fake_tensor_prop.py',
   'DATA'),
  ('torch/fx/passes/dialect/common/cse_pass.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/passes/dialect/common/cse_pass.py',
   'DATA'),
  ('torch/fx/passes/dialect/common/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/passes/dialect/common/__init__.py',
   'DATA'),
  ('torch/fx/passes/dialect/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/passes/dialect/__init__.py',
   'DATA'),
  ('torch/fx/passes/backends/cudagraphs.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/passes/backends/cudagraphs.py',
   'DATA'),
  ('torch/fx/passes/backends/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/passes/backends/__init__.py',
   'DATA'),
  ('torch/fx/passes/annotate_getitem_nodes.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/passes/annotate_getitem_nodes.py',
   'DATA'),
  ('torch/fx/passes/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/passes/__init__.py',
   'DATA'),
  ('torch/fx/operator_schemas.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/operator_schemas.py',
   'DATA'),
  ('torch/fx/node.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/node.py',
   'DATA'),
  ('torch/fx/interpreter.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/interpreter.py',
   'DATA'),
  ('torch/fx/immutable_collections.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/immutable_collections.py',
   'DATA'),
  ('torch/fx/graph_module.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/graph_module.py',
   'DATA'),
  ('torch/fx/graph.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/graph.py',
   'DATA'),
  ('torch/fx/experimental/validator.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/experimental/validator.py',
   'DATA'),
  ('torch/fx/experimental/unify_refinements.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/experimental/unify_refinements.py',
   'DATA'),
  ('torch/fx/experimental/unification/variable.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/experimental/unification/variable.py',
   'DATA'),
  ('torch/fx/experimental/unification/utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/experimental/unification/utils.py',
   'DATA'),
  ('torch/fx/experimental/unification/unification_tools.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/experimental/unification/unification_tools.py',
   'DATA'),
  ('torch/fx/experimental/unification/multipledispatch/variadic.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/experimental/unification/multipledispatch/variadic.py',
   'DATA'),
  ('torch/fx/experimental/unification/multipledispatch/utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/experimental/unification/multipledispatch/utils.py',
   'DATA'),
  ('torch/fx/experimental/unification/multipledispatch/dispatcher.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/experimental/unification/multipledispatch/dispatcher.py',
   'DATA'),
  ('torch/fx/experimental/unification/multipledispatch/core.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/experimental/unification/multipledispatch/core.py',
   'DATA'),
  ('torch/fx/experimental/unification/multipledispatch/conflict.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/experimental/unification/multipledispatch/conflict.py',
   'DATA'),
  ('torch/fx/experimental/unification/multipledispatch/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/experimental/unification/multipledispatch/__init__.py',
   'DATA'),
  ('torch/fx/experimental/unification/more.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/experimental/unification/more.py',
   'DATA'),
  ('torch/fx/experimental/unification/match.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/experimental/unification/match.py',
   'DATA'),
  ('torch/fx/experimental/unification/dispatch.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/experimental/unification/dispatch.py',
   'DATA'),
  ('torch/fx/experimental/unification/core.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/experimental/unification/core.py',
   'DATA'),
  ('torch/fx/experimental/unification/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/experimental/unification/__init__.py',
   'DATA'),
  ('torch/fx/experimental/schema_type_annotation.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/experimental/schema_type_annotation.py',
   'DATA'),
  ('torch/fx/experimental/rewriter.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/experimental/rewriter.py',
   'DATA'),
  ('torch/fx/experimental/refinement_types.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/experimental/refinement_types.py',
   'DATA'),
  ('torch/fx/experimental/recording.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/experimental/recording.py',
   'DATA'),
  ('torch/fx/experimental/proxy_tensor.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/experimental/proxy_tensor.py',
   'DATA'),
  ('torch/fx/experimental/partitioner_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/experimental/partitioner_utils.py',
   'DATA'),
  ('torch/fx/experimental/optimization.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/experimental/optimization.py',
   'DATA'),
  ('torch/fx/experimental/normalize.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/experimental/normalize.py',
   'DATA'),
  ('torch/fx/experimental/migrate_gradual_types/z3_types.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/experimental/migrate_gradual_types/z3_types.py',
   'DATA'),
  ('torch/fx/experimental/migrate_gradual_types/util.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/experimental/migrate_gradual_types/util.py',
   'DATA'),
  ('torch/fx/experimental/migrate_gradual_types/transform_to_z3.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/experimental/migrate_gradual_types/transform_to_z3.py',
   'DATA'),
  ('torch/fx/experimental/migrate_gradual_types/operation.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/experimental/migrate_gradual_types/operation.py',
   'DATA'),
  ('torch/fx/experimental/migrate_gradual_types/constraint_transformation.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/experimental/migrate_gradual_types/constraint_transformation.py',
   'DATA'),
  ('torch/fx/experimental/migrate_gradual_types/constraint_generator.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/experimental/migrate_gradual_types/constraint_generator.py',
   'DATA'),
  ('torch/fx/experimental/migrate_gradual_types/constraint.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/experimental/migrate_gradual_types/constraint.py',
   'DATA'),
  ('torch/fx/experimental/migrate_gradual_types/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/experimental/migrate_gradual_types/__init__.py',
   'DATA'),
  ('torch/fx/experimental/meta_tracer.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/experimental/meta_tracer.py',
   'DATA'),
  ('torch/fx/experimental/merge_matmul.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/experimental/merge_matmul.py',
   'DATA'),
  ('torch/fx/experimental/graph_gradual_typechecker.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/experimental/graph_gradual_typechecker.py',
   'DATA'),
  ('torch/fx/experimental/debug.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/experimental/debug.py',
   'DATA'),
  ('torch/fx/experimental/const_fold.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/experimental/const_fold.py',
   'DATA'),
  ('torch/fx/experimental/accelerator_partitioner.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/experimental/accelerator_partitioner.py',
   'DATA'),
  ('torch/fx/experimental/_config.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/experimental/_config.py',
   'DATA'),
  ('torch/fx/experimental/_backward_state.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/experimental/_backward_state.py',
   'DATA'),
  ('torch/fx/experimental/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/experimental/__init__.py',
   'DATA'),
  ('torch/fx/config.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/config.py',
   'DATA'),
  ('torch/fx/annotate.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/annotate.py',
   'DATA'),
  ('torch/fx/_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/_utils.py',
   'DATA'),
  ('torch/fx/_symbolic_trace.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/_symbolic_trace.py',
   'DATA'),
  ('torch/fx/_pytree.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/_pytree.py',
   'DATA'),
  ('torch/fx/_lazy_graph_module.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/_lazy_graph_module.py',
   'DATA'),
  ('torch/fx/_compatibility.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/_compatibility.py',
   'DATA'),
  ('torch/fx/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/__init__.py',
   'DATA'),
  ('torch/export/unflatten.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/export/unflatten.py',
   'DATA'),
  ('torch/export/passes/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/export/passes/__init__.py',
   'DATA'),
  ('torch/export/graph_signature.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/export/graph_signature.py',
   'DATA'),
  ('torch/export/exported_program.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/export/exported_program.py',
   'DATA'),
  ('torch/export/experimental/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/export/experimental/__init__.py',
   'DATA'),
  ('torch/export/dynamic_shapes.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/export/dynamic_shapes.py',
   'DATA'),
  ('torch/export/custom_obj.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/export/custom_obj.py',
   'DATA'),
  ('torch/export/_unlift.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/export/_unlift.py',
   'DATA'),
  ('torch/export/_tree_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/export/_tree_utils.py',
   'DATA'),
  ('torch/export/_trace.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/export/_trace.py',
   'DATA'),
  ('torch/export/_safeguard.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/export/_safeguard.py',
   'DATA'),
  ('torch/export/_remove_effect_tokens_pass.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/export/_remove_effect_tokens_pass.py',
   'DATA'),
  ('torch/export/_remove_auto_functionalized_pass.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/export/_remove_auto_functionalized_pass.py',
   'DATA'),
  ('torch/distributions/wishart.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributions/wishart.py',
   'DATA'),
  ('torch/distributions/weibull.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributions/weibull.py',
   'DATA'),
  ('torch/distributions/von_mises.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributions/von_mises.py',
   'DATA'),
  ('torch/distributions/utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributions/utils.py',
   'DATA'),
  ('torch/distributions/uniform.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributions/uniform.py',
   'DATA'),
  ('torch/distributions/transforms.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributions/transforms.py',
   'DATA'),
  ('torch/distributions/transformed_distribution.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributions/transformed_distribution.py',
   'DATA'),
  ('torch/distributions/studentT.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributions/studentT.py',
   'DATA'),
  ('torch/distributions/relaxed_categorical.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributions/relaxed_categorical.py',
   'DATA'),
  ('torch/distributions/relaxed_bernoulli.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributions/relaxed_bernoulli.py',
   'DATA'),
  ('torch/distributions/poisson.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributions/poisson.py',
   'DATA'),
  ('torch/distributions/pareto.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributions/pareto.py',
   'DATA'),
  ('torch/distributions/one_hot_categorical.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributions/one_hot_categorical.py',
   'DATA'),
  ('torch/distributions/normal.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributions/normal.py',
   'DATA'),
  ('torch/distributions/negative_binomial.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributions/negative_binomial.py',
   'DATA'),
  ('torch/distributions/multivariate_normal.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributions/multivariate_normal.py',
   'DATA'),
  ('torch/distributions/multinomial.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributions/multinomial.py',
   'DATA'),
  ('torch/distributions/mixture_same_family.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributions/mixture_same_family.py',
   'DATA'),
  ('torch/distributions/lowrank_multivariate_normal.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributions/lowrank_multivariate_normal.py',
   'DATA'),
  ('torch/distributions/logistic_normal.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributions/logistic_normal.py',
   'DATA'),
  ('torch/distributions/log_normal.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributions/log_normal.py',
   'DATA'),
  ('torch/distributions/lkj_cholesky.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributions/lkj_cholesky.py',
   'DATA'),
  ('torch/distributions/laplace.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributions/laplace.py',
   'DATA'),
  ('torch/distributions/kumaraswamy.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributions/kumaraswamy.py',
   'DATA'),
  ('torch/distributions/kl.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributions/kl.py',
   'DATA'),
  ('torch/distributions/inverse_gamma.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributions/inverse_gamma.py',
   'DATA'),
  ('torch/distributions/independent.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributions/independent.py',
   'DATA'),
  ('torch/distributions/half_normal.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributions/half_normal.py',
   'DATA'),
  ('torch/distributions/half_cauchy.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributions/half_cauchy.py',
   'DATA'),
  ('torch/distributions/gumbel.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributions/gumbel.py',
   'DATA'),
  ('torch/distributions/geometric.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributions/geometric.py',
   'DATA'),
  ('torch/distributions/gamma.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributions/gamma.py',
   'DATA'),
  ('torch/distributions/fishersnedecor.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributions/fishersnedecor.py',
   'DATA'),
  ('torch/distributions/exponential.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributions/exponential.py',
   'DATA'),
  ('torch/distributions/exp_family.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributions/exp_family.py',
   'DATA'),
  ('torch/distributions/distribution.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributions/distribution.py',
   'DATA'),
  ('torch/distributions/dirichlet.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributions/dirichlet.py',
   'DATA'),
  ('torch/distributions/continuous_bernoulli.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributions/continuous_bernoulli.py',
   'DATA'),
  ('torch/distributions/constraints.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributions/constraints.py',
   'DATA'),
  ('torch/distributions/constraint_registry.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributions/constraint_registry.py',
   'DATA'),
  ('torch/distributions/chi2.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributions/chi2.py',
   'DATA'),
  ('torch/distributions/cauchy.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributions/cauchy.py',
   'DATA'),
  ('torch/distributions/categorical.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributions/categorical.py',
   'DATA'),
  ('torch/distributions/binomial.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributions/binomial.py',
   'DATA'),
  ('torch/distributions/beta.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributions/beta.py',
   'DATA'),
  ('torch/distributions/bernoulli.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributions/bernoulli.py',
   'DATA'),
  ('torch/distributed/utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/utils.py',
   'DATA'),
  ('torch/distributed/tensor/placement_types.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/tensor/placement_types.py',
   'DATA'),
  ('torch/distributed/tensor/parallel/style.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/tensor/parallel/style.py',
   'DATA'),
  ('torch/distributed/tensor/parallel/loss.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/tensor/parallel/loss.py',
   'DATA'),
  ('torch/distributed/tensor/parallel/input_reshard.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/tensor/parallel/input_reshard.py',
   'DATA'),
  ('torch/distributed/tensor/parallel/fsdp.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/tensor/parallel/fsdp.py',
   'DATA'),
  ('torch/distributed/tensor/parallel/ddp.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/tensor/parallel/ddp.py',
   'DATA'),
  ('torch/distributed/tensor/parallel/api.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/tensor/parallel/api.py',
   'DATA'),
  ('torch/distributed/tensor/parallel/_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/tensor/parallel/_utils.py',
   'DATA'),
  ('torch/distributed/tensor/parallel/_data_parallel_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/tensor/parallel/_data_parallel_utils.py',
   'DATA'),
  ('torch/distributed/tensor/parallel/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/tensor/parallel/__init__.py',
   'DATA'),
  ('torch/distributed/tensor/experimental/_tp_transform.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/tensor/experimental/_tp_transform.py',
   'DATA'),
  ('torch/distributed/tensor/experimental/_register_sharding.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/tensor/experimental/_register_sharding.py',
   'DATA'),
  ('torch/distributed/tensor/experimental/_func_map.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/tensor/experimental/_func_map.py',
   'DATA'),
  ('torch/distributed/tensor/experimental/_attention.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/tensor/experimental/_attention.py',
   'DATA'),
  ('torch/distributed/tensor/experimental/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/tensor/experimental/__init__.py',
   'DATA'),
  ('torch/distributed/tensor/device_mesh.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/tensor/device_mesh.py',
   'DATA'),
  ('torch/distributed/tensor/debug/_visualize_sharding.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/tensor/debug/_visualize_sharding.py',
   'DATA'),
  ('torch/distributed/tensor/debug/_op_coverage.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/tensor/debug/_op_coverage.py',
   'DATA'),
  ('torch/distributed/tensor/debug/_comm_mode.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/tensor/debug/_comm_mode.py',
   'DATA'),
  ('torch/distributed/tensor/debug/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/tensor/debug/__init__.py',
   'DATA'),
  ('torch/distributed/tensor/_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/tensor/_utils.py',
   'DATA'),
  ('torch/distributed/tensor/_tp_conv.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/tensor/_tp_conv.py',
   'DATA'),
  ('torch/distributed/tensor/_shards_wrapper.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/tensor/_shards_wrapper.py',
   'DATA'),
  ('torch/distributed/tensor/_sharding_prop.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/tensor/_sharding_prop.py',
   'DATA'),
  ('torch/distributed/tensor/_redistribute.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/tensor/_redistribute.py',
   'DATA'),
  ('torch/distributed/tensor/_random.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/tensor/_random.py',
   'DATA'),
  ('torch/distributed/tensor/_ops/utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/tensor/_ops/utils.py',
   'DATA'),
  ('torch/distributed/tensor/_ops/_view_ops.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/tensor/_ops/_view_ops.py',
   'DATA'),
  ('torch/distributed/tensor/_ops/_tensor_ops.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/tensor/_ops/_tensor_ops.py',
   'DATA'),
  ('torch/distributed/tensor/_ops/_random_ops.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/tensor/_ops/_random_ops.py',
   'DATA'),
  ('torch/distributed/tensor/_ops/_pointwise_ops.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/tensor/_ops/_pointwise_ops.py',
   'DATA'),
  ('torch/distributed/tensor/_ops/_matrix_ops.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/tensor/_ops/_matrix_ops.py',
   'DATA'),
  ('torch/distributed/tensor/_ops/_math_ops.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/tensor/_ops/_math_ops.py',
   'DATA'),
  ('torch/distributed/tensor/_ops/_experimental_ops.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/tensor/_ops/_experimental_ops.py',
   'DATA'),
  ('torch/distributed/tensor/_ops/_embedding_ops.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/tensor/_ops/_embedding_ops.py',
   'DATA'),
  ('torch/distributed/tensor/_ops/_einsum_strategy.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/tensor/_ops/_einsum_strategy.py',
   'DATA'),
  ('torch/distributed/tensor/_ops/_conv_ops.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/tensor/_ops/_conv_ops.py',
   'DATA'),
  ('torch/distributed/tensor/_ops/_common_rules.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/tensor/_ops/_common_rules.py',
   'DATA'),
  ('torch/distributed/tensor/_ops/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/tensor/_ops/__init__.py',
   'DATA'),
  ('torch/distributed/tensor/_op_schema.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/tensor/_op_schema.py',
   'DATA'),
  ('torch/distributed/tensor/_dtensor_spec.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/tensor/_dtensor_spec.py',
   'DATA'),
  ('torch/distributed/tensor/_dispatch.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/tensor/_dispatch.py',
   'DATA'),
  ('torch/distributed/tensor/_collective_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/tensor/_collective_utils.py',
   'DATA'),
  ('torch/distributed/tensor/_api.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/tensor/_api.py',
   'DATA'),
  ('torch/distributed/tensor/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/tensor/__init__.py',
   'DATA'),
  ('torch/distributed/run.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/run.py',
   'DATA'),
  ('torch/distributed/rpc/server_process_global_profiler.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/rpc/server_process_global_profiler.py',
   'DATA'),
  ('torch/distributed/rpc/rref_proxy.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/rpc/rref_proxy.py',
   'DATA'),
  ('torch/distributed/rpc/options.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/rpc/options.py',
   'DATA'),
  ('torch/distributed/rpc/internal.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/rpc/internal.py',
   'DATA'),
  ('torch/distributed/rpc/functions.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/rpc/functions.py',
   'DATA'),
  ('torch/distributed/rpc/constants.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/rpc/constants.py',
   'DATA'),
  ('torch/distributed/rpc/backend_registry.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/rpc/backend_registry.py',
   'DATA'),
  ('torch/distributed/rpc/api.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/rpc/api.py',
   'DATA'),
  ('torch/distributed/rpc/_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/rpc/_utils.py',
   'DATA'),
  ('torch/distributed/rpc/_testing/faulty_agent_backend_registry.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/rpc/_testing/faulty_agent_backend_registry.py',
   'DATA'),
  ('torch/distributed/rpc/_testing/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/rpc/_testing/__init__.py',
   'DATA'),
  ('torch/distributed/rpc/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/rpc/__init__.py',
   'DATA'),
  ('torch/distributed/rendezvous.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/rendezvous.py',
   'DATA'),
  ('torch/distributed/remote_device.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/remote_device.py',
   'DATA'),
  ('torch/distributed/pipelining/stage.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/pipelining/stage.py',
   'DATA'),
  ('torch/distributed/pipelining/schedules.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/pipelining/schedules.py',
   'DATA'),
  ('torch/distributed/pipelining/microbatch.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/pipelining/microbatch.py',
   'DATA'),
  ('torch/distributed/pipelining/_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/pipelining/_utils.py',
   'DATA'),
  ('torch/distributed/pipelining/_unflatten.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/pipelining/_unflatten.py',
   'DATA'),
  ('torch/distributed/pipelining/_debug.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/pipelining/_debug.py',
   'DATA'),
  ('torch/distributed/pipelining/_backward.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/pipelining/_backward.py',
   'DATA'),
  ('torch/distributed/pipelining/_IR.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/pipelining/_IR.py',
   'DATA'),
  ('torch/distributed/pipelining/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/pipelining/__init__.py',
   'DATA'),
  ('torch/distributed/optim/zero_redundancy_optimizer.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/optim/zero_redundancy_optimizer.py',
   'DATA'),
  ('torch/distributed/optim/utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/optim/utils.py',
   'DATA'),
  ('torch/distributed/optim/post_localSGD_optimizer.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/optim/post_localSGD_optimizer.py',
   'DATA'),
  ('torch/distributed/optim/optimizer.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/optim/optimizer.py',
   'DATA'),
  ('torch/distributed/optim/named_optimizer.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/optim/named_optimizer.py',
   'DATA'),
  ('torch/distributed/optim/functional_sgd.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/optim/functional_sgd.py',
   'DATA'),
  ('torch/distributed/optim/functional_rprop.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/optim/functional_rprop.py',
   'DATA'),
  ('torch/distributed/optim/functional_rmsprop.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/optim/functional_rmsprop.py',
   'DATA'),
  ('torch/distributed/optim/functional_adamw.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/optim/functional_adamw.py',
   'DATA'),
  ('torch/distributed/optim/functional_adamax.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/optim/functional_adamax.py',
   'DATA'),
  ('torch/distributed/optim/functional_adam.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/optim/functional_adam.py',
   'DATA'),
  ('torch/distributed/optim/functional_adagrad.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/optim/functional_adagrad.py',
   'DATA'),
  ('torch/distributed/optim/functional_adadelta.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/optim/functional_adadelta.py',
   'DATA'),
  ('torch/distributed/optim/apply_optimizer_in_backward.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/optim/apply_optimizer_in_backward.py',
   'DATA'),
  ('torch/distributed/optim/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/optim/__init__.py',
   'DATA'),
  ('torch/distributed/nn/jit/templates/remote_module_template.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/nn/jit/templates/remote_module_template.py',
   'DATA'),
  ('torch/distributed/nn/jit/templates/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/nn/jit/templates/__init__.py',
   'DATA'),
  ('torch/distributed/nn/jit/instantiator.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/nn/jit/instantiator.py',
   'DATA'),
  ('torch/distributed/nn/jit/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/nn/jit/__init__.py',
   'DATA'),
  ('torch/distributed/nn/functional.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/nn/functional.py',
   'DATA'),
  ('torch/distributed/nn/api/remote_module.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/nn/api/remote_module.py',
   'DATA'),
  ('torch/distributed/nn/api/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/nn/api/__init__.py',
   'DATA'),
  ('torch/distributed/nn/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/nn/__init__.py',
   'DATA'),
  ('torch/distributed/logging_handlers.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/logging_handlers.py',
   'DATA'),
  ('torch/distributed/launcher/api.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/launcher/api.py',
   'DATA'),
  ('torch/distributed/launcher/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/launcher/__init__.py',
   'DATA'),
  ('torch/distributed/launch.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/launch.py',
   'DATA'),
  ('torch/distributed/fsdp/wrap.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/fsdp/wrap.py',
   'DATA'),
  ('torch/distributed/fsdp/sharded_grad_scaler.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/fsdp/sharded_grad_scaler.py',
   'DATA'),
  ('torch/distributed/fsdp/fully_sharded_data_parallel.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/fsdp/fully_sharded_data_parallel.py',
   'DATA'),
  ('torch/distributed/fsdp/api.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/fsdp/api.py',
   'DATA'),
  ('torch/distributed/fsdp/_wrap_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/fsdp/_wrap_utils.py',
   'DATA'),
  ('torch/distributed/fsdp/_unshard_param_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/fsdp/_unshard_param_utils.py',
   'DATA'),
  ('torch/distributed/fsdp/_traversal_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/fsdp/_traversal_utils.py',
   'DATA'),
  ('torch/distributed/fsdp/_trace_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/fsdp/_trace_utils.py',
   'DATA'),
  ('torch/distributed/fsdp/_state_dict_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/fsdp/_state_dict_utils.py',
   'DATA'),
  ('torch/distributed/fsdp/_shard_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/fsdp/_shard_utils.py',
   'DATA'),
  ('torch/distributed/fsdp/_runtime_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/fsdp/_runtime_utils.py',
   'DATA'),
  ('torch/distributed/fsdp/_optim_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/fsdp/_optim_utils.py',
   'DATA'),
  ('torch/distributed/fsdp/_limiter_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/fsdp/_limiter_utils.py',
   'DATA'),
  ('torch/distributed/fsdp/_init_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/fsdp/_init_utils.py',
   'DATA'),
  ('torch/distributed/fsdp/_fsdp_extensions.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/fsdp/_fsdp_extensions.py',
   'DATA'),
  ('torch/distributed/fsdp/_flat_param.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/fsdp/_flat_param.py',
   'DATA'),
  ('torch/distributed/fsdp/_exec_order_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/fsdp/_exec_order_utils.py',
   'DATA'),
  ('torch/distributed/fsdp/_dynamo_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/fsdp/_dynamo_utils.py',
   'DATA'),
  ('torch/distributed/fsdp/_debug_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/fsdp/_debug_utils.py',
   'DATA'),
  ('torch/distributed/fsdp/_common_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/fsdp/_common_utils.py',
   'DATA'),
  ('torch/distributed/fsdp/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/fsdp/__init__.py',
   'DATA'),
  ('torch/distributed/elastic/utils/store.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/elastic/utils/store.py',
   'DATA'),
  ('torch/distributed/elastic/utils/logging.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/elastic/utils/logging.py',
   'DATA'),
  ('torch/distributed/elastic/utils/log_level.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/elastic/utils/log_level.py',
   'DATA'),
  ('torch/distributed/elastic/utils/distributed.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/elastic/utils/distributed.py',
   'DATA'),
  ('torch/distributed/elastic/utils/data/elastic_distributed_sampler.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/elastic/utils/data/elastic_distributed_sampler.py',
   'DATA'),
  ('torch/distributed/elastic/utils/data/cycling_iterator.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/elastic/utils/data/cycling_iterator.py',
   'DATA'),
  ('torch/distributed/elastic/utils/data/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/elastic/utils/data/__init__.py',
   'DATA'),
  ('torch/distributed/elastic/utils/api.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/elastic/utils/api.py',
   'DATA'),
  ('torch/distributed/elastic/utils/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/elastic/utils/__init__.py',
   'DATA'),
  ('torch/distributed/elastic/timer/local_timer.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/elastic/timer/local_timer.py',
   'DATA'),
  ('torch/distributed/elastic/timer/file_based_local_timer.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/elastic/timer/file_based_local_timer.py',
   'DATA'),
  ('torch/distributed/elastic/timer/debug_info_logging.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/elastic/timer/debug_info_logging.py',
   'DATA'),
  ('torch/distributed/elastic/timer/api.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/elastic/timer/api.py',
   'DATA'),
  ('torch/distributed/elastic/timer/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/elastic/timer/__init__.py',
   'DATA'),
  ('torch/distributed/elastic/rendezvous/utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/elastic/rendezvous/utils.py',
   'DATA'),
  ('torch/distributed/elastic/rendezvous/static_tcp_rendezvous.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/elastic/rendezvous/static_tcp_rendezvous.py',
   'DATA'),
  ('torch/distributed/elastic/rendezvous/registry.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/elastic/rendezvous/registry.py',
   'DATA'),
  ('torch/distributed/elastic/rendezvous/etcd_store.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/elastic/rendezvous/etcd_store.py',
   'DATA'),
  ('torch/distributed/elastic/rendezvous/etcd_server.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/elastic/rendezvous/etcd_server.py',
   'DATA'),
  ('torch/distributed/elastic/rendezvous/etcd_rendezvous_backend.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/elastic/rendezvous/etcd_rendezvous_backend.py',
   'DATA'),
  ('torch/distributed/elastic/rendezvous/etcd_rendezvous.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/elastic/rendezvous/etcd_rendezvous.py',
   'DATA'),
  ('torch/distributed/elastic/rendezvous/dynamic_rendezvous.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/elastic/rendezvous/dynamic_rendezvous.py',
   'DATA'),
  ('torch/distributed/elastic/rendezvous/c10d_rendezvous_backend.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/elastic/rendezvous/c10d_rendezvous_backend.py',
   'DATA'),
  ('torch/distributed/elastic/rendezvous/api.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/elastic/rendezvous/api.py',
   'DATA'),
  ('torch/distributed/elastic/rendezvous/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/elastic/rendezvous/__init__.py',
   'DATA'),
  ('torch/distributed/elastic/multiprocessing/tail_log.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/elastic/multiprocessing/tail_log.py',
   'DATA'),
  ('torch/distributed/elastic/multiprocessing/subprocess_handler/subprocess_handler.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/elastic/multiprocessing/subprocess_handler/subprocess_handler.py',
   'DATA'),
  ('torch/distributed/elastic/multiprocessing/subprocess_handler/handlers.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/elastic/multiprocessing/subprocess_handler/handlers.py',
   'DATA'),
  ('torch/distributed/elastic/multiprocessing/subprocess_handler/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/elastic/multiprocessing/subprocess_handler/__init__.py',
   'DATA'),
  ('torch/distributed/elastic/multiprocessing/redirects.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/elastic/multiprocessing/redirects.py',
   'DATA'),
  ('torch/distributed/elastic/multiprocessing/errors/handlers.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/elastic/multiprocessing/errors/handlers.py',
   'DATA'),
  ('torch/distributed/elastic/multiprocessing/errors/error_handler.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/elastic/multiprocessing/errors/error_handler.py',
   'DATA'),
  ('torch/distributed/elastic/multiprocessing/errors/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/elastic/multiprocessing/errors/__init__.py',
   'DATA'),
  ('torch/distributed/elastic/multiprocessing/api.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/elastic/multiprocessing/api.py',
   'DATA'),
  ('torch/distributed/elastic/multiprocessing/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/elastic/multiprocessing/__init__.py',
   'DATA'),
  ('torch/distributed/elastic/metrics/api.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/elastic/metrics/api.py',
   'DATA'),
  ('torch/distributed/elastic/metrics/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/elastic/metrics/__init__.py',
   'DATA'),
  ('torch/distributed/elastic/events/handlers.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/elastic/events/handlers.py',
   'DATA'),
  ('torch/distributed/elastic/events/api.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/elastic/events/api.py',
   'DATA'),
  ('torch/distributed/elastic/events/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/elastic/events/__init__.py',
   'DATA'),
  ('torch/distributed/elastic/control_plane.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/elastic/control_plane.py',
   'DATA'),
  ('torch/distributed/elastic/agent/server/local_elastic_agent.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/elastic/agent/server/local_elastic_agent.py',
   'DATA'),
  ('torch/distributed/elastic/agent/server/health_check_server.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/elastic/agent/server/health_check_server.py',
   'DATA'),
  ('torch/distributed/elastic/agent/server/api.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/elastic/agent/server/api.py',
   'DATA'),
  ('torch/distributed/elastic/agent/server/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/elastic/agent/server/__init__.py',
   'DATA'),
  ('torch/distributed/elastic/agent/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/elastic/agent/__init__.py',
   'DATA'),
  ('torch/distributed/elastic/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/elastic/__init__.py',
   'DATA'),
  ('torch/distributed/distributed_c10d.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/distributed_c10d.py',
   'DATA'),
  ('torch/distributed/device_mesh.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/device_mesh.py',
   'DATA'),
  ('torch/distributed/constants.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/constants.py',
   'DATA'),
  ('torch/distributed/collective_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/collective_utils.py',
   'DATA'),
  ('torch/distributed/checkpoint/utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/checkpoint/utils.py',
   'DATA'),
  ('torch/distributed/checkpoint/storage.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/checkpoint/storage.py',
   'DATA'),
  ('torch/distributed/checkpoint/stateful.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/checkpoint/stateful.py',
   'DATA'),
  ('torch/distributed/checkpoint/state_dict_saver.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/checkpoint/state_dict_saver.py',
   'DATA'),
  ('torch/distributed/checkpoint/state_dict_loader.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/checkpoint/state_dict_loader.py',
   'DATA'),
  ('torch/distributed/checkpoint/state_dict.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/checkpoint/state_dict.py',
   'DATA'),
  ('torch/distributed/checkpoint/staging.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/checkpoint/staging.py',
   'DATA'),
  ('torch/distributed/checkpoint/resharding.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/checkpoint/resharding.py',
   'DATA'),
  ('torch/distributed/checkpoint/planner_helpers.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/checkpoint/planner_helpers.py',
   'DATA'),
  ('torch/distributed/checkpoint/planner.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/checkpoint/planner.py',
   'DATA'),
  ('torch/distributed/checkpoint/optimizer.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/checkpoint/optimizer.py',
   'DATA'),
  ('torch/distributed/checkpoint/metadata.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/checkpoint/metadata.py',
   'DATA'),
  ('torch/distributed/checkpoint/logging_handlers.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/checkpoint/logging_handlers.py',
   'DATA'),
  ('torch/distributed/checkpoint/logger.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/checkpoint/logger.py',
   'DATA'),
  ('torch/distributed/checkpoint/format_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/checkpoint/format_utils.py',
   'DATA'),
  ('torch/distributed/checkpoint/filesystem.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/checkpoint/filesystem.py',
   'DATA'),
  ('torch/distributed/checkpoint/default_planner.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/checkpoint/default_planner.py',
   'DATA'),
  ('torch/distributed/checkpoint/api.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/checkpoint/api.py',
   'DATA'),
  ('torch/distributed/checkpoint/_version.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/checkpoint/_version.py',
   'DATA'),
  ('torch/distributed/checkpoint/_traverse.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/checkpoint/_traverse.py',
   'DATA'),
  ('torch/distributed/checkpoint/_storage_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/checkpoint/_storage_utils.py',
   'DATA'),
  ('torch/distributed/checkpoint/_sharded_tensor_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/checkpoint/_sharded_tensor_utils.py',
   'DATA'),
  ('torch/distributed/checkpoint/_nested_dict.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/checkpoint/_nested_dict.py',
   'DATA'),
  ('torch/distributed/checkpoint/_fsspec_filesystem.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/checkpoint/_fsspec_filesystem.py',
   'DATA'),
  ('torch/distributed/checkpoint/_dedup_tensors.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/checkpoint/_dedup_tensors.py',
   'DATA'),
  ('torch/distributed/checkpoint/_dedup_save_plans.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/checkpoint/_dedup_save_plans.py',
   'DATA'),
  ('torch/distributed/checkpoint/_checkpointer.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/checkpoint/_checkpointer.py',
   'DATA'),
  ('torch/distributed/checkpoint/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/checkpoint/__init__.py',
   'DATA'),
  ('torch/distributed/c10d_logger.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/c10d_logger.py',
   'DATA'),
  ('torch/distributed/autograd/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/autograd/__init__.py',
   'DATA'),
  ('torch/distributed/argparse_util.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/argparse_util.py',
   'DATA'),
  ('torch/distributed/algorithms/model_averaging/utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/algorithms/model_averaging/utils.py',
   'DATA'),
  ('torch/distributed/algorithms/model_averaging/hierarchical_model_averager.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/algorithms/model_averaging/hierarchical_model_averager.py',
   'DATA'),
  ('torch/distributed/algorithms/model_averaging/averagers.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/algorithms/model_averaging/averagers.py',
   'DATA'),
  ('torch/distributed/algorithms/model_averaging/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/algorithms/model_averaging/__init__.py',
   'DATA'),
  ('torch/distributed/algorithms/join.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/algorithms/join.py',
   'DATA'),
  ('torch/distributed/algorithms/ddp_comm_hooks/quantization_hooks.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/algorithms/ddp_comm_hooks/quantization_hooks.py',
   'DATA'),
  ('torch/distributed/algorithms/ddp_comm_hooks/powerSGD_hook.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/algorithms/ddp_comm_hooks/powerSGD_hook.py',
   'DATA'),
  ('torch/distributed/algorithms/ddp_comm_hooks/post_localSGD_hook.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/algorithms/ddp_comm_hooks/post_localSGD_hook.py',
   'DATA'),
  ('torch/distributed/algorithms/ddp_comm_hooks/optimizer_overlap_hooks.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/algorithms/ddp_comm_hooks/optimizer_overlap_hooks.py',
   'DATA'),
  ('torch/distributed/algorithms/ddp_comm_hooks/mixed_precision_hooks.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/algorithms/ddp_comm_hooks/mixed_precision_hooks.py',
   'DATA'),
  ('torch/distributed/algorithms/ddp_comm_hooks/default_hooks.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/algorithms/ddp_comm_hooks/default_hooks.py',
   'DATA'),
  ('torch/distributed/algorithms/ddp_comm_hooks/debugging_hooks.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/algorithms/ddp_comm_hooks/debugging_hooks.py',
   'DATA'),
  ('torch/distributed/algorithms/ddp_comm_hooks/ddp_zero_hook.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/algorithms/ddp_comm_hooks/ddp_zero_hook.py',
   'DATA'),
  ('torch/distributed/algorithms/ddp_comm_hooks/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/algorithms/ddp_comm_hooks/__init__.py',
   'DATA'),
  ('torch/distributed/algorithms/_quantization/quantization.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/algorithms/_quantization/quantization.py',
   'DATA'),
  ('torch/distributed/algorithms/_quantization/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/algorithms/_quantization/__init__.py',
   'DATA'),
  ('torch/distributed/algorithms/_optimizer_overlap/optimizer_overlap.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/algorithms/_optimizer_overlap/optimizer_overlap.py',
   'DATA'),
  ('torch/distributed/algorithms/_optimizer_overlap/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/algorithms/_optimizer_overlap/__init__.py',
   'DATA'),
  ('torch/distributed/algorithms/_comm_hooks/default_hooks.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/algorithms/_comm_hooks/default_hooks.py',
   'DATA'),
  ('torch/distributed/algorithms/_comm_hooks/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/algorithms/_comm_hooks/__init__.py',
   'DATA'),
  ('torch/distributed/algorithms/_checkpoint/checkpoint_wrapper.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/algorithms/_checkpoint/checkpoint_wrapper.py',
   'DATA'),
  ('torch/distributed/algorithms/_checkpoint/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/algorithms/_checkpoint/__init__.py',
   'DATA'),
  ('torch/distributed/algorithms/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/algorithms/__init__.py',
   'DATA'),
  ('torch/distributed/_tools/runtime_estimator.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/_tools/runtime_estimator.py',
   'DATA'),
  ('torch/distributed/_tools/mod_tracker.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/_tools/mod_tracker.py',
   'DATA'),
  ('torch/distributed/_tools/memory_tracker.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/_tools/memory_tracker.py',
   'DATA'),
  ('torch/distributed/_tools/mem_tracker.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/_tools/mem_tracker.py',
   'DATA'),
  ('torch/distributed/_tools/fsdp2_mem_tracker.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/_tools/fsdp2_mem_tracker.py',
   'DATA'),
  ('torch/distributed/_tools/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/_tools/__init__.py',
   'DATA'),
  ('torch/distributed/_tensor/placement_types.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/_tensor/placement_types.py',
   'DATA'),
  ('torch/distributed/_tensor/api.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/_tensor/api.py',
   'DATA'),
  ('torch/distributed/_tensor/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/_tensor/__init__.py',
   'DATA'),
  ('torch/distributed/_symmetric_memory/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/_symmetric_memory/__init__.py',
   'DATA'),
  ('torch/distributed/_state_dict_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/_state_dict_utils.py',
   'DATA'),
  ('torch/distributed/_sharding_spec/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/_sharding_spec/__init__.py',
   'DATA'),
  ('torch/distributed/_sharded_tensor/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/_sharded_tensor/__init__.py',
   'DATA'),
  ('torch/distributed/_shard/sharding_spec/chunk_sharding_spec_ops/embedding_bag.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/_shard/sharding_spec/chunk_sharding_spec_ops/embedding_bag.py',
   'DATA'),
  ('torch/distributed/_shard/sharding_spec/chunk_sharding_spec_ops/embedding.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/_shard/sharding_spec/chunk_sharding_spec_ops/embedding.py',
   'DATA'),
  ('torch/distributed/_shard/sharding_spec/chunk_sharding_spec_ops/_common.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/_shard/sharding_spec/chunk_sharding_spec_ops/_common.py',
   'DATA'),
  ('torch/distributed/_shard/sharding_spec/chunk_sharding_spec_ops/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/_shard/sharding_spec/chunk_sharding_spec_ops/__init__.py',
   'DATA'),
  ('torch/distributed/_shard/sharding_spec/chunk_sharding_spec.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/_shard/sharding_spec/chunk_sharding_spec.py',
   'DATA'),
  ('torch/distributed/_shard/sharding_spec/api.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/_shard/sharding_spec/api.py',
   'DATA'),
  ('torch/distributed/_shard/sharding_spec/_internals.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/_shard/sharding_spec/_internals.py',
   'DATA'),
  ('torch/distributed/_shard/sharding_spec/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/_shard/sharding_spec/__init__.py',
   'DATA'),
  ('torch/distributed/_shard/sharding_plan/api.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/_shard/sharding_plan/api.py',
   'DATA'),
  ('torch/distributed/_shard/sharding_plan/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/_shard/sharding_plan/__init__.py',
   'DATA'),
  ('torch/distributed/_shard/sharder.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/_shard/sharder.py',
   'DATA'),
  ('torch/distributed/_shard/sharded_tensor/utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/_shard/sharded_tensor/utils.py',
   'DATA'),
  ('torch/distributed/_shard/sharded_tensor/shard.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/_shard/sharded_tensor/shard.py',
   'DATA'),
  ('torch/distributed/_shard/sharded_tensor/reshard.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/_shard/sharded_tensor/reshard.py',
   'DATA'),
  ('torch/distributed/_shard/sharded_tensor/metadata.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/_shard/sharded_tensor/metadata.py',
   'DATA'),
  ('torch/distributed/_shard/sharded_tensor/logging_handlers.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/_shard/sharded_tensor/logging_handlers.py',
   'DATA'),
  ('torch/distributed/_shard/sharded_tensor/logger.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/_shard/sharded_tensor/logger.py',
   'DATA'),
  ('torch/distributed/_shard/sharded_tensor/api.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/_shard/sharded_tensor/api.py',
   'DATA'),
  ('torch/distributed/_shard/sharded_tensor/_ops/tensor_ops.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/_shard/sharded_tensor/_ops/tensor_ops.py',
   'DATA'),
  ('torch/distributed/_shard/sharded_tensor/_ops/misc_ops.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/_shard/sharded_tensor/_ops/misc_ops.py',
   'DATA'),
  ('torch/distributed/_shard/sharded_tensor/_ops/init.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/_shard/sharded_tensor/_ops/init.py',
   'DATA'),
  ('torch/distributed/_shard/sharded_tensor/_ops/binary_cmp.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/_shard/sharded_tensor/_ops/binary_cmp.py',
   'DATA'),
  ('torch/distributed/_shard/sharded_tensor/_ops/_common.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/_shard/sharded_tensor/_ops/_common.py',
   'DATA'),
  ('torch/distributed/_shard/sharded_tensor/_ops/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/_shard/sharded_tensor/_ops/__init__.py',
   'DATA'),
  ('torch/distributed/_shard/sharded_tensor/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/_shard/sharded_tensor/__init__.py',
   'DATA'),
  ('torch/distributed/_shard/sharded_optim/api.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/_shard/sharded_optim/api.py',
   'DATA'),
  ('torch/distributed/_shard/sharded_optim/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/_shard/sharded_optim/__init__.py',
   'DATA'),
  ('torch/distributed/_shard/op_registry_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/_shard/op_registry_utils.py',
   'DATA'),
  ('torch/distributed/_shard/metadata.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/_shard/metadata.py',
   'DATA'),
  ('torch/distributed/_shard/common_op_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/_shard/common_op_utils.py',
   'DATA'),
  ('torch/distributed/_shard/checkpoint/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/_shard/checkpoint/__init__.py',
   'DATA'),
  ('torch/distributed/_shard/api.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/_shard/api.py',
   'DATA'),
  ('torch/distributed/_shard/_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/_shard/_utils.py',
   'DATA'),
  ('torch/distributed/_shard/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/_shard/__init__.py',
   'DATA'),
  ('torch/distributed/_functional_collectives_impl.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/_functional_collectives_impl.py',
   'DATA'),
  ('torch/distributed/_functional_collectives.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/_functional_collectives.py',
   'DATA'),
  ('torch/distributed/_composable_state.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/_composable_state.py',
   'DATA'),
  ('torch/distributed/_composable/replicate.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/_composable/replicate.py',
   'DATA'),
  ('torch/distributed/_composable/fully_shard.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/_composable/fully_shard.py',
   'DATA'),
  ('torch/distributed/_composable/fsdp/fully_shard.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/_composable/fsdp/fully_shard.py',
   'DATA'),
  ('torch/distributed/_composable/fsdp/_fsdp_state.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/_composable/fsdp/_fsdp_state.py',
   'DATA'),
  ('torch/distributed/_composable/fsdp/_fsdp_param_group.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/_composable/fsdp/_fsdp_param_group.py',
   'DATA'),
  ('torch/distributed/_composable/fsdp/_fsdp_param.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/_composable/fsdp/_fsdp_param.py',
   'DATA'),
  ('torch/distributed/_composable/fsdp/_fsdp_init.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/_composable/fsdp/_fsdp_init.py',
   'DATA'),
  ('torch/distributed/_composable/fsdp/_fsdp_common.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/_composable/fsdp/_fsdp_common.py',
   'DATA'),
  ('torch/distributed/_composable/fsdp/_fsdp_collectives.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/_composable/fsdp/_fsdp_collectives.py',
   'DATA'),
  ('torch/distributed/_composable/fsdp/_fsdp_api.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/_composable/fsdp/_fsdp_api.py',
   'DATA'),
  ('torch/distributed/_composable/fsdp/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/_composable/fsdp/__init__.py',
   'DATA'),
  ('torch/distributed/_composable/contract.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/_composable/contract.py',
   'DATA'),
  ('torch/distributed/_composable/checkpoint_activation.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/_composable/checkpoint_activation.py',
   'DATA'),
  ('torch/distributed/_composable/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/_composable/__init__.py',
   'DATA'),
  ('torch/distributed/_checkpointable.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/_checkpointable.py',
   'DATA'),
  ('torch/cuda/tunable.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/cuda/tunable.py',
   'DATA'),
  ('torch/cuda/streams.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/cuda/streams.py',
   'DATA'),
  ('torch/cuda/sparse.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/cuda/sparse.py',
   'DATA'),
  ('torch/cuda/random.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/cuda/random.py',
   'DATA'),
  ('torch/cuda/profiler.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/cuda/profiler.py',
   'DATA'),
  ('torch/cuda/nvtx.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/cuda/nvtx.py',
   'DATA'),
  ('torch/cuda/nccl.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/cuda/nccl.py',
   'DATA'),
  ('torch/cuda/memory.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/cuda/memory.py',
   'DATA'),
  ('torch/cuda/jiterator.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/cuda/jiterator.py',
   'DATA'),
  ('torch/cuda/graphs.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/cuda/graphs.py',
   'DATA'),
  ('torch/cuda/gds.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/cuda/gds.py',
   'DATA'),
  ('torch/cuda/error.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/cuda/error.py',
   'DATA'),
  ('torch/cuda/comm.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/cuda/comm.py',
   'DATA'),
  ('torch/cuda/amp/grad_scaler.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/cuda/amp/grad_scaler.py',
   'DATA'),
  ('torch/cuda/amp/common.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/cuda/amp/common.py',
   'DATA'),
  ('torch/cuda/amp/autocast_mode.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/cuda/amp/autocast_mode.py',
   'DATA'),
  ('torch/cuda/amp/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/cuda/amp/__init__.py',
   'DATA'),
  ('torch/cuda/_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/cuda/_utils.py',
   'DATA'),
  ('torch/cuda/_memory_viz.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/cuda/_memory_viz.py',
   'DATA'),
  ('torch/cuda/_gpu_trace.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/cuda/_gpu_trace.py',
   'DATA'),
  ('torch/cpu/amp/grad_scaler.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/cpu/amp/grad_scaler.py',
   'DATA'),
  ('torch/cpu/amp/autocast_mode.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/cpu/amp/autocast_mode.py',
   'DATA'),
  ('torch/cpu/amp/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/cpu/amp/__init__.py',
   'DATA'),
  ('torch/contrib/_tensorboard_vis.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/contrib/_tensorboard_vis.py',
   'DATA'),
  ('torch/contrib/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/contrib/__init__.py',
   'DATA'),
  ('torch/backends/xnnpack/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/backends/xnnpack/__init__.py',
   'DATA'),
  ('torch/backends/xeon/run_cpu.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/backends/xeon/run_cpu.py',
   'DATA'),
  ('torch/backends/xeon/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/backends/xeon/__init__.py',
   'DATA'),
  ('torch/backends/quantized/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/backends/quantized/__init__.py',
   'DATA'),
  ('torch/backends/opt_einsum/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/backends/opt_einsum/__init__.py',
   'DATA'),
  ('torch/backends/openmp/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/backends/openmp/__init__.py',
   'DATA'),
  ('torch/backends/nnpack/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/backends/nnpack/__init__.py',
   'DATA'),
  ('torch/backends/mps/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/backends/mps/__init__.py',
   'DATA'),
  ('torch/backends/mkldnn/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/backends/mkldnn/__init__.py',
   'DATA'),
  ('torch/backends/mkl/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/backends/mkl/__init__.py',
   'DATA'),
  ('torch/backends/mha/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/backends/mha/__init__.py',
   'DATA'),
  ('torch/backends/cusparselt/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/backends/cusparselt/__init__.py',
   'DATA'),
  ('torch/backends/cudnn/rnn.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/backends/cudnn/rnn.py',
   'DATA'),
  ('torch/backends/cudnn/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/backends/cudnn/__init__.py',
   'DATA'),
  ('torch/backends/cuda/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/backends/cuda/__init__.py',
   'DATA'),
  ('torch/backends/cpu/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/backends/cpu/__init__.py',
   'DATA'),
  ('torch/backends/_nnapi/serializer.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/backends/_nnapi/serializer.py',
   'DATA'),
  ('torch/backends/_nnapi/prepare.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/backends/_nnapi/prepare.py',
   'DATA'),
  ('torch/backends/_nnapi/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/backends/_nnapi/__init__.py',
   'DATA'),
  ('torch/backends/_coreml/preprocess.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/backends/_coreml/preprocess.py',
   'DATA'),
  ('torch/backends/_coreml/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/backends/_coreml/__init__.py',
   'DATA'),
  ('torch/autograd/variable.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/autograd/variable.py',
   'DATA'),
  ('torch/autograd/profiler_util.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/autograd/profiler_util.py',
   'DATA'),
  ('torch/autograd/profiler_legacy.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/autograd/profiler_legacy.py',
   'DATA'),
  ('torch/autograd/profiler.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/autograd/profiler.py',
   'DATA'),
  ('torch/autograd/graph.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/autograd/graph.py',
   'DATA'),
  ('torch/autograd/gradcheck.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/autograd/gradcheck.py',
   'DATA'),
  ('torch/autograd/grad_mode.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/autograd/grad_mode.py',
   'DATA'),
  ('torch/autograd/functional.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/autograd/functional.py',
   'DATA'),
  ('torch/autograd/function.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/autograd/function.py',
   'DATA'),
  ('torch/autograd/forward_ad.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/autograd/forward_ad.py',
   'DATA'),
  ('torch/autograd/anomaly_mode.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/autograd/anomaly_mode.py',
   'DATA'),
  ('torch/autograd/_functions/utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/autograd/_functions/utils.py',
   'DATA'),
  ('torch/autograd/_functions/tensor.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/autograd/_functions/tensor.py',
   'DATA'),
  ('torch/autograd/_functions/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/autograd/_functions/__init__.py',
   'DATA'),
  ('torch/ao/quantization/utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/utils.py',
   'DATA'),
  ('torch/ao/quantization/stubs.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/stubs.py',
   'DATA'),
  ('torch/ao/quantization/quantizer/xnnpack_quantizer_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/quantizer/xnnpack_quantizer_utils.py',
   'DATA'),
  ('torch/ao/quantization/quantizer/xnnpack_quantizer.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/quantizer/xnnpack_quantizer.py',
   'DATA'),
  ('torch/ao/quantization/quantizer/x86_inductor_quantizer.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/quantizer/x86_inductor_quantizer.py',
   'DATA'),
  ('torch/ao/quantization/quantizer/utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/quantizer/utils.py',
   'DATA'),
  ('torch/ao/quantization/quantizer/quantizer.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/quantizer/quantizer.py',
   'DATA'),
  ('torch/ao/quantization/quantizer/embedding_quantizer.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/quantizer/embedding_quantizer.py',
   'DATA'),
  ('torch/ao/quantization/quantizer/composable_quantizer.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/quantizer/composable_quantizer.py',
   'DATA'),
  ('torch/ao/quantization/quantizer/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/quantizer/__init__.py',
   'DATA'),
  ('torch/ao/quantization/quantize_pt2e.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/quantize_pt2e.py',
   'DATA'),
  ('torch/ao/quantization/quantize_jit.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/quantize_jit.py',
   'DATA'),
  ('torch/ao/quantization/quantize_fx.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/quantize_fx.py',
   'DATA'),
  ('torch/ao/quantization/quantize.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/quantize.py',
   'DATA'),
  ('torch/ao/quantization/quantization_mappings.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/quantization_mappings.py',
   'DATA'),
  ('torch/ao/quantization/quant_type.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/quant_type.py',
   'DATA'),
  ('torch/ao/quantization/qconfig_mapping.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/qconfig_mapping.py',
   'DATA'),
  ('torch/ao/quantization/qconfig.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/qconfig.py',
   'DATA'),
  ('torch/ao/quantization/pt2e/utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/pt2e/utils.py',
   'DATA'),
  ('torch/ao/quantization/pt2e/representation/rewrite.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/pt2e/representation/rewrite.py',
   'DATA'),
  ('torch/ao/quantization/pt2e/representation/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/pt2e/representation/__init__.py',
   'DATA'),
  ('torch/ao/quantization/pt2e/qat_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/pt2e/qat_utils.py',
   'DATA'),
  ('torch/ao/quantization/pt2e/prepare.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/pt2e/prepare.py',
   'DATA'),
  ('torch/ao/quantization/pt2e/port_metadata_pass.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/pt2e/port_metadata_pass.py',
   'DATA'),
  ('torch/ao/quantization/pt2e/graph_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/pt2e/graph_utils.py',
   'DATA'),
  ('torch/ao/quantization/pt2e/export_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/pt2e/export_utils.py',
   'DATA'),
  ('torch/ao/quantization/pt2e/duplicate_dq_pass.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/pt2e/duplicate_dq_pass.py',
   'DATA'),
  ('torch/ao/quantization/pt2e/_numeric_debugger.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/pt2e/_numeric_debugger.py',
   'DATA'),
  ('torch/ao/quantization/pt2e/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/pt2e/__init__.py',
   'DATA'),
  ('torch/ao/quantization/observer.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/observer.py',
   'DATA'),
  ('torch/ao/quantization/fx/utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/fx/utils.py',
   'DATA'),
  ('torch/ao/quantization/fx/tracer.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/fx/tracer.py',
   'DATA'),
  ('torch/ao/quantization/fx/quantize_handler.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/fx/quantize_handler.py',
   'DATA'),
  ('torch/ao/quantization/fx/qconfig_mapping_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/fx/qconfig_mapping_utils.py',
   'DATA'),
  ('torch/ao/quantization/fx/prepare.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/fx/prepare.py',
   'DATA'),
  ('torch/ao/quantization/fx/pattern_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/fx/pattern_utils.py',
   'DATA'),
  ('torch/ao/quantization/fx/match_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/fx/match_utils.py',
   'DATA'),
  ('torch/ao/quantization/fx/lstm_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/fx/lstm_utils.py',
   'DATA'),
  ('torch/ao/quantization/fx/lower_to_qnnpack.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/fx/lower_to_qnnpack.py',
   'DATA'),
  ('torch/ao/quantization/fx/lower_to_fbgemm.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/fx/lower_to_fbgemm.py',
   'DATA'),
  ('torch/ao/quantization/fx/graph_module.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/fx/graph_module.py',
   'DATA'),
  ('torch/ao/quantization/fx/fuse_handler.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/fx/fuse_handler.py',
   'DATA'),
  ('torch/ao/quantization/fx/fuse.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/fx/fuse.py',
   'DATA'),
  ('torch/ao/quantization/fx/custom_config.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/fx/custom_config.py',
   'DATA'),
  ('torch/ao/quantization/fx/convert.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/fx/convert.py',
   'DATA'),
  ('torch/ao/quantization/fx/_model_report/model_report_visualizer.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/fx/_model_report/model_report_visualizer.py',
   'DATA'),
  ('torch/ao/quantization/fx/_model_report/model_report_observer.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/fx/_model_report/model_report_observer.py',
   'DATA'),
  ('torch/ao/quantization/fx/_model_report/model_report.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/fx/_model_report/model_report.py',
   'DATA'),
  ('torch/ao/quantization/fx/_model_report/detector.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/fx/_model_report/detector.py',
   'DATA'),
  ('torch/ao/quantization/fx/_model_report/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/fx/_model_report/__init__.py',
   'DATA'),
  ('torch/ao/quantization/fx/_lower_to_native_backend.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/fx/_lower_to_native_backend.py',
   'DATA'),
  ('torch/ao/quantization/fx/_equalize.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/fx/_equalize.py',
   'DATA'),
  ('torch/ao/quantization/fx/_decomposed.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/fx/_decomposed.py',
   'DATA'),
  ('torch/ao/quantization/fx/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/fx/__init__.py',
   'DATA'),
  ('torch/ao/quantization/fuser_method_mappings.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/fuser_method_mappings.py',
   'DATA'),
  ('torch/ao/quantization/fuse_modules.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/fuse_modules.py',
   'DATA'),
  ('torch/ao/quantization/fake_quantize.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/fake_quantize.py',
   'DATA'),
  ('torch/ao/quantization/backend_config/x86.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/backend_config/x86.py',
   'DATA'),
  ('torch/ao/quantization/backend_config/utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/backend_config/utils.py',
   'DATA'),
  ('torch/ao/quantization/backend_config/tensorrt.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/backend_config/tensorrt.py',
   'DATA'),
  ('torch/ao/quantization/backend_config/qnnpack.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/backend_config/qnnpack.py',
   'DATA'),
  ('torch/ao/quantization/backend_config/onednn.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/backend_config/onednn.py',
   'DATA'),
  ('torch/ao/quantization/backend_config/observation_type.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/backend_config/observation_type.py',
   'DATA'),
  ('torch/ao/quantization/backend_config/native.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/backend_config/native.py',
   'DATA'),
  ('torch/ao/quantization/backend_config/fbgemm.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/backend_config/fbgemm.py',
   'DATA'),
  ('torch/ao/quantization/backend_config/executorch.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/backend_config/executorch.py',
   'DATA'),
  ('torch/ao/quantization/backend_config/backend_config.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/backend_config/backend_config.py',
   'DATA'),
  ('torch/ao/quantization/backend_config/_qnnpack_pt2e.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/backend_config/_qnnpack_pt2e.py',
   'DATA'),
  ('torch/ao/quantization/backend_config/_common_operator_config_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/backend_config/_common_operator_config_utils.py',
   'DATA'),
  ('torch/ao/quantization/backend_config/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/backend_config/__init__.py',
   'DATA'),
  ('torch/ao/quantization/_learnable_fake_quantize.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/_learnable_fake_quantize.py',
   'DATA'),
  ('torch/ao/quantization/_equalize.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/_equalize.py',
   'DATA'),
  ('torch/ao/quantization/_correct_bias.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/_correct_bias.py',
   'DATA'),
  ('torch/ao/quantization/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/quantization/__init__.py',
   'DATA'),
  ('torch/ao/pruning/sparsifier/weight_norm_sparsifier.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/pruning/sparsifier/weight_norm_sparsifier.py',
   'DATA'),
  ('torch/ao/pruning/sparsifier/utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/pruning/sparsifier/utils.py',
   'DATA'),
  ('torch/ao/pruning/sparsifier/nearly_diagonal_sparsifier.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/pruning/sparsifier/nearly_diagonal_sparsifier.py',
   'DATA'),
  ('torch/ao/pruning/sparsifier/base_sparsifier.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/pruning/sparsifier/base_sparsifier.py',
   'DATA'),
  ('torch/ao/pruning/sparsifier/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/pruning/sparsifier/__init__.py',
   'DATA'),
  ('torch/ao/pruning/scheduler/lambda_scheduler.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/pruning/scheduler/lambda_scheduler.py',
   'DATA'),
  ('torch/ao/pruning/scheduler/cubic_scheduler.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/pruning/scheduler/cubic_scheduler.py',
   'DATA'),
  ('torch/ao/pruning/scheduler/base_scheduler.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/pruning/scheduler/base_scheduler.py',
   'DATA'),
  ('torch/ao/pruning/scheduler/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/pruning/scheduler/__init__.py',
   'DATA'),
  ('torch/ao/pruning/_mappings.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/pruning/_mappings.py',
   'DATA'),
  ('torch/ao/pruning/_experimental/pruner/saliency_pruner.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/pruning/_experimental/pruner/saliency_pruner.py',
   'DATA'),
  ('torch/ao/pruning/_experimental/pruner/prune_functions.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/pruning/_experimental/pruner/prune_functions.py',
   'DATA'),
  ('torch/ao/pruning/_experimental/pruner/parametrization.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/pruning/_experimental/pruner/parametrization.py',
   'DATA'),
  ('torch/ao/pruning/_experimental/pruner/match_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/pruning/_experimental/pruner/match_utils.py',
   'DATA'),
  ('torch/ao/pruning/_experimental/pruner/lstm_saliency_pruner.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/pruning/_experimental/pruner/lstm_saliency_pruner.py',
   'DATA'),
  ('torch/ao/pruning/_experimental/pruner/base_structured_sparsifier.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/pruning/_experimental/pruner/base_structured_sparsifier.py',
   'DATA'),
  ('torch/ao/pruning/_experimental/pruner/FPGM_pruner.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/pruning/_experimental/pruner/FPGM_pruner.py',
   'DATA'),
  ('torch/ao/pruning/_experimental/pruner/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/pruning/_experimental/pruner/__init__.py',
   'DATA'),
  ('torch/ao/pruning/_experimental/data_sparsifier/quantization_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/pruning/_experimental/data_sparsifier/quantization_utils.py',
   'DATA'),
  ('torch/ao/pruning/_experimental/data_sparsifier/lightning/callbacks/data_sparsity.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/pruning/_experimental/data_sparsifier/lightning/callbacks/data_sparsity.py',
   'DATA'),
  ('torch/ao/pruning/_experimental/data_sparsifier/lightning/callbacks/_data_sparstity_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/pruning/_experimental/data_sparsifier/lightning/callbacks/_data_sparstity_utils.py',
   'DATA'),
  ('torch/ao/pruning/_experimental/data_sparsifier/lightning/callbacks/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/pruning/_experimental/data_sparsifier/lightning/callbacks/__init__.py',
   'DATA'),
  ('torch/ao/pruning/_experimental/data_sparsifier/lightning/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/pruning/_experimental/data_sparsifier/lightning/__init__.py',
   'DATA'),
  ('torch/ao/pruning/_experimental/data_sparsifier/data_norm_sparsifier.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/pruning/_experimental/data_sparsifier/data_norm_sparsifier.py',
   'DATA'),
  ('torch/ao/pruning/_experimental/data_sparsifier/base_data_sparsifier.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/pruning/_experimental/data_sparsifier/base_data_sparsifier.py',
   'DATA'),
  ('torch/ao/pruning/_experimental/data_sparsifier/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/pruning/_experimental/data_sparsifier/__init__.py',
   'DATA'),
  ('torch/ao/pruning/_experimental/data_scheduler/base_data_scheduler.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/pruning/_experimental/data_scheduler/base_data_scheduler.py',
   'DATA'),
  ('torch/ao/pruning/_experimental/data_scheduler/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/pruning/_experimental/data_scheduler/__init__.py',
   'DATA'),
  ('torch/ao/pruning/_experimental/activation_sparsifier/activation_sparsifier.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/pruning/_experimental/activation_sparsifier/activation_sparsifier.py',
   'DATA'),
  ('torch/ao/pruning/_experimental/activation_sparsifier/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/pruning/_experimental/activation_sparsifier/__init__.py',
   'DATA'),
  ('torch/ao/pruning/_experimental/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/pruning/_experimental/__init__.py',
   'DATA'),
  ('torch/ao/pruning/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/pruning/__init__.py',
   'DATA'),
  ('torch/ao/ns/fx/weight_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/ns/fx/weight_utils.py',
   'DATA'),
  ('torch/ao/ns/fx/utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/ns/fx/utils.py',
   'DATA'),
  ('torch/ao/ns/fx/qconfig_multi_mapping.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/ns/fx/qconfig_multi_mapping.py',
   'DATA'),
  ('torch/ao/ns/fx/pattern_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/ns/fx/pattern_utils.py',
   'DATA'),
  ('torch/ao/ns/fx/ns_types.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/ns/fx/ns_types.py',
   'DATA'),
  ('torch/ao/ns/fx/n_shadows_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/ns/fx/n_shadows_utils.py',
   'DATA'),
  ('torch/ao/ns/fx/mappings.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/ns/fx/mappings.py',
   'DATA'),
  ('torch/ao/ns/fx/graph_passes.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/ns/fx/graph_passes.py',
   'DATA'),
  ('torch/ao/ns/fx/graph_matcher.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/ns/fx/graph_matcher.py',
   'DATA'),
  ('torch/ao/ns/fx/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/ns/fx/__init__.py',
   'DATA'),
  ('torch/ao/ns/_numeric_suite_fx.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/ns/_numeric_suite_fx.py',
   'DATA'),
  ('torch/ao/ns/_numeric_suite.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/ns/_numeric_suite.py',
   'DATA'),
  ('torch/ao/ns/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/ns/__init__.py',
   'DATA'),
  ('torch/ao/nn/sparse/quantized/utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/nn/sparse/quantized/utils.py',
   'DATA'),
  ('torch/ao/nn/sparse/quantized/linear.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/nn/sparse/quantized/linear.py',
   'DATA'),
  ('torch/ao/nn/sparse/quantized/dynamic/linear.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/nn/sparse/quantized/dynamic/linear.py',
   'DATA'),
  ('torch/ao/nn/sparse/quantized/dynamic/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/nn/sparse/quantized/dynamic/__init__.py',
   'DATA'),
  ('torch/ao/nn/sparse/quantized/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/nn/sparse/quantized/__init__.py',
   'DATA'),
  ('torch/ao/nn/sparse/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/nn/sparse/__init__.py',
   'DATA'),
  ('torch/ao/nn/quantized/reference/modules/utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/nn/quantized/reference/modules/utils.py',
   'DATA'),
  ('torch/ao/nn/quantized/reference/modules/sparse.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/nn/quantized/reference/modules/sparse.py',
   'DATA'),
  ('torch/ao/nn/quantized/reference/modules/rnn.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/nn/quantized/reference/modules/rnn.py',
   'DATA'),
  ('torch/ao/nn/quantized/reference/modules/linear.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/nn/quantized/reference/modules/linear.py',
   'DATA'),
  ('torch/ao/nn/quantized/reference/modules/conv.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/nn/quantized/reference/modules/conv.py',
   'DATA'),
  ('torch/ao/nn/quantized/reference/modules/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/nn/quantized/reference/modules/__init__.py',
   'DATA'),
  ('torch/ao/nn/quantized/reference/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/nn/quantized/reference/__init__.py',
   'DATA'),
  ('torch/ao/nn/quantized/modules/utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/nn/quantized/modules/utils.py',
   'DATA'),
  ('torch/ao/nn/quantized/modules/rnn.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/nn/quantized/modules/rnn.py',
   'DATA'),
  ('torch/ao/nn/quantized/modules/normalization.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/nn/quantized/modules/normalization.py',
   'DATA'),
  ('torch/ao/nn/quantized/modules/linear.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/nn/quantized/modules/linear.py',
   'DATA'),
  ('torch/ao/nn/quantized/modules/functional_modules.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/nn/quantized/modules/functional_modules.py',
   'DATA'),
  ('torch/ao/nn/quantized/modules/embedding_ops.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/nn/quantized/modules/embedding_ops.py',
   'DATA'),
  ('torch/ao/nn/quantized/modules/dropout.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/nn/quantized/modules/dropout.py',
   'DATA'),
  ('torch/ao/nn/quantized/modules/conv.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/nn/quantized/modules/conv.py',
   'DATA'),
  ('torch/ao/nn/quantized/modules/batchnorm.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/nn/quantized/modules/batchnorm.py',
   'DATA'),
  ('torch/ao/nn/quantized/modules/activation.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/nn/quantized/modules/activation.py',
   'DATA'),
  ('torch/ao/nn/quantized/modules/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/nn/quantized/modules/__init__.py',
   'DATA'),
  ('torch/ao/nn/quantized/functional.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/nn/quantized/functional.py',
   'DATA'),
  ('torch/ao/nn/quantized/dynamic/modules/rnn.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/nn/quantized/dynamic/modules/rnn.py',
   'DATA'),
  ('torch/ao/nn/quantized/dynamic/modules/linear.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/nn/quantized/dynamic/modules/linear.py',
   'DATA'),
  ('torch/ao/nn/quantized/dynamic/modules/conv.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/nn/quantized/dynamic/modules/conv.py',
   'DATA'),
  ('torch/ao/nn/quantized/dynamic/modules/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/nn/quantized/dynamic/modules/__init__.py',
   'DATA'),
  ('torch/ao/nn/quantized/dynamic/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/nn/quantized/dynamic/__init__.py',
   'DATA'),
  ('torch/ao/nn/quantized/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/nn/quantized/__init__.py',
   'DATA'),
  ('torch/ao/nn/quantizable/modules/rnn.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/nn/quantizable/modules/rnn.py',
   'DATA'),
  ('torch/ao/nn/quantizable/modules/activation.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/nn/quantizable/modules/activation.py',
   'DATA'),
  ('torch/ao/nn/quantizable/modules/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/nn/quantizable/modules/__init__.py',
   'DATA'),
  ('torch/ao/nn/quantizable/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/nn/quantizable/__init__.py',
   'DATA'),
  ('torch/ao/nn/qat/modules/linear.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/nn/qat/modules/linear.py',
   'DATA'),
  ('torch/ao/nn/qat/modules/embedding_ops.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/nn/qat/modules/embedding_ops.py',
   'DATA'),
  ('torch/ao/nn/qat/modules/conv.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/nn/qat/modules/conv.py',
   'DATA'),
  ('torch/ao/nn/qat/modules/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/nn/qat/modules/__init__.py',
   'DATA'),
  ('torch/ao/nn/qat/dynamic/modules/linear.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/nn/qat/dynamic/modules/linear.py',
   'DATA'),
  ('torch/ao/nn/qat/dynamic/modules/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/nn/qat/dynamic/modules/__init__.py',
   'DATA'),
  ('torch/ao/nn/qat/dynamic/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/nn/qat/dynamic/__init__.py',
   'DATA'),
  ('torch/ao/nn/qat/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/nn/qat/__init__.py',
   'DATA'),
  ('torch/ao/nn/intrinsic/quantized/modules/linear_relu.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/nn/intrinsic/quantized/modules/linear_relu.py',
   'DATA'),
  ('torch/ao/nn/intrinsic/quantized/modules/conv_relu.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/nn/intrinsic/quantized/modules/conv_relu.py',
   'DATA'),
  ('torch/ao/nn/intrinsic/quantized/modules/conv_add.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/nn/intrinsic/quantized/modules/conv_add.py',
   'DATA'),
  ('torch/ao/nn/intrinsic/quantized/modules/bn_relu.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/nn/intrinsic/quantized/modules/bn_relu.py',
   'DATA'),
  ('torch/ao/nn/intrinsic/quantized/modules/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/nn/intrinsic/quantized/modules/__init__.py',
   'DATA'),
  ('torch/ao/nn/intrinsic/quantized/dynamic/modules/linear_relu.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/nn/intrinsic/quantized/dynamic/modules/linear_relu.py',
   'DATA'),
  ('torch/ao/nn/intrinsic/quantized/dynamic/modules/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/nn/intrinsic/quantized/dynamic/modules/__init__.py',
   'DATA'),
  ('torch/ao/nn/intrinsic/quantized/dynamic/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/nn/intrinsic/quantized/dynamic/__init__.py',
   'DATA'),
  ('torch/ao/nn/intrinsic/quantized/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/nn/intrinsic/quantized/__init__.py',
   'DATA'),
  ('torch/ao/nn/intrinsic/qat/modules/linear_relu.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/nn/intrinsic/qat/modules/linear_relu.py',
   'DATA'),
  ('torch/ao/nn/intrinsic/qat/modules/linear_fused.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/nn/intrinsic/qat/modules/linear_fused.py',
   'DATA'),
  ('torch/ao/nn/intrinsic/qat/modules/conv_fused.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/nn/intrinsic/qat/modules/conv_fused.py',
   'DATA'),
  ('torch/ao/nn/intrinsic/qat/modules/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/nn/intrinsic/qat/modules/__init__.py',
   'DATA'),
  ('torch/ao/nn/intrinsic/qat/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/nn/intrinsic/qat/__init__.py',
   'DATA'),
  ('torch/ao/nn/intrinsic/modules/fused.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/nn/intrinsic/modules/fused.py',
   'DATA'),
  ('torch/ao/nn/intrinsic/modules/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/nn/intrinsic/modules/__init__.py',
   'DATA'),
  ('torch/ao/nn/intrinsic/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/nn/intrinsic/__init__.py',
   'DATA'),
  ('torch/ao/nn/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/nn/__init__.py',
   'DATA'),
  ('torch/amp/grad_scaler.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/amp/grad_scaler.py',
   'DATA'),
  ('torch/amp/autocast_mode.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/amp/autocast_mode.py',
   'DATA'),
  ('torch/_weights_only_unpickler.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_weights_only_unpickler.py',
   'DATA'),
  ('torch/_vendor/packaging/version.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_vendor/packaging/version.py',
   'DATA'),
  ('torch/_vendor/packaging/_structures.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_vendor/packaging/_structures.py',
   'DATA'),
  ('torch/_vendor/packaging/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_vendor/packaging/__init__.py',
   'DATA'),
  ('torch/_vendor/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_vendor/__init__.py',
   'DATA'),
  ('torch/_subclasses/schema_check_mode.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_subclasses/schema_check_mode.py',
   'DATA'),
  ('torch/_subclasses/meta_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_subclasses/meta_utils.py',
   'DATA'),
  ('torch/_subclasses/functional_tensor.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_subclasses/functional_tensor.py',
   'DATA'),
  ('torch/_subclasses/fake_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_subclasses/fake_utils.py',
   'DATA'),
  ('torch/_subclasses/fake_tensor.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_subclasses/fake_tensor.py',
   'DATA'),
  ('torch/_subclasses/fake_impls.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_subclasses/fake_impls.py',
   'DATA'),
  ('torch/_subclasses/_fake_tensor_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_subclasses/_fake_tensor_utils.py',
   'DATA'),
  ('torch/_strobelight/compile_time_profiler.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_strobelight/compile_time_profiler.py',
   'DATA'),
  ('torch/_strobelight/cli_function_profiler.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_strobelight/cli_function_profiler.py',
   'DATA'),
  ('torch/_strobelight/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_strobelight/__init__.py',
   'DATA'),
  ('torch/_streambase.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_streambase.py',
   'DATA'),
  ('torch/_sources.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_sources.py',
   'DATA'),
  ('torch/_refs/special/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_refs/special/__init__.py',
   'DATA'),
  ('torch/_refs/nn/functional/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_refs/nn/functional/__init__.py',
   'DATA'),
  ('torch/_refs/nn/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_refs/nn/__init__.py',
   'DATA'),
  ('torch/_refs/linalg/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_refs/linalg/__init__.py',
   'DATA'),
  ('torch/_refs/fft.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_refs/fft.py',
   'DATA'),
  ('torch/_refs/_conversions.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_refs/_conversions.py',
   'DATA'),
  ('torch/_refs/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_refs/__init__.py',
   'DATA'),
  ('torch/_python_dispatcher.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_python_dispatcher.py',
   'DATA'),
  ('torch/_prims_common/wrappers.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_prims_common/wrappers.py',
   'DATA'),
  ('torch/_prims_common/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_prims_common/__init__.py',
   'DATA'),
  ('torch/_prims/rng_prims.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_prims/rng_prims.py',
   'DATA'),
  ('torch/_prims/executor.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_prims/executor.py',
   'DATA'),
  ('torch/_prims/debug_prims.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_prims/debug_prims.py',
   'DATA'),
  ('torch/_prims/context.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_prims/context.py',
   'DATA'),
  ('torch/_numpy/testing/utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_numpy/testing/utils.py',
   'DATA'),
  ('torch/_numpy/testing/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_numpy/testing/__init__.py',
   'DATA'),
  ('torch/_numpy/random.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_numpy/random.py',
   'DATA'),
  ('torch/_numpy/linalg.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_numpy/linalg.py',
   'DATA'),
  ('torch/_numpy/fft.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_numpy/fft.py',
   'DATA'),
  ('torch/_numpy/_util.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_numpy/_util.py',
   'DATA'),
  ('torch/_numpy/_unary_ufuncs_impl.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_numpy/_unary_ufuncs_impl.py',
   'DATA'),
  ('torch/_numpy/_ufuncs.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_numpy/_ufuncs.py',
   'DATA'),
  ('torch/_numpy/_reductions_impl.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_numpy/_reductions_impl.py',
   'DATA'),
  ('torch/_numpy/_normalizations.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_numpy/_normalizations.py',
   'DATA'),
  ('torch/_numpy/_ndarray.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_numpy/_ndarray.py',
   'DATA'),
  ('torch/_numpy/_getlimits.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_numpy/_getlimits.py',
   'DATA'),
  ('torch/_numpy/_funcs_impl.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_numpy/_funcs_impl.py',
   'DATA'),
  ('torch/_numpy/_funcs.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_numpy/_funcs.py',
   'DATA'),
  ('torch/_numpy/_dtypes_impl.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_numpy/_dtypes_impl.py',
   'DATA'),
  ('torch/_numpy/_dtypes.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_numpy/_dtypes.py',
   'DATA'),
  ('torch/_numpy/_casting_dicts.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_numpy/_casting_dicts.py',
   'DATA'),
  ('torch/_numpy/_binary_ufuncs_impl.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_numpy/_binary_ufuncs_impl.py',
   'DATA'),
  ('torch/_numpy/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_numpy/__init__.py',
   'DATA'),
  ('torch/_namedtensor_internals.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_namedtensor_internals.py',
   'DATA'),
  ('torch/_lowrank.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_lowrank.py',
   'DATA'),
  ('torch/_logging/structured.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_logging/structured.py',
   'DATA'),
  ('torch/_logging/scribe.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_logging/scribe.py',
   'DATA'),
  ('torch/_logging/_registrations.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_logging/_registrations.py',
   'DATA'),
  ('torch/_logging/_internal.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_logging/_internal.py',
   'DATA'),
  ('torch/_library/utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_library/utils.py',
   'DATA'),
  ('torch/_library/triton.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_library/triton.py',
   'DATA'),
  ('torch/_library/simple_registry.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_library/simple_registry.py',
   'DATA'),
  ('torch/_library/infer_schema.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_library/infer_schema.py',
   'DATA'),
  ('torch/_library/fake_impl.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_library/fake_impl.py',
   'DATA'),
  ('torch/_library/fake_class_registry.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_library/fake_class_registry.py',
   'DATA'),
  ('torch/_library/custom_ops.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_library/custom_ops.py',
   'DATA'),
  ('torch/_library/autograd.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_library/autograd.py',
   'DATA'),
  ('torch/_lazy/ts_backend.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_lazy/ts_backend.py',
   'DATA'),
  ('torch/_lazy/tensor_factory_functions.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_lazy/tensor_factory_functions.py',
   'DATA'),
  ('torch/_lazy/metrics.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_lazy/metrics.py',
   'DATA'),
  ('torch/_lazy/ir_cache.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_lazy/ir_cache.py',
   'DATA'),
  ('torch/_lazy/extract_compiled_graph.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_lazy/extract_compiled_graph.py',
   'DATA'),
  ('torch/_lazy/device_context.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_lazy/device_context.py',
   'DATA'),
  ('torch/_lazy/debug.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_lazy/debug.py',
   'DATA'),
  ('torch/_lazy/config.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_lazy/config.py',
   'DATA'),
  ('torch/_lazy/computation.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_lazy/computation.py',
   'DATA'),
  ('torch/_lazy/closure.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_lazy/closure.py',
   'DATA'),
  ('torch/_lazy/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_lazy/__init__.py',
   'DATA'),
  ('torch/_jit_internal.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_jit_internal.py',
   'DATA'),
  ('torch/_inductor/wrapper_benchmark.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/wrapper_benchmark.py',
   'DATA'),
  ('torch/_inductor/virtualized.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/virtualized.py',
   'DATA'),
  ('torch/_inductor/utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/utils.py',
   'DATA'),
  ('torch/_inductor/kernel/mm_common.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/kernel/mm_common.py',
   'DATA'),
  ('torch/_inductor/kernel/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/kernel/__init__.py',
   'DATA'),
  ('torch/_inductor/kernel/unpack_mixed_mm.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/kernel/unpack_mixed_mm.py',
   'DATA'),
  ('torch/_inductor/kernel/mm_plus_mm.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/kernel/mm_plus_mm.py',
   'DATA'),
  ('torch/_inductor/kernel/mm.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/kernel/mm.py',
   'DATA'),
  ('torch/_inductor/test_operators.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/test_operators.py',
   'DATA'),
  ('torch/_inductor/test_case.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/test_case.py',
   'DATA'),
  ('torch/_inductor/subgraph_lowering.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/subgraph_lowering.py',
   'DATA'),
  ('torch/_inductor/sizevars.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/sizevars.py',
   'DATA'),
  ('torch/_inductor/select_algorithm.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/select_algorithm.py',
   'DATA'),
  ('torch/_inductor/scheduler.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/scheduler.py',
   'DATA'),
  ('torch/_inductor/runtime/triton_heuristics.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/runtime/triton_heuristics.py',
   'DATA'),
  ('torch/_inductor/runtime/triton_helpers.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/runtime/triton_helpers.py',
   'DATA'),
  ('torch/_inductor/runtime/runtime_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/runtime/runtime_utils.py',
   'DATA'),
  ('torch/_inductor/runtime/hints.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/runtime/hints.py',
   'DATA'),
  ('torch/_inductor/runtime/halide_helpers.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/runtime/halide_helpers.py',
   'DATA'),
  ('torch/_inductor/runtime/coordinate_descent_tuner.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/runtime/coordinate_descent_tuner.py',
   'DATA'),
  ('torch/_inductor/runtime/compile_tasks.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/runtime/compile_tasks.py',
   'DATA'),
  ('torch/_inductor/runtime/benchmarking.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/runtime/benchmarking.py',
   'DATA'),
  ('torch/_inductor/runtime/autotune_cache.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/runtime/autotune_cache.py',
   'DATA'),
  ('torch/_inductor/runtime/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/runtime/__init__.py',
   'DATA'),
  ('torch/_inductor/remote_cache.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/remote_cache.py',
   'DATA'),
  ('torch/_inductor/quantized_lowerings.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/quantized_lowerings.py',
   'DATA'),
  ('torch/_inductor/pattern_matcher.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/pattern_matcher.py',
   'DATA'),
  ('torch/_inductor/package/pt2_archive_constants.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/package/pt2_archive_constants.py',
   'DATA'),
  ('torch/_inductor/package/package.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/package/package.py',
   'DATA'),
  ('torch/_inductor/package/build_package.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/package/build_package.py',
   'DATA'),
  ('torch/_inductor/package/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/package/__init__.py',
   'DATA'),
  ('torch/_inductor/optimize_indexing.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/optimize_indexing.py',
   'DATA'),
  ('torch/_inductor/ops_handler.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/ops_handler.py',
   'DATA'),
  ('torch/_inductor/mkldnn_lowerings.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/mkldnn_lowerings.py',
   'DATA'),
  ('torch/_inductor/mkldnn_ir.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/mkldnn_ir.py',
   'DATA'),
  ('torch/_inductor/metrics.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/metrics.py',
   'DATA'),
  ('torch/_inductor/lowering.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/lowering.py',
   'DATA'),
  ('torch/_inductor/loop_body.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/loop_body.py',
   'DATA'),
  ('torch/_inductor/jagged_lowerings.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/jagged_lowerings.py',
   'DATA'),
  ('torch/_inductor/ir.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/ir.py',
   'DATA'),
  ('torch/_inductor/inductor_prims.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/inductor_prims.py',
   'DATA'),
  ('torch/_inductor/index_propagation.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/index_propagation.py',
   'DATA'),
  ('torch/_inductor/hooks.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/hooks.py',
   'DATA'),
  ('torch/_inductor/graph.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/graph.py',
   'DATA'),
  ('torch/_inductor/fx_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/fx_utils.py',
   'DATA'),
  ('torch/_inductor/fx_passes/split_cat.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/fx_passes/split_cat.py',
   'DATA'),
  ('torch/_inductor/fx_passes/serialized_patterns/mm_pattern.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/fx_passes/serialized_patterns/mm_pattern.py',
   'DATA'),
  ('torch/_inductor/fx_passes/serialized_patterns/bmm_pattern.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/fx_passes/serialized_patterns/bmm_pattern.py',
   'DATA'),
  ('torch/_inductor/fx_passes/serialized_patterns/addmm_pattern.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/fx_passes/serialized_patterns/addmm_pattern.py',
   'DATA'),
  ('torch/_inductor/fx_passes/serialized_patterns/_sfdp_pattern_9.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/fx_passes/serialized_patterns/_sfdp_pattern_9.py',
   'DATA'),
  ('torch/_inductor/fx_passes/serialized_patterns/_sfdp_pattern_8.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/fx_passes/serialized_patterns/_sfdp_pattern_8.py',
   'DATA'),
  ('torch/_inductor/fx_passes/serialized_patterns/_sfdp_pattern_7.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/fx_passes/serialized_patterns/_sfdp_pattern_7.py',
   'DATA'),
  ('torch/_inductor/fx_passes/serialized_patterns/_sfdp_pattern_6.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/fx_passes/serialized_patterns/_sfdp_pattern_6.py',
   'DATA'),
  ('torch/_inductor/fx_passes/serialized_patterns/_sfdp_pattern_5.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/fx_passes/serialized_patterns/_sfdp_pattern_5.py',
   'DATA'),
  ('torch/_inductor/fx_passes/serialized_patterns/_sfdp_pattern_4.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/fx_passes/serialized_patterns/_sfdp_pattern_4.py',
   'DATA'),
  ('torch/_inductor/fx_passes/serialized_patterns/_sfdp_pattern_3.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/fx_passes/serialized_patterns/_sfdp_pattern_3.py',
   'DATA'),
  ('torch/_inductor/fx_passes/serialized_patterns/_sfdp_pattern_2.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/fx_passes/serialized_patterns/_sfdp_pattern_2.py',
   'DATA'),
  ('torch/_inductor/fx_passes/serialized_patterns/_sfdp_pattern_19.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/fx_passes/serialized_patterns/_sfdp_pattern_19.py',
   'DATA'),
  ('torch/_inductor/fx_passes/serialized_patterns/_sfdp_pattern_18.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/fx_passes/serialized_patterns/_sfdp_pattern_18.py',
   'DATA'),
  ('torch/_inductor/fx_passes/serialized_patterns/_sfdp_pattern_17.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/fx_passes/serialized_patterns/_sfdp_pattern_17.py',
   'DATA'),
  ('torch/_inductor/fx_passes/serialized_patterns/_sfdp_pattern_16.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/fx_passes/serialized_patterns/_sfdp_pattern_16.py',
   'DATA'),
  ('torch/_inductor/fx_passes/serialized_patterns/_sfdp_pattern_15.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/fx_passes/serialized_patterns/_sfdp_pattern_15.py',
   'DATA'),
  ('torch/_inductor/fx_passes/serialized_patterns/_sfdp_pattern_14.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/fx_passes/serialized_patterns/_sfdp_pattern_14.py',
   'DATA'),
  ('torch/_inductor/fx_passes/serialized_patterns/_sfdp_pattern_13.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/fx_passes/serialized_patterns/_sfdp_pattern_13.py',
   'DATA'),
  ('torch/_inductor/fx_passes/serialized_patterns/_sfdp_pattern_12.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/fx_passes/serialized_patterns/_sfdp_pattern_12.py',
   'DATA'),
  ('torch/_inductor/fx_passes/serialized_patterns/_sfdp_pattern_11.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/fx_passes/serialized_patterns/_sfdp_pattern_11.py',
   'DATA'),
  ('torch/_inductor/fx_passes/serialized_patterns/_sfdp_pattern_10.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/fx_passes/serialized_patterns/_sfdp_pattern_10.py',
   'DATA'),
  ('torch/_inductor/fx_passes/serialized_patterns/_sfdp_pattern_1.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/fx_passes/serialized_patterns/_sfdp_pattern_1.py',
   'DATA'),
  ('torch/_inductor/fx_passes/serialized_patterns/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/fx_passes/serialized_patterns/__init__.py',
   'DATA'),
  ('torch/_inductor/fx_passes/replace_random.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/fx_passes/replace_random.py',
   'DATA'),
  ('torch/_inductor/fx_passes/reinplace.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/fx_passes/reinplace.py',
   'DATA'),
  ('torch/_inductor/fx_passes/quantization.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/fx_passes/quantization.py',
   'DATA'),
  ('torch/_inductor/fx_passes/pre_grad.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/fx_passes/pre_grad.py',
   'DATA'),
  ('torch/_inductor/fx_passes/post_grad.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/fx_passes/post_grad.py',
   'DATA'),
  ('torch/_inductor/fx_passes/pad_mm.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/fx_passes/pad_mm.py',
   'DATA'),
  ('torch/_inductor/fx_passes/numeric_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/fx_passes/numeric_utils.py',
   'DATA'),
  ('torch/_inductor/fx_passes/mkldnn_fusion.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/fx_passes/mkldnn_fusion.py',
   'DATA'),
  ('torch/_inductor/fx_passes/misc_patterns.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/fx_passes/misc_patterns.py',
   'DATA'),
  ('torch/_inductor/fx_passes/micro_pipeline_tp.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/fx_passes/micro_pipeline_tp.py',
   'DATA'),
  ('torch/_inductor/fx_passes/joint_graph.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/fx_passes/joint_graph.py',
   'DATA'),
  ('torch/_inductor/fx_passes/group_batch_fusion.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/fx_passes/group_batch_fusion.py',
   'DATA'),
  ('torch/_inductor/fx_passes/fuse_attention.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/fx_passes/fuse_attention.py',
   'DATA'),
  ('torch/_inductor/fx_passes/freezing_patterns.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/fx_passes/freezing_patterns.py',
   'DATA'),
  ('torch/_inductor/fx_passes/efficient_conv_bn_eval.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/fx_passes/efficient_conv_bn_eval.py',
   'DATA'),
  ('torch/_inductor/fx_passes/dedupe_symint_uses.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/fx_passes/dedupe_symint_uses.py',
   'DATA'),
  ('torch/_inductor/fx_passes/decompose_mem_bound_mm.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/fx_passes/decompose_mem_bound_mm.py',
   'DATA'),
  ('torch/_inductor/fx_passes/ddp_fusion.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/fx_passes/ddp_fusion.py',
   'DATA'),
  ('torch/_inductor/fx_passes/binary_folding.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/fx_passes/binary_folding.py',
   'DATA'),
  ('torch/_inductor/fx_passes/b2b_gemm.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/fx_passes/b2b_gemm.py',
   'DATA'),
  ('torch/_inductor/fx_passes/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/fx_passes/__init__.py',
   'DATA'),
  ('torch/_inductor/freezing.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/freezing.py',
   'DATA'),
  ('torch/_inductor/extern_node_serializer.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/extern_node_serializer.py',
   'DATA'),
  ('torch/_inductor/exc.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/exc.py',
   'DATA'),
  ('torch/_inductor/dependencies.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/dependencies.py',
   'DATA'),
  ('torch/_inductor/decomposition.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/decomposition.py',
   'DATA'),
  ('torch/_inductor/debug.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/debug.py',
   'DATA'),
  ('torch/_inductor/cudagraph_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/cudagraph_utils.py',
   'DATA'),
  ('torch/_inductor/cpu_vec_isa.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/cpu_vec_isa.py',
   'DATA'),
  ('torch/_inductor/cpp_builder.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/cpp_builder.py',
   'DATA'),
  ('torch/_inductor/constant_folding.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/constant_folding.py',
   'DATA'),
  ('torch/_inductor/compile_worker/watchdog.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/compile_worker/watchdog.py',
   'DATA'),
  ('torch/_inductor/compile_worker/subproc_pool.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/compile_worker/subproc_pool.py',
   'DATA'),
  ('torch/_inductor/compile_worker/__main__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/compile_worker/__main__.py',
   'DATA'),
  ('torch/_inductor/compile_worker/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/compile_worker/__init__.py',
   'DATA'),
  ('torch/_inductor/comms.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/comms.py',
   'DATA'),
  ('torch/_inductor/comm_analysis.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/comm_analysis.py',
   'DATA'),
  ('torch/_inductor/codegen/xpu/device_op_overrides.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/codegen/xpu/device_op_overrides.py',
   'DATA'),
  ('torch/_inductor/codegen/xpu/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/codegen/xpu/__init__.py',
   'DATA'),
  ('torch/_inductor/codegen/wrapper.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/codegen/wrapper.py',
   'DATA'),
  ('torch/_inductor/codegen/triton_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/codegen/triton_utils.py',
   'DATA'),
  ('torch/_inductor/codegen/triton_split_scan.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/codegen/triton_split_scan.py',
   'DATA'),
  ('torch/_inductor/codegen/triton_combo_kernel.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/codegen/triton_combo_kernel.py',
   'DATA'),
  ('torch/_inductor/codegen/triton.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/codegen/triton.py',
   'DATA'),
  ('torch/_inductor/codegen/simd.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/codegen/simd.py',
   'DATA'),
  ('torch/_inductor/codegen/rocm/rocm_template_buffer.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/codegen/rocm/rocm_template_buffer.py',
   'DATA'),
  ('torch/_inductor/codegen/rocm/rocm_template.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/codegen/rocm/rocm_template.py',
   'DATA'),
  ('torch/_inductor/codegen/rocm/rocm_kernel.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/codegen/rocm/rocm_kernel.py',
   'DATA'),
  ('torch/_inductor/codegen/rocm/rocm_cpp_scheduling.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/codegen/rocm/rocm_cpp_scheduling.py',
   'DATA'),
  ('torch/_inductor/codegen/rocm/rocm_benchmark_request.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/codegen/rocm/rocm_benchmark_request.py',
   'DATA'),
  ('torch/_inductor/codegen/rocm/compile_command.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/codegen/rocm/compile_command.py',
   'DATA'),
  ('torch/_inductor/codegen/rocm/ck_universal_gemm_template.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/codegen/rocm/ck_universal_gemm_template.py',
   'DATA'),
  ('torch/_inductor/codegen/rocm/ck_template.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/codegen/rocm/ck_template.py',
   'DATA'),
  ('torch/_inductor/codegen/rocm/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/codegen/rocm/__init__.py',
   'DATA'),
  ('torch/_inductor/codegen/multi_kernel.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/codegen/multi_kernel.py',
   'DATA'),
  ('torch/_inductor/codegen/memory_planning.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/codegen/memory_planning.py',
   'DATA'),
  ('torch/_inductor/codegen/halide.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/codegen/halide.py',
   'DATA'),
  ('torch/_inductor/codegen/debug_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/codegen/debug_utils.py',
   'DATA'),
  ('torch/_inductor/codegen/cuda_combined_scheduling.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/codegen/cuda_combined_scheduling.py',
   'DATA'),
  ('torch/_inductor/codegen/cuda/gemm_template.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/codegen/cuda/gemm_template.py',
   'DATA'),
  ('torch/_inductor/codegen/cuda/device_op_overrides.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/codegen/cuda/device_op_overrides.py',
   'DATA'),
  ('torch/_inductor/codegen/cuda/cutlass_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/codegen/cuda/cutlass_utils.py',
   'DATA'),
  ('torch/_inductor/codegen/cuda/cutlass_lib_extensions/gemm_operation_extensions.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/codegen/cuda/cutlass_lib_extensions/gemm_operation_extensions.py',
   'DATA'),
  ('torch/_inductor/codegen/cuda/cutlass_lib_extensions/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/codegen/cuda/cutlass_lib_extensions/__init__.py',
   'DATA'),
  ('torch/_inductor/codegen/cuda/cutlass_epilogue_gen.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/codegen/cuda/cutlass_epilogue_gen.py',
   'DATA'),
  ('torch/_inductor/codegen/cuda/cuda_template.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/codegen/cuda/cuda_template.py',
   'DATA'),
  ('torch/_inductor/codegen/cuda/cuda_kernel.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/codegen/cuda/cuda_kernel.py',
   'DATA'),
  ('torch/_inductor/codegen/cuda/cuda_env.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/codegen/cuda/cuda_env.py',
   'DATA'),
  ('torch/_inductor/codegen/cuda/cuda_cpp_scheduling.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/codegen/cuda/cuda_cpp_scheduling.py',
   'DATA'),
  ('torch/_inductor/codegen/cuda/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/codegen/cuda/__init__.py',
   'DATA'),
  ('torch/_inductor/codegen/cpp_wrapper_cuda.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/codegen/cpp_wrapper_cuda.py',
   'DATA'),
  ('torch/_inductor/codegen/cpp_wrapper_cpu.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/codegen/cpp_wrapper_cpu.py',
   'DATA'),
  ('torch/_inductor/codegen/cpp_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/codegen/cpp_utils.py',
   'DATA'),
  ('torch/_inductor/codegen/cpp_template_kernel.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/codegen/cpp_template_kernel.py',
   'DATA'),
  ('torch/_inductor/codegen/cpp_template.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/codegen/cpp_template.py',
   'DATA'),
  ('torch/_inductor/codegen/cpp_micro_gemm.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/codegen/cpp_micro_gemm.py',
   'DATA'),
  ('torch/_inductor/codegen/cpp_gemm_template.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/codegen/cpp_gemm_template.py',
   'DATA'),
  ('torch/_inductor/codegen/cpp.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/codegen/cpp.py',
   'DATA'),
  ('torch/_inductor/codegen/common.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/codegen/common.py',
   'DATA'),
  ('torch/_inductor/codegen/codegen_device_driver.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/codegen/codegen_device_driver.py',
   'DATA'),
  ('torch/_inductor/codegen/aoti_hipify_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/codegen/aoti_hipify_utils.py',
   'DATA'),
  ('torch/_inductor/codegen/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/codegen/__init__.py',
   'DATA'),
  ('torch/_inductor/codecache.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/codecache.py',
   'DATA'),
  ('torch/_inductor/bounds.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/bounds.py',
   'DATA'),
  ('torch/_inductor/autotune_process.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/autotune_process.py',
   'DATA'),
  ('torch/_inductor/autoheuristic/learnedheuristic_interface.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/autoheuristic/learnedheuristic_interface.py',
   'DATA'),
  ('torch/_inductor/autoheuristic/learned_heuristic_controller.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/autoheuristic/learned_heuristic_controller.py',
   'DATA'),
  ('torch/_inductor/autoheuristic/autoheuristic_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/autoheuristic/autoheuristic_utils.py',
   'DATA'),
  ('torch/_inductor/autoheuristic/autoheuristic.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/autoheuristic/autoheuristic.py',
   'DATA'),
  ('torch/_inductor/autoheuristic/artifacts/_PadMMA100.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/autoheuristic/artifacts/_PadMMA100.py',
   'DATA'),
  ('torch/_inductor/autoheuristic/artifacts/_MixedMMH100.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/autoheuristic/artifacts/_MixedMMH100.py',
   'DATA'),
  ('torch/_inductor/autoheuristic/artifacts/_MixedMMA100.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/autoheuristic/artifacts/_MixedMMA100.py',
   'DATA'),
  ('torch/_inductor/autoheuristic/artifacts/_MMRankingH100.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/autoheuristic/artifacts/_MMRankingH100.py',
   'DATA'),
  ('torch/_inductor/autoheuristic/artifacts/_MMRankingA100.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/autoheuristic/artifacts/_MMRankingA100.py',
   'DATA'),
  ('torch/_inductor/autoheuristic/artifacts/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/autoheuristic/artifacts/__init__.py',
   'DATA'),
  ('torch/_inductor/autoheuristic/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/autoheuristic/__init__.py',
   'DATA'),
  ('torch/_inductor/async_compile.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/async_compile.py',
   'DATA'),
  ('torch/_inductor/aoti_eager.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/aoti_eager.py',
   'DATA'),
  ('torch/_higher_order_ops/wrap.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_higher_order_ops/wrap.py',
   'DATA'),
  ('torch/_higher_order_ops/utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_higher_order_ops/utils.py',
   'DATA'),
  ('torch/_higher_order_ops/triton_kernel_wrap.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_higher_order_ops/triton_kernel_wrap.py',
   'DATA'),
  ('torch/_higher_order_ops/torchbind.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_higher_order_ops/torchbind.py',
   'DATA'),
  ('torch/_higher_order_ops/strict_mode.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_higher_order_ops/strict_mode.py',
   'DATA'),
  ('torch/_higher_order_ops/run_const_graph.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_higher_order_ops/run_const_graph.py',
   'DATA'),
  ('torch/_higher_order_ops/out_dtype.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_higher_order_ops/out_dtype.py',
   'DATA'),
  ('torch/_higher_order_ops/map.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_higher_order_ops/map.py',
   'DATA'),
  ('torch/_higher_order_ops/hints_wrap.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_higher_order_ops/hints_wrap.py',
   'DATA'),
  ('torch/_higher_order_ops/flex_attention.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_higher_order_ops/flex_attention.py',
   'DATA'),
  ('torch/_higher_order_ops/executorch_call_delegate.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_higher_order_ops/executorch_call_delegate.py',
   'DATA'),
  ('torch/_higher_order_ops/effects.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_higher_order_ops/effects.py',
   'DATA'),
  ('torch/_higher_order_ops/auto_functionalize.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_higher_order_ops/auto_functionalize.py',
   'DATA'),
  ('torch/_higher_order_ops/associative_scan.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_higher_order_ops/associative_scan.py',
   'DATA'),
  ('torch/_guards.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_guards.py',
   'DATA'),
  ('torch/_functorch/vmap.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_functorch/vmap.py',
   'DATA'),
  ('torch/_functorch/utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_functorch/utils.py',
   'DATA'),
  ('torch/_functorch/top_operators_github_usage.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_functorch/top_operators_github_usage.py',
   'DATA'),
  ('torch/_functorch/pytree_hacks.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_functorch/pytree_hacks.py',
   'DATA'),
  ('torch/_functorch/python_key.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_functorch/python_key.py',
   'DATA'),
  ('torch/_functorch/pyfunctorch.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_functorch/pyfunctorch.py',
   'DATA'),
  ('torch/_functorch/partitioners.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_functorch/partitioners.py',
   'DATA'),
  ('torch/_functorch/make_functional.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_functorch/make_functional.py',
   'DATA'),
  ('torch/_functorch/fx_minifier.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_functorch/fx_minifier.py',
   'DATA'),
  ('torch/_functorch/functional_call.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_functorch/functional_call.py',
   'DATA'),
  ('torch/_functorch/eager_transforms.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_functorch/eager_transforms.py',
   'DATA'),
  ('torch/_functorch/deprecated.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_functorch/deprecated.py',
   'DATA'),
  ('torch/_functorch/config.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_functorch/config.py',
   'DATA'),
  ('torch/_functorch/compilers.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_functorch/compilers.py',
   'DATA'),
  ('torch/_functorch/compile_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_functorch/compile_utils.py',
   'DATA'),
  ('torch/_functorch/benchmark_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_functorch/benchmark_utils.py',
   'DATA'),
  ('torch/_functorch/batch_norm_replacement.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_functorch/batch_norm_replacement.py',
   'DATA'),
  ('torch/_functorch/autograd_function.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_functorch/autograd_function.py',
   'DATA'),
  ('torch/_functorch/apis.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_functorch/apis.py',
   'DATA'),
  ('torch/_functorch/aot_autograd.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_functorch/aot_autograd.py',
   'DATA'),
  ('torch/_functorch/_aot_autograd/utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_functorch/_aot_autograd/utils.py',
   'DATA'),
  ('torch/_functorch/_aot_autograd/traced_function_transforms.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_functorch/_aot_autograd/traced_function_transforms.py',
   'DATA'),
  ('torch/_functorch/_aot_autograd/subclass_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_functorch/_aot_autograd/subclass_utils.py',
   'DATA'),
  ('torch/_functorch/_aot_autograd/schemas.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_functorch/_aot_autograd/schemas.py',
   'DATA'),
  ('torch/_functorch/_aot_autograd/runtime_wrappers.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_functorch/_aot_autograd/runtime_wrappers.py',
   'DATA'),
  ('torch/_functorch/_aot_autograd/logging_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_functorch/_aot_autograd/logging_utils.py',
   'DATA'),
  ('torch/_functorch/_aot_autograd/jit_compile_runtime_wrappers.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_functorch/_aot_autograd/jit_compile_runtime_wrappers.py',
   'DATA'),
  ('torch/_functorch/_aot_autograd/input_output_analysis.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_functorch/_aot_autograd/input_output_analysis.py',
   'DATA'),
  ('torch/_functorch/_aot_autograd/functional_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_functorch/_aot_autograd/functional_utils.py',
   'DATA'),
  ('torch/_functorch/_aot_autograd/dispatch_and_compile_graph.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_functorch/_aot_autograd/dispatch_and_compile_graph.py',
   'DATA'),
  ('torch/_functorch/_aot_autograd/collect_metadata_analysis.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_functorch/_aot_autograd/collect_metadata_analysis.py',
   'DATA'),
  ('torch/_functorch/_aot_autograd/autograd_cache.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_functorch/_aot_autograd/autograd_cache.py',
   'DATA'),
  ('torch/_functorch/_aot_autograd/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_functorch/_aot_autograd/__init__.py',
   'DATA'),
  ('torch/_functorch/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_functorch/__init__.py',
   'DATA'),
  ('torch/_export/wrappers.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/wrappers.py',
   'DATA'),
  ('torch/_export/verifier.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/verifier.py',
   'DATA'),
  ('torch/_export/utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/utils.py',
   'DATA'),
  ('torch/_export/tools.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/tools.py',
   'DATA'),
  ('torch/_export/serde/union.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/serde/union.py',
   'DATA'),
  ('torch/_export/serde/serialize.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/serde/serialize.py',
   'DATA'),
  ('torch/_export/serde/schema_check.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/serde/schema_check.py',
   'DATA'),
  ('torch/_export/serde/schema.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/serde/schema.py',
   'DATA'),
  ('torch/_export/serde/dynamic_shapes.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/serde/dynamic_shapes.py',
   'DATA'),
  ('torch/_export/serde/aoti_schema.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/serde/aoti_schema.py',
   'DATA'),
  ('torch/_export/serde/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/serde/__init__.py',
   'DATA'),
  ('torch/_export/passes/replace_with_hop_pass_util.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/passes/replace_with_hop_pass_util.py',
   'DATA'),
  ('torch/_export/passes/replace_view_ops_with_view_copy_ops_pass.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/passes/replace_view_ops_with_view_copy_ops_pass.py',
   'DATA'),
  ('torch/_export/passes/replace_set_grad_with_hop_pass.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/passes/replace_set_grad_with_hop_pass.py',
   'DATA'),
  ('torch/_export/passes/replace_quantized_ops_with_standard_ops_pass.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/passes/replace_quantized_ops_with_standard_ops_pass.py',
   'DATA'),
  ('torch/_export/passes/replace_autocast_with_hop_pass.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/passes/replace_autocast_with_hop_pass.py',
   'DATA'),
  ('torch/_export/passes/remove_runtime_assertions.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/passes/remove_runtime_assertions.py',
   'DATA'),
  ('torch/_export/passes/lift_constants_pass.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/passes/lift_constants_pass.py',
   'DATA'),
  ('torch/_export/passes/functionalize_side_effectful_ops_pass.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/passes/functionalize_side_effectful_ops_pass.py',
   'DATA'),
  ('torch/_export/passes/constant_folding.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/passes/constant_folding.py',
   'DATA'),
  ('torch/_export/passes/collect_tracepoints_pass.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/passes/collect_tracepoints_pass.py',
   'DATA'),
  ('torch/_export/passes/add_runtime_assertions_for_constraints_pass.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/passes/add_runtime_assertions_for_constraints_pass.py',
   'DATA'),
  ('torch/_export/passes/_node_metadata_hook.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/passes/_node_metadata_hook.py',
   'DATA'),
  ('torch/_export/passes/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/passes/__init__.py',
   'DATA'),
  ('torch/_export/pass_infra/proxy_value.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/pass_infra/proxy_value.py',
   'DATA'),
  ('torch/_export/pass_infra/node_metadata.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/pass_infra/node_metadata.py',
   'DATA'),
  ('torch/_export/pass_infra/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/pass_infra/__init__.py',
   'DATA'),
  ('torch/_export/pass_base.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/pass_base.py',
   'DATA'),
  ('torch/_export/non_strict_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/non_strict_utils.py',
   'DATA'),
  ('torch/_export/error.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/error.py',
   'DATA'),
  ('torch/_export/db/logging.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/db/logging.py',
   'DATA'),
  ('torch/_export/db/gen_example.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/db/gen_example.py',
   'DATA'),
  ('torch/_export/db/examples/user_input_mutation.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/db/examples/user_input_mutation.py',
   'DATA'),
  ('torch/_export/db/examples/unsupported_operator.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/db/examples/unsupported_operator.py',
   'DATA'),
  ('torch/_export/db/examples/type_reflection_method.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/db/examples/type_reflection_method.py',
   'DATA'),
  ('torch/_export/db/examples/tensor_setattr.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/db/examples/tensor_setattr.py',
   'DATA'),
  ('torch/_export/db/examples/static_if.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/db/examples/static_if.py',
   'DATA'),
  ('torch/_export/db/examples/static_for_loop.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/db/examples/static_for_loop.py',
   'DATA'),
  ('torch/_export/db/examples/specialized_attribute.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/db/examples/specialized_attribute.py',
   'DATA'),
  ('torch/_export/db/examples/scalar_output.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/db/examples/scalar_output.py',
   'DATA'),
  ('torch/_export/db/examples/pytree_flatten.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/db/examples/pytree_flatten.py',
   'DATA'),
  ('torch/_export/db/examples/optional_input.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/db/examples/optional_input.py',
   'DATA'),
  ('torch/_export/db/examples/null_context_manager.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/db/examples/null_context_manager.py',
   'DATA'),
  ('torch/_export/db/examples/nested_function.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/db/examples/nested_function.py',
   'DATA'),
  ('torch/_export/db/examples/model_attr_mutation.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/db/examples/model_attr_mutation.py',
   'DATA'),
  ('torch/_export/db/examples/list_unpack.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/db/examples/list_unpack.py',
   'DATA'),
  ('torch/_export/db/examples/list_contains.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/db/examples/list_contains.py',
   'DATA'),
  ('torch/_export/db/examples/fn_with_kwargs.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/db/examples/fn_with_kwargs.py',
   'DATA'),
  ('torch/_export/db/examples/dynamic_shape_view.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/db/examples/dynamic_shape_view.py',
   'DATA'),
  ('torch/_export/db/examples/dynamic_shape_slicing.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/db/examples/dynamic_shape_slicing.py',
   'DATA'),
  ('torch/_export/db/examples/dynamic_shape_round.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/db/examples/dynamic_shape_round.py',
   'DATA'),
  ('torch/_export/db/examples/dynamic_shape_map.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/db/examples/dynamic_shape_map.py',
   'DATA'),
  ('torch/_export/db/examples/dynamic_shape_if_guard.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/db/examples/dynamic_shape_if_guard.py',
   'DATA'),
  ('torch/_export/db/examples/dynamic_shape_constructor.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/db/examples/dynamic_shape_constructor.py',
   'DATA'),
  ('torch/_export/db/examples/dynamic_shape_assert.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/db/examples/dynamic_shape_assert.py',
   'DATA'),
  ('torch/_export/db/examples/dictionary.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/db/examples/dictionary.py',
   'DATA'),
  ('torch/_export/db/examples/decorator.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/db/examples/decorator.py',
   'DATA'),
  ('torch/_export/db/examples/constrain_as_value_example.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/db/examples/constrain_as_value_example.py',
   'DATA'),
  ('torch/_export/db/examples/constrain_as_size_example.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/db/examples/constrain_as_size_example.py',
   'DATA'),
  ('torch/_export/db/examples/cond_predicate.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/db/examples/cond_predicate.py',
   'DATA'),
  ('torch/_export/db/examples/cond_operands.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/db/examples/cond_operands.py',
   'DATA'),
  ('torch/_export/db/examples/cond_closed_over_variable.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/db/examples/cond_closed_over_variable.py',
   'DATA'),
  ('torch/_export/db/examples/cond_branch_nonlocal_variables.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/db/examples/cond_branch_nonlocal_variables.py',
   'DATA'),
  ('torch/_export/db/examples/cond_branch_nested_function.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/db/examples/cond_branch_nested_function.py',
   'DATA'),
  ('torch/_export/db/examples/cond_branch_class_method.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/db/examples/cond_branch_class_method.py',
   'DATA'),
  ('torch/_export/db/examples/class_method.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/db/examples/class_method.py',
   'DATA'),
  ('torch/_export/db/examples/autograd_function.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/db/examples/autograd_function.py',
   'DATA'),
  ('torch/_export/db/examples/assume_constant_result.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/db/examples/assume_constant_result.py',
   'DATA'),
  ('torch/_export/db/examples/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/db/examples/__init__.py',
   'DATA'),
  ('torch/_export/db/case.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/db/case.py',
   'DATA'),
  ('torch/_export/db/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/db/__init__.py',
   'DATA'),
  ('torch/_export/converter.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/converter.py',
   'DATA'),
  ('torch/_export/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_export/__init__.py',
   'DATA'),
  ('torch/_dynamo/variables/user_defined.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/variables/user_defined.py',
   'DATA'),
  ('torch/_dynamo/variables/torch_function.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/variables/torch_function.py',
   'DATA'),
  ('torch/_dynamo/variables/torch.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/variables/torch.py',
   'DATA'),
  ('torch/_dynamo/variables/tensor.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/variables/tensor.py',
   'DATA'),
  ('torch/_dynamo/variables/sdpa.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/variables/sdpa.py',
   'DATA'),
  ('torch/_dynamo/variables/script_object.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/variables/script_object.py',
   'DATA'),
  ('torch/_dynamo/variables/optimizer.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/variables/optimizer.py',
   'DATA'),
  ('torch/_dynamo/variables/nn_module.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/variables/nn_module.py',
   'DATA'),
  ('torch/_dynamo/variables/misc.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/variables/misc.py',
   'DATA'),
  ('torch/_dynamo/variables/lists.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/variables/lists.py',
   'DATA'),
  ('torch/_dynamo/variables/lazy.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/variables/lazy.py',
   'DATA'),
  ('torch/_dynamo/variables/iter.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/variables/iter.py',
   'DATA'),
  ('torch/_dynamo/variables/higher_order_ops.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/variables/higher_order_ops.py',
   'DATA'),
  ('torch/_dynamo/variables/functions.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/variables/functions.py',
   'DATA'),
  ('torch/_dynamo/variables/distributed.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/variables/distributed.py',
   'DATA'),
  ('torch/_dynamo/variables/dicts.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/variables/dicts.py',
   'DATA'),
  ('torch/_dynamo/variables/ctx_manager.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/variables/ctx_manager.py',
   'DATA'),
  ('torch/_dynamo/variables/constant.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/variables/constant.py',
   'DATA'),
  ('torch/_dynamo/variables/builtin.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/variables/builtin.py',
   'DATA'),
  ('torch/_dynamo/variables/builder.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/variables/builder.py',
   'DATA'),
  ('torch/_dynamo/variables/base.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/variables/base.py',
   'DATA'),
  ('torch/_dynamo/variables/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/variables/__init__.py',
   'DATA'),
  ('torch/_dynamo/utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/utils.py',
   'DATA'),
  ('torch/_dynamo/types.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/types.py',
   'DATA'),
  ('torch/_dynamo/trace_rules.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/trace_rules.py',
   'DATA'),
  ('torch/_dynamo/testing.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/testing.py',
   'DATA'),
  ('torch/_dynamo/test_minifier_common.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/test_minifier_common.py',
   'DATA'),
  ('torch/_dynamo/test_case.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/test_case.py',
   'DATA'),
  ('torch/_dynamo/tensor_version_op.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/tensor_version_op.py',
   'DATA'),
  ('torch/_dynamo/symbolic_convert.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/symbolic_convert.py',
   'DATA'),
  ('torch/_dynamo/source.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/source.py',
   'DATA'),
  ('torch/_dynamo/side_effects.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/side_effects.py',
   'DATA'),
  ('torch/_dynamo/resume_execution.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/resume_execution.py',
   'DATA'),
  ('torch/_dynamo/repro/after_dynamo.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/repro/after_dynamo.py',
   'DATA'),
  ('torch/_dynamo/repro/after_aot.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/repro/after_aot.py',
   'DATA'),
  ('torch/_dynamo/repro/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/repro/__init__.py',
   'DATA'),
  ('torch/_dynamo/replay_record.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/replay_record.py',
   'DATA'),
  ('torch/_dynamo/profiler.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/profiler.py',
   'DATA'),
  ('torch/_dynamo/polyfills/sys.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/polyfills/sys.py',
   'DATA'),
  ('torch/_dynamo/polyfills/os.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/polyfills/os.py',
   'DATA'),
  ('torch/_dynamo/polyfills/loader.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/polyfills/loader.py',
   'DATA'),
  ('torch/_dynamo/polyfills/itertools.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/polyfills/itertools.py',
   'DATA'),
  ('torch/_dynamo/polyfills/functools.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/polyfills/functools.py',
   'DATA'),
  ('torch/_dynamo/polyfills/builtins.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/polyfills/builtins.py',
   'DATA'),
  ('torch/_dynamo/polyfills/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/polyfills/__init__.py',
   'DATA'),
  ('torch/_dynamo/output_graph.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/output_graph.py',
   'DATA'),
  ('torch/_dynamo/mutation_guard.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/mutation_guard.py',
   'DATA'),
  ('torch/_dynamo/logging.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/logging.py',
   'DATA'),
  ('torch/_dynamo/hooks.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/hooks.py',
   'DATA'),
  ('torch/_dynamo/guards.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/guards.py',
   'DATA'),
  ('torch/_dynamo/funcname_cache.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/funcname_cache.py',
   'DATA'),
  ('torch/_dynamo/external_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/external_utils.py',
   'DATA'),
  ('torch/_dynamo/exc.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/exc.py',
   'DATA'),
  ('torch/_dynamo/eval_frame.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/eval_frame.py',
   'DATA'),
  ('torch/_dynamo/distributed.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/distributed.py',
   'DATA'),
  ('torch/_dynamo/device_interface.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/device_interface.py',
   'DATA'),
  ('torch/_dynamo/decorators.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/decorators.py',
   'DATA'),
  ('torch/_dynamo/debug_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/debug_utils.py',
   'DATA'),
  ('torch/_dynamo/current_scope_id.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/current_scope_id.py',
   'DATA'),
  ('torch/_dynamo/create_parameter_op.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/create_parameter_op.py',
   'DATA'),
  ('torch/_dynamo/convert_frame.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/convert_frame.py',
   'DATA'),
  ('torch/_dynamo/config.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/config.py',
   'DATA'),
  ('torch/_dynamo/comptime.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/comptime.py',
   'DATA'),
  ('torch/_dynamo/compiled_autograd.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/compiled_autograd.py',
   'DATA'),
  ('torch/_dynamo/codegen.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/codegen.py',
   'DATA'),
  ('torch/_dynamo/code_context.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/code_context.py',
   'DATA'),
  ('torch/_dynamo/callback.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/callback.py',
   'DATA'),
  ('torch/_dynamo/cache_size.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/cache_size.py',
   'DATA'),
  ('torch/_dynamo/bytecode_transformation.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/bytecode_transformation.py',
   'DATA'),
  ('torch/_dynamo/bytecode_analysis.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/bytecode_analysis.py',
   'DATA'),
  ('torch/_dynamo/backends/tvm.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/backends/tvm.py',
   'DATA'),
  ('torch/_dynamo/backends/torchxla.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/backends/torchxla.py',
   'DATA'),
  ('torch/_dynamo/backends/tensorrt.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/backends/tensorrt.py',
   'DATA'),
  ('torch/_dynamo/backends/onnxrt.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/backends/onnxrt.py',
   'DATA'),
  ('torch/_dynamo/backends/inductor.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/backends/inductor.py',
   'DATA'),
  ('torch/_dynamo/backends/distributed.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/backends/distributed.py',
   'DATA'),
  ('torch/_dynamo/backends/debugging.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/backends/debugging.py',
   'DATA'),
  ('torch/_dynamo/backends/cudagraphs.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/backends/cudagraphs.py',
   'DATA'),
  ('torch/_dynamo/backends/common.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/backends/common.py',
   'DATA'),
  ('torch/_dynamo/backends/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/backends/__init__.py',
   'DATA'),
  ('torch/_dynamo/_trace_wrapped_higher_order_op.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/_trace_wrapped_higher_order_op.py',
   'DATA'),
  ('torch/_dispatch/python.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dispatch/python.py',
   'DATA'),
  ('torch/_dispatch/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dispatch/__init__.py',
   'DATA'),
  ('torch/_deploy.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_deploy.py',
   'DATA'),
  ('torch/_decomp/decompositions_for_rng.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_decomp/decompositions_for_rng.py',
   'DATA'),
  ('torch/_decomp/decompositions_for_jvp.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_decomp/decompositions_for_jvp.py',
   'DATA'),
  ('torch/_decomp/decompositions.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_decomp/decompositions.py',
   'DATA'),
  ('torch/_decomp/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_decomp/__init__.py',
   'DATA'),
  ('torch/_custom_ops.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_custom_ops.py',
   'DATA'),
  ('torch/_custom_op/impl.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_custom_op/impl.py',
   'DATA'),
  ('torch/_custom_op/functional.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_custom_op/functional.py',
   'DATA'),
  ('torch/_custom_op/autograd.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_custom_op/autograd.py',
   'DATA'),
  ('torch/_custom_op/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_custom_op/__init__.py',
   'DATA'),
  ('torch/_appdirs.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_appdirs.py',
   'DATA'),
  ('torch/_logging/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_logging/__init__.py',
   'DATA'),
  ('torch/onnx/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/onnx/__init__.py',
   'DATA'),
  ('torch/_subclasses/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_subclasses/__init__.py',
   'DATA'),
  ('torch/_dynamo/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/__init__.py',
   'DATA'),
  ('torch/compiler/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/compiler/__init__.py',
   'DATA'),
  ('torch/fx/experimental/sym_node.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/experimental/sym_node.py',
   'DATA'),
  ('torch/cuda/_sanitizer.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/cuda/_sanitizer.py',
   'DATA'),
  ('torch/_meta_registrations.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_meta_registrations.py',
   'DATA'),
  ('torch/_higher_order_ops/while_loop.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_higher_order_ops/while_loop.py',
   'DATA'),
  ('torch/_higher_order_ops/cond.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_higher_order_ops/cond.py',
   'DATA'),
  ('torch/_higher_order_ops/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_higher_order_ops/__init__.py',
   'DATA'),
  ('torch/return_types.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/return_types.py',
   'DATA'),
  ('torch/library.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/library.py',
   'DATA'),
  ('torch/func/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/func/__init__.py',
   'DATA'),
  ('torch/export/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/export/__init__.py',
   'DATA'),
  ('torch/_dynamo/backends/registry.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_dynamo/backends/registry.py',
   'DATA'),
  ('torch/_inductor/cudagraph_trees.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/cudagraph_trees.py',
   'DATA'),
  ('torch/_inductor/compile_fx.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/compile_fx.py',
   'DATA'),
  ('torch/_inductor/config.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/config.py',
   'DATA'),
  ('torch/_inductor/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_inductor/__init__.py',
   'DATA'),
  ('torch/utils/dlpack.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/dlpack.py',
   'DATA'),
  ('torch/_linalg_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_linalg_utils.py',
   'DATA'),
  ('torch/masked/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/masked/__init__.py',
   'DATA'),
  ('torch/_lobpcg.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_lobpcg.py',
   'DATA'),
  ('torch/multiprocessing/_atfork.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/multiprocessing/_atfork.py',
   'DATA'),
  ('torch/quasirandom.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/quasirandom.py',
   'DATA'),
  ('torch/quantization/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/quantization/__init__.py',
   'DATA'),
  ('torch/_classes.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_classes.py',
   'DATA'),
  ('torch/_library/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_library/__init__.py',
   'DATA'),
  ('torch/_torch_docs.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_torch_docs.py',
   'DATA'),
  ('torch/_tensor_docs.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_tensor_docs.py',
   'DATA'),
  ('torch/_storage_docs.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_storage_docs.py',
   'DATA'),
  ('torch/_size_docs.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_size_docs.py',
   'DATA'),
  ('torch/nn/quantized/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/quantized/__init__.py',
   'DATA'),
  ('torch/nn/quantizable/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/quantizable/__init__.py',
   'DATA'),
  ('torch/nn/qat/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/qat/__init__.py',
   'DATA'),
  ('torch/nn/intrinsic/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/intrinsic/__init__.py',
   'DATA'),
  ('torch/ao/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/ao/__init__.py',
   'DATA'),
  ('torch/signal/windows/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/signal/windows/__init__.py',
   'DATA'),
  ('torch/signal/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/signal/__init__.py',
   'DATA'),
  ('torch/xpu/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/xpu/__init__.py',
   'DATA'),
  ('torch/utils/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/__init__.py',
   'DATA'),
  ('torch/testing/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/testing/__init__.py',
   'DATA'),
  ('torch/special/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/special/__init__.py',
   'DATA'),
  ('torch/sparse/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/sparse/__init__.py',
   'DATA'),
  ('torch/profiler/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/profiler/__init__.py',
   'DATA'),
  ('torch/overrides.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/overrides.py',
   'DATA'),
  ('torch/optim/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/optim/__init__.py',
   'DATA'),
  ('torch/nn/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nn/__init__.py',
   'DATA'),
  ('torch/nested/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/nested/__init__.py',
   'DATA'),
  ('torch/multiprocessing/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/multiprocessing/__init__.py',
   'DATA'),
  ('torch/mtia/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/mtia/__init__.py',
   'DATA'),
  ('torch/mps/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/mps/__init__.py',
   'DATA'),
  ('torch/linalg/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/linalg/__init__.py',
   'DATA'),
  ('torch/jit/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/jit/__init__.py',
   'DATA'),
  ('torch/hub.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/hub.py',
   'DATA'),
  ('torch/futures/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/futures/__init__.py',
   'DATA'),
  ('torch/fft/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fft/__init__.py',
   'DATA'),
  ('torch/distributions/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributions/__init__.py',
   'DATA'),
  ('torch/distributed/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/distributed/__init__.py',
   'DATA'),
  ('torch/cuda/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/cuda/__init__.py',
   'DATA'),
  ('torch/cpu/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/cpu/__init__.py',
   'DATA'),
  ('torch/backends/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/backends/__init__.py',
   'DATA'),
  ('torch/_awaits/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_awaits/__init__.py',
   'DATA'),
  ('torch/__future__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/__future__.py',
   'DATA'),
  ('torch/__config__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/__config__.py',
   'DATA'),
  ('torch/autograd/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/autograd/__init__.py',
   'DATA'),
  ('torch/functional.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/functional.py',
   'DATA'),
  ('torch/_compile.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_compile.py',
   'DATA'),
  ('torch/_tensor_str.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_tensor_str.py',
   'DATA'),
  ('torch/serialization.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/serialization.py',
   'DATA'),
  ('torch/random.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/random.py',
   'DATA'),
  ('torch/amp/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/amp/__init__.py',
   'DATA'),
  ('torch/storage.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/storage.py',
   'DATA'),
  ('torch/_tensor.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_tensor.py',
   'DATA'),
  ('torch/fx/experimental/symbolic_shapes.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/fx/experimental/symbolic_shapes.py',
   'DATA'),
  ('torch/utils/_device.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/_device.py',
   'DATA'),
  ('torch/version.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/version.py',
   'DATA'),
  ('torch/torch_version.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/torch_version.py',
   'DATA'),
  ('torch/_utils_internal.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_utils_internal.py',
   'DATA'),
  ('torch/_utils.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_utils.py',
   'DATA'),
  ('torch/types.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/types.py',
   'DATA'),
  ('torch/_vmap_internals.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_vmap_internals.py',
   'DATA'),
  ('torch/_prims/__init__.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_prims/__init__.py',
   'DATA'),
  ('torch/_ops.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_ops.py',
   'DATA'),
  ('torch/_VF.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/_VF.py',
   'DATA'),
  ('libc10.dylib', 'torch/lib/libc10.dylib', 'SYMLINK'),
  ('libtorch_cpu.dylib', 'torch/lib/libtorch_cpu.dylib', 'SYMLINK'),
  ('libshm.dylib', 'torch/lib/libshm.dylib', 'SYMLINK'),
  ('libtorch.dylib', 'torch/lib/libtorch.dylib', 'SYMLINK'),
  ('libomp.dylib', 'torch/lib/libomp.dylib', 'SYMLINK'),
  ('libc++.1.dylib', 'libc++.1.0.dylib', 'SYMLINK'),
  ('libc++.1.0.dylib', 'torchaudio/.dylibs/libc++.1.0.dylib', 'SYMLINK'),
  ('QtWidgets',
   'PyQt6/Qt6/lib/QtWidgets.framework/Versions/A/QtWidgets',
   'SYMLINK'),
  ('QtGui', 'PyQt6/Qt6/lib/QtGui.framework/Versions/A/QtGui', 'SYMLINK'),
  ('QtCore', 'PyQt6/Qt6/lib/QtCore.framework/Versions/A/QtCore', 'SYMLINK'),
  ('QtSvg', 'PyQt6/Qt6/lib/QtSvg.framework/Versions/A/QtSvg', 'SYMLINK'),
  ('QtPdf', 'PyQt6/Qt6/lib/QtPdf.framework/Versions/A/QtPdf', 'SYMLINK'),
  ('QtNetwork',
   'PyQt6/Qt6/lib/QtNetwork.framework/Versions/A/QtNetwork',
   'SYMLINK'),
  ('liblzma.5.dylib', 'PIL/.dylibs/liblzma.5.dylib', 'SYMLINK'),
  ('libsf_error_state.dylib',
   'scipy/special/libsf_error_state.dylib',
   'SYMLINK'),
  ('libopenjp2.2.5.2.dylib', 'PIL/.dylibs/libopenjp2.2.5.2.dylib', 'SYMLINK'),
  ('libxcb.1.1.0.dylib', 'PIL/.dylibs/libxcb.1.1.0.dylib', 'SYMLINK'),
  ('libtiff.6.dylib', 'PIL/.dylibs/libtiff.6.dylib', 'SYMLINK'),
  ('libjpeg.62.4.0.dylib', 'PIL/.dylibs/libjpeg.62.4.0.dylib', 'SYMLINK'),
  ('libz.1.3.1.dylib', 'PIL/.dylibs/libz.1.3.1.dylib', 'SYMLINK'),
  ('libwebp.7.dylib', 'PIL/.dylibs/libwebp.7.dylib', 'SYMLINK'),
  ('libwebpmux.3.dylib', 'PIL/.dylibs/libwebpmux.3.dylib', 'SYMLINK'),
  ('libwebpdemux.2.dylib', 'PIL/.dylibs/libwebpdemux.2.dylib', 'SYMLINK'),
  ('libharfbuzz.0.dylib', 'PIL/.dylibs/libharfbuzz.0.dylib', 'SYMLINK'),
  ('libfreetype.6.dylib', 'PIL/.dylibs/libfreetype.6.dylib', 'SYMLINK'),
  ('liblcms2.2.dylib', 'PIL/.dylibs/liblcms2.2.dylib', 'SYMLINK'),
  ('libgfortran.5.dylib', 'scipy/.dylibs/libgfortran.5.dylib', 'SYMLINK'),
  ('libtorch_python.dylib', 'torch/lib/libtorch_python.dylib', 'SYMLINK'),
  ('QtTest', 'PyQt6/Qt6/lib/QtTest.framework/Versions/A/QtTest', 'SYMLINK'),
  ('QtOpenGL',
   'PyQt6/Qt6/lib/QtOpenGL.framework/Versions/A/QtOpenGL',
   'SYMLINK'),
  ('QtOpenGLWidgets',
   'PyQt6/Qt6/lib/QtOpenGLWidgets.framework/Versions/A/QtOpenGLWidgets',
   'SYMLINK'),
  ('QtDBus', 'PyQt6/Qt6/lib/QtDBus.framework/Versions/A/QtDBus', 'SYMLINK'),
  ('libXau.6.0.0.dylib', 'PIL/.dylibs/libXau.6.0.0.dylib', 'SYMLINK'),
  ('libsharpyuv.0.dylib', 'PIL/.dylibs/libsharpyuv.0.dylib', 'SYMLINK'),
  ('libpng16.16.dylib', 'PIL/.dylibs/libpng16.16.dylib', 'SYMLINK'),
  ('libbrotlidec.1.1.0.dylib',
   'PIL/.dylibs/libbrotlidec.1.1.0.dylib',
   'SYMLINK'),
  ('libgcc_s.1.1.dylib', 'scipy/.dylibs/libgcc_s.1.1.dylib', 'SYMLINK'),
  ('libquadmath.0.dylib', 'scipy/.dylibs/libquadmath.0.dylib', 'SYMLINK'),
  ('libbrotlicommon.1.1.0.dylib',
   'PIL/.dylibs/libbrotlicommon.1.1.0.dylib',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtCore.framework/QtCore',
   'Versions/Current/QtCore',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtCore.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtCore.framework/Versions/A/Resources/Info.plist',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/lib/QtCore.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt6/Qt6/lib/QtCore.framework/Versions/Current', 'A', 'SYMLINK'),
  ('PyQt6/Qt6/lib/QtDBus.framework/QtDBus',
   'Versions/Current/QtDBus',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtDBus.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtDBus.framework/Versions/A/Resources/Info.plist',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/lib/QtDBus.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt6/Qt6/lib/QtDBus.framework/Versions/Current', 'A', 'SYMLINK'),
  ('PyQt6/Qt6/lib/QtGui.framework/QtGui', 'Versions/Current/QtGui', 'SYMLINK'),
  ('PyQt6/Qt6/lib/QtGui.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtGui.framework/Versions/A/Resources/Info.plist',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/lib/QtGui.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt6/Qt6/lib/QtGui.framework/Versions/Current', 'A', 'SYMLINK'),
  ('PyQt6/Qt6/lib/QtNetwork.framework/QtNetwork',
   'Versions/Current/QtNetwork',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtNetwork.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtNetwork.framework/Versions/A/Resources/Info.plist',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/lib/QtNetwork.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt6/Qt6/lib/QtNetwork.framework/Versions/Current', 'A', 'SYMLINK'),
  ('PyQt6/Qt6/lib/QtOpenGL.framework/QtOpenGL',
   'Versions/Current/QtOpenGL',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtOpenGL.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtOpenGL.framework/Versions/A/Resources/Info.plist',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/lib/QtOpenGL.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt6/Qt6/lib/QtOpenGL.framework/Versions/Current', 'A', 'SYMLINK'),
  ('PyQt6/Qt6/lib/QtOpenGLWidgets.framework/QtOpenGLWidgets',
   'Versions/Current/QtOpenGLWidgets',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtOpenGLWidgets.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtOpenGLWidgets.framework/Versions/A/Resources/Info.plist',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/lib/QtOpenGLWidgets.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt6/Qt6/lib/QtOpenGLWidgets.framework/Versions/Current', 'A', 'SYMLINK'),
  ('PyQt6/Qt6/lib/QtPdf.framework/QtPdf', 'Versions/Current/QtPdf', 'SYMLINK'),
  ('PyQt6/Qt6/lib/QtPdf.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtPdf.framework/Versions/A/Resources/Info.plist',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/lib/QtPdf.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt6/Qt6/lib/QtPdf.framework/Versions/Current', 'A', 'SYMLINK'),
  ('PyQt6/Qt6/lib/QtSvg.framework/QtSvg', 'Versions/Current/QtSvg', 'SYMLINK'),
  ('PyQt6/Qt6/lib/QtSvg.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtSvg.framework/Versions/A/Resources/Info.plist',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/lib/QtSvg.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt6/Qt6/lib/QtSvg.framework/Versions/Current', 'A', 'SYMLINK'),
  ('PyQt6/Qt6/lib/QtTest.framework/QtTest',
   'Versions/Current/QtTest',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtTest.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtTest.framework/Versions/A/Resources/Info.plist',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/lib/QtTest.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt6/Qt6/lib/QtTest.framework/Versions/Current', 'A', 'SYMLINK'),
  ('PyQt6/Qt6/lib/QtWidgets.framework/QtWidgets',
   'Versions/Current/QtWidgets',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtWidgets.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtWidgets.framework/Versions/A/Resources/Info.plist',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/lib/QtWidgets.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt6/Qt6/lib/QtWidgets.framework/Versions/Current', 'A', 'SYMLINK')],
 'libpython3.12.dylib',
 False,
 False,
 False,
 [],
 'arm64',
 None,
 None)
