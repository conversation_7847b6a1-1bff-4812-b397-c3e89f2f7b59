
This file lists modules PyInstaller was not able to find. This does not
necessarily mean this module is required for running your program. Python and
Python 3rd-party packages include a lot of conditional or optional modules. For
example the module 'ntpath' only exists on Windows, whereas the module
'posixpath' only exists on Posix systems.

Types if import:
* top-level: imported at the top-level - look at these first
* conditional: imported within an if-statement
* delayed: imported within a function
* optional: imported within a try-except-statement

IMPORTANT: Do NOT post this list to the issue-tracker. Use it as a basis for
            tracking down the missing module yourself. Thanks!

missing module named _winapi - imported by encodings (delayed, conditional, optional), shutil (conditional), ntpath (optional), subprocess (conditional), multiprocessing.connection (optional), multiprocessing.spawn (delayed, conditional), multiprocessing.reduction (conditional), multiprocessing.shared_memory (conditional), multiprocessing.heap (conditional), multiprocessing.popen_spawn_win32 (top-level), asyncio.windows_events (top-level), asyncio.windows_utils (top-level), mimetypes (optional)
missing module named winreg - imported by importlib._bootstrap_external (conditional), platform (delayed, optional), mimetypes (optional), urllib.request (delayed, conditional, optional), platformdirs.windows (delayed, optional), setuptools._distutils._msvccompiler (top-level), numba.misc.appdirs (delayed), setuptools.msvc (conditional)
missing module named nt - imported by shutil (conditional), importlib._bootstrap_external (conditional), ntpath (optional), os (delayed, conditional, optional), ctypes (delayed, conditional)
missing module named _frozen_importlib_external - imported by importlib._bootstrap (delayed), importlib (optional), importlib.abc (optional), zipimport (top-level)
excluded module named _frozen_importlib - imported by importlib (optional), importlib.abc (optional), zipimport (top-level)
missing module named vms_lib - imported by platform (delayed, optional)
missing module named 'java.lang' - imported by platform (delayed, optional)
missing module named java - imported by platform (delayed)
missing module named msvcrt - imported by subprocess (optional), multiprocessing.spawn (delayed, conditional), multiprocessing.popen_spawn_win32 (top-level), asyncio.windows_events (top-level), asyncio.windows_utils (top-level), getpass (optional)
missing module named _winreg - imported by platform (delayed, optional)
missing module named _wmi - imported by platform (optional)
missing module named multiprocessing.set_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_context - imported by multiprocessing (top-level), multiprocessing.pool (top-level), multiprocessing.managers (top-level), multiprocessing.sharedctypes (top-level)
missing module named multiprocessing.TimeoutError - imported by multiprocessing (top-level), multiprocessing.pool (top-level), numba.testing.main (optional)
missing module named multiprocessing.BufferTooShort - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.AuthenticationError - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.freeze_support - imported by multiprocessing (conditional), numba.runtests (conditional)
missing module named usercustomize - imported by site (delayed, optional)
missing module named sitecustomize - imported by site (delayed, optional)
missing module named '_typeshed.importlib' - imported by pkg_resources (conditional)
missing module named _typeshed - imported by pkg_resources (conditional), setuptools.glob (conditional), setuptools.compat.py311 (conditional)
missing module named jnius - imported by platformdirs.android (delayed, optional)
missing module named importlib_resources - imported by setuptools._vendor.jaraco.text (optional)
missing module named _manylinux - imported by packaging._manylinux (delayed, optional), setuptools._vendor.packaging._manylinux (delayed, optional), wheel.vendored.packaging._manylinux (delayed, optional)
missing module named trove_classifiers - imported by setuptools.config._validate_pyproject.formats (optional)
missing module named dl - imported by setuptools.command.build_ext (conditional, optional)
missing module named Cython - imported by setuptools.command.build_ext (conditional, optional)
missing module named pyimod02_importers - imported by /Users/<USER>/miniconda3/lib/python3.12/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pkgutil.py (delayed), /Users/<USER>/miniconda3/lib/python3.12/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pkgres.py (delayed)
missing module named _dummy_thread - imported by numpy._core.arrayprint (optional), cffi.lock (conditional, optional)
missing module named numpy._core.void - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ushort - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.unsignedinteger - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ulonglong - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ulong - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.uintp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.uintc - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.uint64 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.uint32 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.uint16 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.uint - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ubyte - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.trunc - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.true_divide - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.timedelta64 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.tanh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.tan - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.subtract - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.str_ - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.square - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.spacing - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.sinh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.signedinteger - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.short - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.rint - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.right_shift - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.remainder - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.radians - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.rad2deg - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.power - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.positive - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.pi - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.not_equal - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.negative - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.modf - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.mod - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.minimum - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.maximum - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.longdouble - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.long - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logical_xor - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logical_or - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logical_not - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logical_and - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logaddexp2 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logaddexp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.log2 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.log1p - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.log - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.less_equal - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.less - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.left_shift - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ldexp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.lcm - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.integer - imported by numpy._core (conditional), numpy (conditional), numpy.fft._helper (top-level)
missing module named numpy._core.int8 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.int64 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.int32 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.int16 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.hypot - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.heaviside - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.half - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.greater_equal - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.greater - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.gcd - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.frompyfunc - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.frexp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.fmod - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.fmin - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.fmax - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.floor_divide - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.floor - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.floating - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.float_power - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.float16 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.fabs - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.expm1 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.exp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.euler_gamma - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.equal - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.e - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.divmod - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.degrees - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.deg2rad - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.datetime64 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.cosh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.cos - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.copysign - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.conjugate - imported by numpy._core (conditional), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.conj - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.complex64 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.clongdouble - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.character - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ceil - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.cbrt - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bytes_ - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.byte - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bool_ - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bitwise_xor - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bitwise_or - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bitwise_count - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bitwise_and - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arctanh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arctan2 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arctan - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arcsinh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arcsin - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arccosh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arccos - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ones - imported by numpy._core (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.hstack - imported by numpy._core (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.atleast_1d - imported by numpy._core (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.atleast_3d - imported by numpy._core (top-level), numpy.lib._shape_base_impl (top-level), numpy (conditional)
missing module named numpy._core.vstack - imported by numpy._core (top-level), numpy.lib._shape_base_impl (top-level), numpy (conditional)
missing module named numpy._core.linspace - imported by numpy._core (top-level), numpy.lib._index_tricks_impl (top-level), numpy (conditional)
missing module named numpy._core.result_type - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.number - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.max - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.array2string - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.signbit - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.isscalar - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.isnat - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy (conditional)
missing module named numpy._core.array_repr - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy (conditional)
missing module named numpy._core.arange - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy (conditional), numpy.fft._helper (top-level)
missing module named numpy._core.float32 - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy (conditional)
missing module named numpy._core.vecdot - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.matrix_transpose - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.tensordot - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.outer - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.cross - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.trace - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.diagonal - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.reciprocal - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.sort - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.argsort - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.sign - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.isnan - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.count_nonzero - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.divide - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.swapaxes - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.matmul - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.object_ - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.asanyarray - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.intp - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (top-level), numpy (conditional)
missing module named numpy._core.atleast_2d - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.prod - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.amax - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.amin - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.moveaxis - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.errstate - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.finfo - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.isfinite - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.sum - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.sqrt - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.multiply - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.add - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.dot - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.inf - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.all - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.newaxis - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.complexfloating - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.inexact - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.cdouble - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.csingle - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.double - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.single - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.intc - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.empty_like - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.empty - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (top-level), numpy (conditional), numpy.fft._helper (top-level)
missing module named numpy._core.zeros - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.array - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.iinfo - imported by numpy._core (top-level), numpy.lib._twodim_base_impl (top-level), numpy (conditional)
missing module named numpy._core.transpose - imported by numpy._core (top-level), numpy.lib._function_base_impl (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.ndarray - imported by numpy._core (top-level), numpy.lib._utils_impl (top-level), numpy.testing._private.utils (top-level), numpy (conditional)
missing module named numpy._core.asarray - imported by numpy._core (top-level), numpy.lib._array_utils_impl (top-level), numpy.linalg._linalg (top-level), numpy (conditional), numpy.fft._pocketfft (top-level), numpy.fft._helper (top-level)
missing module named pyodide_js - imported by threadpoolctl (delayed, optional)
missing module named psutil - imported by numpy.testing._private.utils (delayed, optional)
missing module named win32pdh - imported by numpy.testing._private.utils (delayed, conditional)
missing module named numpy._typing._ufunc - imported by numpy._typing (conditional)
missing module named numba.np.ufunc.tbbpool - imported by numba.np.ufunc (delayed, conditional, optional), numba.np.ufunc.parallel (delayed, conditional, optional)
missing module named numba.core.types.StringLiteral - imported by numba.core.types (top-level), numba.np.arrayobj (top-level)
missing module named 'setuptools._distutils.msvc9compiler' - imported by cffi._shimmed_dist_utils (conditional, optional)
missing module named imp - imported by cffi.verifier (conditional), cffi._imp_emulation (optional)
missing module named dummy_thread - imported by cffi.lock (conditional, optional)
missing module named thread - imported by cffi.lock (conditional, optional), cffi.cparser (conditional, optional)
missing module named cStringIO - imported by cffi.ffiplatform (optional)
missing module named cPickle - imported by pycparser.ply.yacc (delayed, optional)
missing module named cffi._pycparser - imported by cffi (optional), cffi.cparser (optional)
missing module named 'win32com.shell' - imported by numba.misc.appdirs (conditional, optional)
missing module named 'com.sun' - imported by numba.misc.appdirs (delayed, conditional, optional)
missing module named com - imported by numba.misc.appdirs (delayed)
missing module named win32api - imported by numba.misc.appdirs (delayed, conditional, optional)
missing module named win32com - imported by numba.misc.appdirs (delayed)
missing module named numba.types.uint64 - imported by numba.types (top-level), numba.cuda.mathimpl (top-level)
missing module named numba.types.int64 - imported by numba.types (top-level), numba.cuda.mathimpl (top-level), numba.cuda.libdevicefuncs (top-level)
missing module named numba.types.float64 - imported by numba.types (top-level), numba.cuda.mathimpl (top-level), numba.cuda.libdevicefuncs (top-level)
missing module named numba.types.float32 - imported by numba.types (top-level), numba.cuda.mathimpl (top-level), numba.cuda.libdevicefuncs (top-level)
missing module named numba.types.Tuple - imported by numba.types (top-level), numba.cuda.libdevicefuncs (top-level)
missing module named numba.types.void - imported by numba.types (top-level), numba.cuda.libdevicefuncs (top-level)
missing module named numba.types.int32 - imported by numba.types (top-level), numba.cuda.libdevicefuncs (top-level)
missing module named numba.types.int16 - imported by numba.types (top-level), numba.cuda.libdevicefuncs (top-level)
missing module named ptxcompiler - imported by numba.cuda.cudadrv.driver (delayed, optional), numba.cuda.testing (delayed, optional)
missing module named cubinlinker - imported by numba.cuda.cudadrv.driver (delayed, optional), numba.cuda.testing (delayed, optional)
missing module named coverage - imported by numba.tests.support (optional)
missing module named 'numba_rvsdg.core' - imported by numba.core.rvsdg_frontend.rvsdg.bc2rvsdg (top-level), numba.core.rvsdg_frontend.rvsdg.regionpasses (top-level), numba.core.rvsdg_frontend.rvsdg.regionrenderer (top-level)
missing module named graphviz - imported by llvmlite.binding.analysis (delayed), numba.core.ir (delayed, optional), numba.core.controlflow (delayed, optional), numba.misc.inspection (delayed, optional), numba.core.codegen (delayed), numba.core.rvsdg_frontend.rvsdg.regionrenderer (delayed)
missing module named 'numba_rvsdg.rendering' - imported by numba.core.rvsdg_frontend.rvsdg.bc2rvsdg (top-level)
missing module named numba_rvsdg - imported by numba.core.rvsdg_frontend (delayed, optional)
missing module named xmlrunner - imported by numba.testing (delayed, conditional)
missing module named git - imported by numba.testing.main (delayed, optional)
missing module named Queue - imported by numba.testing.main (optional)
missing module named numba.cuda.is_available - imported by numba.cuda (delayed), numba.cuda.testing (delayed)
missing module named numba.core.types.Tuple - imported by numba.core.types (delayed), numba.core.types.iterators (delayed), numba.core.types.npytypes (delayed)
missing module named numba.core.types.Array - imported by numba.core.types (delayed), numba.core.types.abstract (delayed)
missing module named llvmlite.ir.Constant - imported by llvmlite.ir (delayed), llvmlite.ir.types (delayed), numba.core.base (top-level), numba.core.pythonapi (top-level), numba.core.lowering (top-level), numba.core.generators (top-level), numba.core.callwrapper (top-level), numba.pycc.llvm_types (top-level), numba.cpython.unicode (top-level), numba.cpython.numbers (top-level), numba.cpython.mathimpl (top-level), numba.np.arrayobj (top-level), numba.np.npdatetime (top-level), numba.np.ufunc.wrappers (top-level), numba.np.math.mathimpl (top-level), numba.np.math.numbers (top-level)
missing module named numba.typed.List - imported by numba.typed (delayed), numba.core.codegen (delayed), numba.typed.listobject (delayed), numba.core.typing.typeof (delayed)
missing module named numba.typed.Dict - imported by numba.typed (delayed, conditional), numba.typed.dictobject (delayed, conditional), numba.typed.dictimpl (delayed), numba.core.typing.typeof (delayed)
missing module named numba.core.types.NoneType - imported by numba.core.types (top-level), numba.typed.listobject (top-level)
missing module named numba.core.types.Type - imported by numba.core.types (top-level), numba.typed.listobject (top-level), numba.typed.dictobject (top-level)
missing module named numba.core.types.ListTypeIteratorType - imported by numba.core.types (top-level), numba.typed.listobject (top-level)
missing module named numba.core.types.ListTypeIterableType - imported by numba.core.types (top-level), numba.typed.listobject (top-level)
missing module named numba.core.types.ListType - imported by numba.core.types (top-level), numba.typed.listobject (top-level), numba.typed.typedlist (top-level)
missing module named numba.core.types.DictType - imported by numba.core.types (top-level), numba.typed.typeddict (top-level), numba.typed.dictobject (top-level)
missing module named numba.core.types.DictIteratorType - imported by numba.core.types (top-level), numba.typed.dictobject (top-level)
missing module named numba.core.types.DictValuesIterableType - imported by numba.core.types (top-level), numba.typed.dictobject (top-level)
missing module named numba.core.types.DictKeysIterableType - imported by numba.core.types (top-level), numba.typed.dictobject (top-level)
missing module named numba.core.types.DictItemsIterableType - imported by numba.core.types (top-level), numba.typed.dictobject (top-level)
missing module named llvmlite.ir.Value - imported by llvmlite.ir (delayed), llvmlite.ir.types (delayed)
missing module named llvmlite.ir.GlobalVariable - imported by llvmlite.ir (delayed), llvmlite.ir.types (delayed)
missing module named llvmlite.ir.Module - imported by llvmlite.ir (delayed), llvmlite.ir.types (delayed)
missing module named cuda - imported by numba.cuda.cudadrv.driver (conditional), numba.core.config (delayed, conditional, optional)
missing module named 'IPython.display' - imported by llvmlite.binding.analysis (delayed, conditional, optional)
missing module named numba.core.types.ClassInstanceType - imported by numba.core.types (top-level), numba.experimental.jitclass.overloads (top-level)
missing module named numba.core.types.WrapperAddressProtocol - imported by numba.core.types (top-level), numba.experimental.function_type (top-level)
missing module named numba.core.types.FunctionPrototype - imported by numba.core.types (top-level), numba.experimental.function_type (top-level)
missing module named numba.core.types.UndefinedFunctionType - imported by numba.core.types (top-level), numba.experimental.function_type (top-level)
missing module named numba.core.types.FunctionType - imported by numba.core.types (top-level), numba.experimental.function_type (top-level)
missing module named 'pygments.formatters' - imported by numba.core.ir (delayed, conditional, optional), numba.core.codegen (delayed, conditional, optional), numba.core.annotations.pretty_annotate (delayed, optional), numba.core.utils (delayed, conditional, optional)
missing module named 'pygments.lexers' - imported by numba.core.codegen (delayed, conditional, optional), numba.core.annotations.pretty_annotate (delayed, optional), numba.core.utils (delayed, conditional, optional)
missing module named pygments - imported by numba.core.ir (delayed, conditional, optional), numba.core.codegen (delayed, conditional, optional), numba.core.annotations.pretty_annotate (delayed, optional), numba.core.utils (delayed, conditional, optional)
missing module named numba.uint8 - imported by numba (top-level), numba.np.random.random_methods (top-level)
missing module named numba.uint16 - imported by numba (top-level), numba.np.random.random_methods (top-level)
missing module named numba.uint32 - imported by numba (top-level), numba.np.random.random_methods (top-level)
missing module named numba.uint64 - imported by numba (top-level), numba.np.random.random_methods (top-level)
missing module named numba.int64 - imported by numba (top-level), numba.np.random.distributions (top-level)
missing module named numba.float32 - imported by numba (top-level), numba.np.random.generator_core (top-level), numba.np.random.distributions (top-level)
missing module named 'elftools.elf' - imported by numba.core.codegen (delayed)
missing module named elftools - imported by numba.core.codegen (delayed)
missing module named 'pygments.style' - imported by numba.misc.dump_style (top-level)
missing module named 'pygments.token' - imported by numba.misc.dump_style (top-level)
missing module named 'pygments.lexer' - imported by numba.misc.dump_style (top-level)
missing module named 'pygments.styles' - imported by numba.misc.dump_style (top-level)
missing module named r2pipe - imported by numba.misc.inspection (delayed, optional)
missing module named colorama - imported by numba.core.errors (optional)
missing module named shiboken6 - imported by pyqtgraph.Qt (conditional)
missing module named shiboken2 - imported by pyqtgraph.Qt (conditional)
missing module named sip - imported by pyqtgraph.Qt (conditional, optional), pyqtgraph.debug (delayed, optional)
missing module named pyside2uic - imported by pyqtgraph.Qt (delayed, conditional, optional)
missing module named 'matplotlib.figure' - imported by pyqtgraph.widgets.MatplotlibWidget (top-level)
missing module named 'matplotlib.backends' - imported by pyqtgraph.widgets.MatplotlibWidget (top-level)
missing module named metaarray - imported by pyqtgraph.widgets.DataTreeWidget (optional), pyqtgraph.flowchart.library.common (optional)
missing module named 'scipy.signal' - imported by pyqtgraph.flowchart.library.functions (delayed, optional), pyqtgraph.flowchart.library.Filters (delayed, optional)
missing module named 'scipy.ndimage' - imported by pyqtgraph.functions (delayed, conditional, optional), pyqtgraph.flowchart.library.Filters (delayed, optional)
missing module named 'OpenGL.GL' - imported by pyqtgraph.widgets.RawImageWidget (optional)
missing module named colorcet - imported by pyqtgraph.colormap (delayed, conditional, optional), pyqtgraph.widgets.ColorMapMenu (delayed)
missing module named cupy - imported by pyqtgraph.util.cupy_helper (delayed, conditional, optional)
missing module named 'h5py.highlevel' - imported by pyqtgraph.metaarray.MetaArray (conditional, optional)
missing module named h5py - imported by pyqtgraph.metaarray.MetaArray (optional), pyqtgraph.exporters.HDF5Exporter (delayed)
missing module named bottleneck - imported by pyqtgraph.imageview.ImageView (optional)
missing module named OpenGL - imported by pyqtgraph.graphicsItems.PlotCurveItem (delayed)
missing module named pyqtgraph.PlotItem - imported by pyqtgraph (top-level), pyqtgraph.exporters.CSVExporter (top-level), pyqtgraph.exporters.HDF5Exporter (top-level), pyqtgraph.exporters.Matplotlib (top-level)
missing module named pyqtgraph.ErrorBarItem - imported by pyqtgraph (top-level), pyqtgraph.exporters.CSVExporter (top-level)
missing module named 'matplotlib.pyplot' - imported by pyqtgraph.colormap (delayed, conditional, optional)
missing module named 'numpy_distutils.cpuinfo' - imported by numpy.f2py.diagnose (delayed, conditional, optional)
missing module named 'numpy_distutils.fcompiler' - imported by numpy.f2py.diagnose (delayed, conditional, optional)
missing module named 'numpy_distutils.command' - imported by numpy.f2py.diagnose (delayed, conditional, optional)
missing module named numpy_distutils - imported by numpy.f2py.diagnose (delayed, optional)
missing module named unicodedata2 - imported by charset_normalizer.utils (optional)
missing module named numpy._distributor_init_local - imported by numpy (optional), numpy._distributor_init (optional)
missing module named _overlapped - imported by asyncio.windows_events (top-level)
missing module named asyncio.DefaultEventLoopPolicy - imported by asyncio (delayed, conditional), asyncio.events (delayed, conditional)
