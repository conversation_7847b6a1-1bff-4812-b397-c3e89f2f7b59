(['/Users/<USER>/Local/zip/app1.py'],
 ['/Users/<USER>/Local', '/Users/<USER>/Local/zip'],
 ['PyQt6.QtCore',
  'PyQt6.QtGui',
  'PyQt6.QtWidgets',
  'PyQt6.sip',
  'sounddevice',
  'sounddevice._internal',
  'numpy',
  'numpy.fft',
  'numpy.random',
  'pyqtgraph',
  'pyqtgraph.graphicsItems',
  'pyqtgraph.widgets',
  'eventkit',
  'numba',
  'numba.core',
  'hifiscan',
  'hifiscan.analyzer',
  'hifiscan.audio',
  'hifiscan.io_',
  'analyzer',
  'audio',
  'io_'],
 [('/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/_pyinstaller',
   0),
  ('/Users/<USER>/miniconda3/lib/python3.12/site-packages/_pyinstaller_hooks_contrib/stdhooks',
   -1000),
  ('/Users/<USER>/miniconda3/lib/python3.12/site-packages/_pyinstaller_hooks_contrib',
   -1000)],
 {},
 ['pytest',
  'black',
  'flake8',
  'mypy',
  'sphinx',
  'docutils',
  'tkinter',
  'matplotlib.backends._backend_tk',
  'scipy',
  'pandas',
  'matplotlib',
  'IPython',
  'jupyter',
  '__main__'],
 [],
 False,
 {},
 0,
 [],
 [('docs/INSTALL.md', '/Users/<USER>/Local/zip/INSTALL.md', 'DATA'),
  ('docs/真实麦克风校准指南.md', '/Users/<USER>/Local/zip/真实麦克风校准指南.md', 'DATA'),
  ('docs/麦克风校准使用说明.md', '/Users/<USER>/Local/zip/麦克风校准使用说明.md', 'DATA'),
  ('logo.ico', '/Users/<USER>/Local/zip/logo.ico', 'DATA'),
  ('logo.png', '/Users/<USER>/Local/zip/logo.png', 'DATA'),
  ('preset.json', '/Users/<USER>/Local/zip/preset.json', 'DATA'),
  ('start.png', '/Users/<USER>/Local/zip/start.png', 'DATA')],
 '3.12.4 | packaged by Anaconda, Inc. | (main, Jun 18 2024, 10:07:17) [Clang '
 '14.0.6 ]',
 [('pyi_rth_inspect',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyInstaller/hooks/rthooks/pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyInstaller/hooks/rthooks/pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyInstaller/hooks/rthooks/pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_pyqtgraph_multiprocess',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/_pyinstaller_hooks_contrib/rthooks/pyi_rth_pyqtgraph_multiprocess.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt6',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pyqt6.py',
   'PYSOURCE'),
  ('app1', '/Users/<USER>/Local/zip/app1.py', 'PYSOURCE')],
 [('_pyi_rth_utils.qt',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyInstaller/fake-modules/_pyi_rth_utils/qt.py',
   'PYMODULE'),
  ('importlib',
   '/Users/<USER>/miniconda3/lib/python3.12/importlib/__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   '/Users/<USER>/miniconda3/lib/python3.12/importlib/_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   '/Users/<USER>/miniconda3/lib/python3.12/importlib/metadata/__init__.py',
   'PYMODULE'),
  ('typing', '/Users/<USER>/miniconda3/lib/python3.12/typing.py', 'PYMODULE'),
  ('importlib.abc',
   '/Users/<USER>/miniconda3/lib/python3.12/importlib/abc.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   '/Users/<USER>/miniconda3/lib/python3.12/importlib/resources/abc.py',
   'PYMODULE'),
  ('importlib.resources',
   '/Users/<USER>/miniconda3/lib/python3.12/importlib/resources/__init__.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   '/Users/<USER>/miniconda3/lib/python3.12/importlib/resources/_legacy.py',
   'PYMODULE'),
  ('importlib.resources._common',
   '/Users/<USER>/miniconda3/lib/python3.12/importlib/resources/_common.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   '/Users/<USER>/miniconda3/lib/python3.12/importlib/resources/_adapters.py',
   'PYMODULE'),
  ('tempfile',
   '/Users/<USER>/miniconda3/lib/python3.12/tempfile.py',
   'PYMODULE'),
  ('shutil', '/Users/<USER>/miniconda3/lib/python3.12/shutil.py', 'PYMODULE'),
  ('tarfile',
   '/Users/<USER>/miniconda3/lib/python3.12/tarfile.py',
   'PYMODULE'),
  ('argparse',
   '/Users/<USER>/miniconda3/lib/python3.12/argparse.py',
   'PYMODULE'),
  ('gettext',
   '/Users/<USER>/miniconda3/lib/python3.12/gettext.py',
   'PYMODULE'),
  ('gzip', '/Users/<USER>/miniconda3/lib/python3.12/gzip.py', 'PYMODULE'),
  ('_compression',
   '/Users/<USER>/miniconda3/lib/python3.12/_compression.py',
   'PYMODULE'),
  ('struct', '/Users/<USER>/miniconda3/lib/python3.12/struct.py', 'PYMODULE'),
  ('lzma', '/Users/<USER>/miniconda3/lib/python3.12/lzma.py', 'PYMODULE'),
  ('bz2', '/Users/<USER>/miniconda3/lib/python3.12/bz2.py', 'PYMODULE'),
  ('fnmatch',
   '/Users/<USER>/miniconda3/lib/python3.12/fnmatch.py',
   'PYMODULE'),
  ('importlib._abc',
   '/Users/<USER>/miniconda3/lib/python3.12/importlib/_abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   '/Users/<USER>/miniconda3/lib/python3.12/importlib/machinery.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   '/Users/<USER>/miniconda3/lib/python3.12/importlib/metadata/_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   '/Users/<USER>/miniconda3/lib/python3.12/importlib/metadata/_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   '/Users/<USER>/miniconda3/lib/python3.12/importlib/metadata/_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   '/Users/<USER>/miniconda3/lib/python3.12/importlib/metadata/_meta.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   '/Users/<USER>/miniconda3/lib/python3.12/importlib/metadata/_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   '/Users/<USER>/miniconda3/lib/python3.12/importlib/metadata/_text.py',
   'PYMODULE'),
  ('email.message',
   '/Users/<USER>/miniconda3/lib/python3.12/email/message.py',
   'PYMODULE'),
  ('email.policy',
   '/Users/<USER>/miniconda3/lib/python3.12/email/policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   '/Users/<USER>/miniconda3/lib/python3.12/email/contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime',
   '/Users/<USER>/miniconda3/lib/python3.12/email/quoprimime.py',
   'PYMODULE'),
  ('string', '/Users/<USER>/miniconda3/lib/python3.12/string.py', 'PYMODULE'),
  ('email.headerregistry',
   '/Users/<USER>/miniconda3/lib/python3.12/email/headerregistry.py',
   'PYMODULE'),
  ('email._header_value_parser',
   '/Users/<USER>/miniconda3/lib/python3.12/email/_header_value_parser.py',
   'PYMODULE'),
  ('urllib',
   '/Users/<USER>/miniconda3/lib/python3.12/urllib/__init__.py',
   'PYMODULE'),
  ('email.iterators',
   '/Users/<USER>/miniconda3/lib/python3.12/email/iterators.py',
   'PYMODULE'),
  ('email.generator',
   '/Users/<USER>/miniconda3/lib/python3.12/email/generator.py',
   'PYMODULE'),
  ('email._encoded_words',
   '/Users/<USER>/miniconda3/lib/python3.12/email/_encoded_words.py',
   'PYMODULE'),
  ('email.charset',
   '/Users/<USER>/miniconda3/lib/python3.12/email/charset.py',
   'PYMODULE'),
  ('email.encoders',
   '/Users/<USER>/miniconda3/lib/python3.12/email/encoders.py',
   'PYMODULE'),
  ('email.base64mime',
   '/Users/<USER>/miniconda3/lib/python3.12/email/base64mime.py',
   'PYMODULE'),
  ('email._policybase',
   '/Users/<USER>/miniconda3/lib/python3.12/email/_policybase.py',
   'PYMODULE'),
  ('email.header',
   '/Users/<USER>/miniconda3/lib/python3.12/email/header.py',
   'PYMODULE'),
  ('email.errors',
   '/Users/<USER>/miniconda3/lib/python3.12/email/errors.py',
   'PYMODULE'),
  ('email.utils',
   '/Users/<USER>/miniconda3/lib/python3.12/email/utils.py',
   'PYMODULE'),
  ('email._parseaddr',
   '/Users/<USER>/miniconda3/lib/python3.12/email/_parseaddr.py',
   'PYMODULE'),
  ('calendar',
   '/Users/<USER>/miniconda3/lib/python3.12/calendar.py',
   'PYMODULE'),
  ('urllib.parse',
   '/Users/<USER>/miniconda3/lib/python3.12/urllib/parse.py',
   'PYMODULE'),
  ('ipaddress',
   '/Users/<USER>/miniconda3/lib/python3.12/ipaddress.py',
   'PYMODULE'),
  ('socket', '/Users/<USER>/miniconda3/lib/python3.12/socket.py', 'PYMODULE'),
  ('selectors',
   '/Users/<USER>/miniconda3/lib/python3.12/selectors.py',
   'PYMODULE'),
  ('quopri', '/Users/<USER>/miniconda3/lib/python3.12/quopri.py', 'PYMODULE'),
  ('getopt', '/Users/<USER>/miniconda3/lib/python3.12/getopt.py', 'PYMODULE'),
  ('inspect',
   '/Users/<USER>/miniconda3/lib/python3.12/inspect.py',
   'PYMODULE'),
  ('token', '/Users/<USER>/miniconda3/lib/python3.12/token.py', 'PYMODULE'),
  ('dis', '/Users/<USER>/miniconda3/lib/python3.12/dis.py', 'PYMODULE'),
  ('opcode', '/Users/<USER>/miniconda3/lib/python3.12/opcode.py', 'PYMODULE'),
  ('ast', '/Users/<USER>/miniconda3/lib/python3.12/ast.py', 'PYMODULE'),
  ('contextlib',
   '/Users/<USER>/miniconda3/lib/python3.12/contextlib.py',
   'PYMODULE'),
  ('textwrap',
   '/Users/<USER>/miniconda3/lib/python3.12/textwrap.py',
   'PYMODULE'),
  ('zipfile',
   '/Users/<USER>/miniconda3/lib/python3.12/zipfile/__init__.py',
   'PYMODULE'),
  ('zipfile._path',
   '/Users/<USER>/miniconda3/lib/python3.12/zipfile/_path/__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   '/Users/<USER>/miniconda3/lib/python3.12/zipfile/_path/glob.py',
   'PYMODULE'),
  ('py_compile',
   '/Users/<USER>/miniconda3/lib/python3.12/py_compile.py',
   'PYMODULE'),
  ('importlib.util',
   '/Users/<USER>/miniconda3/lib/python3.12/importlib/util.py',
   'PYMODULE'),
  ('email',
   '/Users/<USER>/miniconda3/lib/python3.12/email/__init__.py',
   'PYMODULE'),
  ('email.parser',
   '/Users/<USER>/miniconda3/lib/python3.12/email/parser.py',
   'PYMODULE'),
  ('email.feedparser',
   '/Users/<USER>/miniconda3/lib/python3.12/email/feedparser.py',
   'PYMODULE'),
  ('csv', '/Users/<USER>/miniconda3/lib/python3.12/csv.py', 'PYMODULE'),
  ('importlib.readers',
   '/Users/<USER>/miniconda3/lib/python3.12/importlib/readers.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   '/Users/<USER>/miniconda3/lib/python3.12/importlib/resources/readers.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   '/Users/<USER>/miniconda3/lib/python3.12/importlib/resources/_itertools.py',
   'PYMODULE'),
  ('tokenize',
   '/Users/<USER>/miniconda3/lib/python3.12/tokenize.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   '/Users/<USER>/miniconda3/lib/python3.12/importlib/_bootstrap.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyInstaller/fake-modules/_pyi_rth_utils/__init__.py',
   'PYMODULE'),
  ('_distutils_hack',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/_distutils_hack/__init__.py',
   'PYMODULE'),
  ('setuptools',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/__init__.py',
   'PYMODULE'),
  ('setuptools.msvc',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/msvc.py',
   'PYMODULE'),
  ('typing_extensions',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/_vendor/more_itertools/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/_vendor/more_itertools/recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/_vendor/more_itertools/more.py',
   'PYMODULE'),
  ('queue', '/Users/<USER>/miniconda3/lib/python3.12/queue.py', 'PYMODULE'),
  ('platform',
   '/Users/<USER>/miniconda3/lib/python3.12/platform.py',
   'PYMODULE'),
  ('plistlib',
   '/Users/<USER>/miniconda3/lib/python3.12/plistlib.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   '/Users/<USER>/miniconda3/lib/python3.12/xml/parsers/expat.py',
   'PYMODULE'),
  ('xml.parsers',
   '/Users/<USER>/miniconda3/lib/python3.12/xml/parsers/__init__.py',
   'PYMODULE'),
  ('xml',
   '/Users/<USER>/miniconda3/lib/python3.12/xml/__init__.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   '/Users/<USER>/miniconda3/lib/python3.12/xml/sax/expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   '/Users/<USER>/miniconda3/lib/python3.12/xml/sax/saxutils.py',
   'PYMODULE'),
  ('urllib.request',
   '/Users/<USER>/miniconda3/lib/python3.12/urllib/request.py',
   'PYMODULE'),
  ('getpass',
   '/Users/<USER>/miniconda3/lib/python3.12/getpass.py',
   'PYMODULE'),
  ('nturl2path',
   '/Users/<USER>/miniconda3/lib/python3.12/nturl2path.py',
   'PYMODULE'),
  ('ftplib', '/Users/<USER>/miniconda3/lib/python3.12/ftplib.py', 'PYMODULE'),
  ('netrc', '/Users/<USER>/miniconda3/lib/python3.12/netrc.py', 'PYMODULE'),
  ('mimetypes',
   '/Users/<USER>/miniconda3/lib/python3.12/mimetypes.py',
   'PYMODULE'),
  ('http.cookiejar',
   '/Users/<USER>/miniconda3/lib/python3.12/http/cookiejar.py',
   'PYMODULE'),
  ('http',
   '/Users/<USER>/miniconda3/lib/python3.12/http/__init__.py',
   'PYMODULE'),
  ('ssl', '/Users/<USER>/miniconda3/lib/python3.12/ssl.py', 'PYMODULE'),
  ('urllib.response',
   '/Users/<USER>/miniconda3/lib/python3.12/urllib/response.py',
   'PYMODULE'),
  ('urllib.error',
   '/Users/<USER>/miniconda3/lib/python3.12/urllib/error.py',
   'PYMODULE'),
  ('http.client',
   '/Users/<USER>/miniconda3/lib/python3.12/http/client.py',
   'PYMODULE'),
  ('hashlib',
   '/Users/<USER>/miniconda3/lib/python3.12/hashlib.py',
   'PYMODULE'),
  ('bisect', '/Users/<USER>/miniconda3/lib/python3.12/bisect.py', 'PYMODULE'),
  ('xml.sax',
   '/Users/<USER>/miniconda3/lib/python3.12/xml/sax/__init__.py',
   'PYMODULE'),
  ('xml.sax.handler',
   '/Users/<USER>/miniconda3/lib/python3.12/xml/sax/handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   '/Users/<USER>/miniconda3/lib/python3.12/xml/sax/_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   '/Users/<USER>/miniconda3/lib/python3.12/xml/sax/xmlreader.py',
   'PYMODULE'),
  ('subprocess',
   '/Users/<USER>/miniconda3/lib/python3.12/subprocess.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/_distutils/command/build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/_distutils/command/__init__.py',
   'PYMODULE'),
  ('setuptools._distutils',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/_distutils/__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/_distutils/version.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/_distutils/archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/_distutils/spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/_distutils/debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/_distutils/dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/_distutils/file_util.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/_distutils/_msvccompiler.py',
   'PYMODULE'),
  ('unittest.mock',
   '/Users/<USER>/miniconda3/lib/python3.12/unittest/mock.py',
   'PYMODULE'),
  ('unittest',
   '/Users/<USER>/miniconda3/lib/python3.12/unittest/__init__.py',
   'PYMODULE'),
  ('unittest.async_case',
   '/Users/<USER>/miniconda3/lib/python3.12/unittest/async_case.py',
   'PYMODULE'),
  ('contextvars',
   '/Users/<USER>/miniconda3/lib/python3.12/contextvars.py',
   'PYMODULE'),
  ('unittest.signals',
   '/Users/<USER>/miniconda3/lib/python3.12/unittest/signals.py',
   'PYMODULE'),
  ('unittest.main',
   '/Users/<USER>/miniconda3/lib/python3.12/unittest/main.py',
   'PYMODULE'),
  ('unittest.runner',
   '/Users/<USER>/miniconda3/lib/python3.12/unittest/runner.py',
   'PYMODULE'),
  ('unittest.loader',
   '/Users/<USER>/miniconda3/lib/python3.12/unittest/loader.py',
   'PYMODULE'),
  ('unittest.suite',
   '/Users/<USER>/miniconda3/lib/python3.12/unittest/suite.py',
   'PYMODULE'),
  ('unittest.case',
   '/Users/<USER>/miniconda3/lib/python3.12/unittest/case.py',
   'PYMODULE'),
  ('unittest._log',
   '/Users/<USER>/miniconda3/lib/python3.12/unittest/_log.py',
   'PYMODULE'),
  ('difflib',
   '/Users/<USER>/miniconda3/lib/python3.12/difflib.py',
   'PYMODULE'),
  ('unittest.result',
   '/Users/<USER>/miniconda3/lib/python3.12/unittest/result.py',
   'PYMODULE'),
  ('unittest.util',
   '/Users/<USER>/miniconda3/lib/python3.12/unittest/util.py',
   'PYMODULE'),
  ('pkgutil',
   '/Users/<USER>/miniconda3/lib/python3.12/pkgutil.py',
   'PYMODULE'),
  ('zipimport',
   '/Users/<USER>/miniconda3/lib/python3.12/zipimport.py',
   'PYMODULE'),
  ('pprint', '/Users/<USER>/miniconda3/lib/python3.12/pprint.py', 'PYMODULE'),
  ('dataclasses',
   '/Users/<USER>/miniconda3/lib/python3.12/dataclasses.py',
   'PYMODULE'),
  ('concurrent.futures',
   '/Users/<USER>/miniconda3/lib/python3.12/concurrent/futures/__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   '/Users/<USER>/miniconda3/lib/python3.12/concurrent/futures/thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   '/Users/<USER>/miniconda3/lib/python3.12/concurrent/futures/process.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   '/Users/<USER>/miniconda3/lib/python3.12/multiprocessing/synchronize.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   '/Users/<USER>/miniconda3/lib/python3.12/multiprocessing/heap.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   '/Users/<USER>/miniconda3/lib/python3.12/multiprocessing/resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   '/Users/<USER>/miniconda3/lib/python3.12/multiprocessing/spawn.py',
   'PYMODULE'),
  ('runpy', '/Users/<USER>/miniconda3/lib/python3.12/runpy.py', 'PYMODULE'),
  ('multiprocessing.util',
   '/Users/<USER>/miniconda3/lib/python3.12/multiprocessing/util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   '/Users/<USER>/miniconda3/lib/python3.12/multiprocessing/forkserver.py',
   'PYMODULE'),
  ('multiprocessing.process',
   '/Users/<USER>/miniconda3/lib/python3.12/multiprocessing/process.py',
   'PYMODULE'),
  ('multiprocessing.context',
   '/Users/<USER>/miniconda3/lib/python3.12/multiprocessing/context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   '/Users/<USER>/miniconda3/lib/python3.12/multiprocessing/popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   '/Users/<USER>/miniconda3/lib/python3.12/multiprocessing/popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   '/Users/<USER>/miniconda3/lib/python3.12/multiprocessing/popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   '/Users/<USER>/miniconda3/lib/python3.12/multiprocessing/popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   '/Users/<USER>/miniconda3/lib/python3.12/multiprocessing/sharedctypes.py',
   'PYMODULE'),
  ('ctypes',
   '/Users/<USER>/miniconda3/lib/python3.12/ctypes/__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   '/Users/<USER>/miniconda3/lib/python3.12/ctypes/_endian.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   '/Users/<USER>/miniconda3/lib/python3.12/multiprocessing/pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   '/Users/<USER>/miniconda3/lib/python3.12/multiprocessing/dummy/__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   '/Users/<USER>/miniconda3/lib/python3.12/multiprocessing/dummy/connection.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   '/Users/<USER>/miniconda3/lib/python3.12/multiprocessing/managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   '/Users/<USER>/miniconda3/lib/python3.12/multiprocessing/shared_memory.py',
   'PYMODULE'),
  ('secrets',
   '/Users/<USER>/miniconda3/lib/python3.12/secrets.py',
   'PYMODULE'),
  ('hmac', '/Users/<USER>/miniconda3/lib/python3.12/hmac.py', 'PYMODULE'),
  ('multiprocessing.reduction',
   '/Users/<USER>/miniconda3/lib/python3.12/multiprocessing/reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   '/Users/<USER>/miniconda3/lib/python3.12/multiprocessing/resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   '/Users/<USER>/miniconda3/lib/python3.12/multiprocessing/queues.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   '/Users/<USER>/miniconda3/lib/python3.12/multiprocessing/connection.py',
   'PYMODULE'),
  ('xmlrpc.client',
   '/Users/<USER>/miniconda3/lib/python3.12/xmlrpc/client.py',
   'PYMODULE'),
  ('xmlrpc',
   '/Users/<USER>/miniconda3/lib/python3.12/xmlrpc/__init__.py',
   'PYMODULE'),
  ('decimal',
   '/Users/<USER>/miniconda3/lib/python3.12/decimal.py',
   'PYMODULE'),
  ('_pydecimal',
   '/Users/<USER>/miniconda3/lib/python3.12/_pydecimal.py',
   'PYMODULE'),
  ('numbers',
   '/Users/<USER>/miniconda3/lib/python3.12/numbers.py',
   'PYMODULE'),
  ('multiprocessing',
   '/Users/<USER>/miniconda3/lib/python3.12/multiprocessing/__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   '/Users/<USER>/miniconda3/lib/python3.12/concurrent/futures/_base.py',
   'PYMODULE'),
  ('concurrent',
   '/Users/<USER>/miniconda3/lib/python3.12/concurrent/__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/_distutils/ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/_distutils/fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/_distutils/util.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py38',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/_distutils/compat/py38.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/_distutils/compat/__init__.py',
   'PYMODULE'),
  ('_aix_support',
   '/Users/<USER>/miniconda3/lib/python3.12/_aix_support.py',
   'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/_vendor/jaraco/functools/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('sysconfig',
   '/Users/<USER>/miniconda3/lib/python3.12/sysconfig.py',
   'PYMODULE'),
  ('_sysconfigdata__darwin_darwin',
   '/Users/<USER>/miniconda3/lib/python3.12/_sysconfigdata__darwin_darwin.py',
   'PYMODULE'),
  ('_osx_support',
   '/Users/<USER>/miniconda3/lib/python3.12/_osx_support.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/_distutils/sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/_distutils/text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/_distutils/compat/py39.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/_distutils/extension.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/_distutils/_modified.py',
   'PYMODULE'),
  ('site', '/Users/<USER>/miniconda3/lib/python3.12/site.py', 'PYMODULE'),
  ('rlcompleter',
   '/Users/<USER>/miniconda3/lib/python3.12/rlcompleter.py',
   'PYMODULE'),
  ('_sitebuiltins',
   '/Users/<USER>/miniconda3/lib/python3.12/_sitebuiltins.py',
   'PYMODULE'),
  ('pydoc', '/Users/<USER>/miniconda3/lib/python3.12/pydoc.py', 'PYMODULE'),
  ('webbrowser',
   '/Users/<USER>/miniconda3/lib/python3.12/webbrowser.py',
   'PYMODULE'),
  ('shlex', '/Users/<USER>/miniconda3/lib/python3.12/shlex.py', 'PYMODULE'),
  ('http.server',
   '/Users/<USER>/miniconda3/lib/python3.12/http/server.py',
   'PYMODULE'),
  ('socketserver',
   '/Users/<USER>/miniconda3/lib/python3.12/socketserver.py',
   'PYMODULE'),
  ('html',
   '/Users/<USER>/miniconda3/lib/python3.12/html/__init__.py',
   'PYMODULE'),
  ('html.entities',
   '/Users/<USER>/miniconda3/lib/python3.12/html/entities.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   '/Users/<USER>/miniconda3/lib/python3.12/pydoc_data/topics.py',
   'PYMODULE'),
  ('pydoc_data',
   '/Users/<USER>/miniconda3/lib/python3.12/pydoc_data/__init__.py',
   'PYMODULE'),
  ('tty', '/Users/<USER>/miniconda3/lib/python3.12/tty.py', 'PYMODULE'),
  ('setuptools._distutils._log',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/_distutils/_log.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/_distutils/errors.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/_distutils/core.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/_distutils/dist.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/_distutils/versionpredicate.py',
   'PYMODULE'),
  ('configparser',
   '/Users/<USER>/miniconda3/lib/python3.12/configparser.py',
   'PYMODULE'),
  ('packaging.utils',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/packaging/utils.py',
   'PYMODULE'),
  ('packaging',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/packaging/__init__.py',
   'PYMODULE'),
  ('pkg_resources',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pkg_resources/__init__.py',
   'PYMODULE'),
  ('packaging.metadata',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/packaging/metadata.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/packaging/_tokenizer.py',
   'PYMODULE'),
  ('packaging._structures',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/packaging/_structures.py',
   'PYMODULE'),
  ('packaging._parser',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/packaging/_parser.py',
   'PYMODULE'),
  ('packaging._elffile',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/packaging/_elffile.py',
   'PYMODULE'),
  ('platformdirs',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/platformdirs/__init__.py',
   'PYMODULE'),
  ('platformdirs.android',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/platformdirs/android.py',
   'PYMODULE'),
  ('platformdirs.unix',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/platformdirs/unix.py',
   'PYMODULE'),
  ('platformdirs.macos',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/platformdirs/macos.py',
   'PYMODULE'),
  ('platformdirs.windows',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/platformdirs/windows.py',
   'PYMODULE'),
  ('platformdirs.version',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/platformdirs/version.py',
   'PYMODULE'),
  ('platformdirs.api',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/platformdirs/api.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/_vendor/jaraco/text/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/_vendor/jaraco/context.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/_vendor/backports/tarfile/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/_vendor/backports/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/_vendor/backports/tarfile/compat/py38.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/_vendor/backports/tarfile/compat/__init__.py',
   'PYMODULE'),
  ('backports',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/_vendor/backports/__init__.py',
   'PYMODULE'),
  ('packaging.specifiers',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/packaging/specifiers.py',
   'PYMODULE'),
  ('packaging.requirements',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/packaging/requirements.py',
   'PYMODULE'),
  ('packaging.markers',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/packaging/markers.py',
   'PYMODULE'),
  ('packaging._musllinux',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/packaging/_musllinux.py',
   'PYMODULE'),
  ('packaging._manylinux',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/packaging/_manylinux.py',
   'PYMODULE'),
  ('packaging.version',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/packaging/version.py',
   'PYMODULE'),
  ('packaging.tags',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/packaging/tags.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/_distutils/cmd.py',
   'PYMODULE'),
  ('setuptools.warnings',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/warnings.py',
   'PYMODULE'),
  ('setuptools.version',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/version.py',
   'PYMODULE'),
  ('setuptools._importlib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/_importlib.py',
   'PYMODULE'),
  ('importlib_metadata',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/importlib_metadata/__init__.py',
   'PYMODULE'),
  ('zipp.compat.overlay',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/zipp/compat/overlay.py',
   'PYMODULE'),
  ('zipp.compat',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/zipp/compat/__init__.py',
   'PYMODULE'),
  ('zipp',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/zipp/__init__.py',
   'PYMODULE'),
  ('zipp._functools',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/zipp/_functools.py',
   'PYMODULE'),
  ('zipp.glob',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/zipp/glob.py',
   'PYMODULE'),
  ('zipp.compat.py310',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/zipp/compat/py310.py',
   'PYMODULE'),
  ('importlib_metadata._adapters',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/importlib_metadata/_adapters.py',
   'PYMODULE'),
  ('importlib_metadata._text',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/importlib_metadata/_text.py',
   'PYMODULE'),
  ('importlib_metadata.compat.py311',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/importlib_metadata/compat/py311.py',
   'PYMODULE'),
  ('importlib_metadata.compat.py39',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/importlib_metadata/compat/py39.py',
   'PYMODULE'),
  ('importlib_metadata.compat',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/importlib_metadata/compat/__init__.py',
   'PYMODULE'),
  ('importlib_metadata._itertools',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/importlib_metadata/_itertools.py',
   'PYMODULE'),
  ('importlib_metadata._functools',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/importlib_metadata/_functools.py',
   'PYMODULE'),
  ('importlib_metadata._compat',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/importlib_metadata/_compat.py',
   'PYMODULE'),
  ('importlib_metadata._collections',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/importlib_metadata/_collections.py',
   'PYMODULE'),
  ('importlib_metadata._meta',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/importlib_metadata/_meta.py',
   'PYMODULE'),
  ('setuptools.extension',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/extension.py',
   'PYMODULE'),
  ('setuptools._path',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/_path.py',
   'PYMODULE'),
  ('setuptools.dist',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/dist.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/command/bdist_wheel.py',
   'PYMODULE'),
  ('wheel.macosx_libfile',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/wheel/macosx_libfile.py',
   'PYMODULE'),
  ('wheel',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/wheel/__init__.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/command/egg_info.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/_distutils/filelist.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/command/_requirestxt.py',
   'PYMODULE'),
  ('setuptools.glob',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/glob.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/command/setopt.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/command/sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/_distutils/command/sdist.py',
   'PYMODULE'),
  ('setuptools.command.build',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/command/build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/_distutils/command/build.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/command/bdist_egg.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/unicode_utils.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/compat/py39.py',
   'PYMODULE'),
  ('setuptools.compat',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/compat/__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/compat/py311.py',
   'PYMODULE'),
  ('wheel.wheelfile',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/wheel/wheelfile.py',
   'PYMODULE'),
  ('wheel.util',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/wheel/util.py',
   'PYMODULE'),
  ('wheel.cli',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/wheel/cli/__init__.py',
   'PYMODULE'),
  ('wheel.cli.tags',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/wheel/cli/tags.py',
   'PYMODULE'),
  ('wheel.cli.convert',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/wheel/cli/convert.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.tags',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/wheel/vendored/packaging/tags.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._musllinux',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/wheel/vendored/packaging/_musllinux.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._elffile',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/wheel/vendored/packaging/_elffile.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._manylinux',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/wheel/vendored/packaging/_manylinux.py',
   'PYMODULE'),
  ('wheel.vendored.packaging',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/wheel/vendored/packaging/__init__.py',
   'PYMODULE'),
  ('wheel.vendored',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/wheel/vendored/__init__.py',
   'PYMODULE'),
  ('wheel.metadata',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/wheel/metadata.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.requirements',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/wheel/vendored/packaging/requirements.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.utils',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/wheel/vendored/packaging/utils.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.version',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/wheel/vendored/packaging/version.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._structures',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/wheel/vendored/packaging/_structures.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.specifiers',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/wheel/vendored/packaging/specifiers.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.markers',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/wheel/vendored/packaging/markers.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._tokenizer',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/wheel/vendored/packaging/_tokenizer.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._parser',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/wheel/vendored/packaging/_parser.py',
   'PYMODULE'),
  ('wheel.cli.pack',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/wheel/cli/pack.py',
   'PYMODULE'),
  ('wheel.cli.unpack',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/wheel/cli/unpack.py',
   'PYMODULE'),
  ('setuptools.installer',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/installer.py',
   'PYMODULE'),
  ('setuptools.wheel',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/wheel.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/_distutils/log.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/config/setupcfg.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/config/expand.py',
   'PYMODULE'),
  ('setuptools.errors',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/errors.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/config/pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/config/_validate_pyproject/__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/config/_validate_pyproject/fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/config/_validate_pyproject/fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/config/_validate_pyproject/extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/config/_validate_pyproject/error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/config/_validate_pyproject/formats.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/_vendor/packaging/requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/_vendor/packaging/utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/_vendor/packaging/version.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/_vendor/packaging/_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/_vendor/packaging/tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/_vendor/packaging/_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/_vendor/packaging/_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/_vendor/packaging/_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/_vendor/packaging/specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/_vendor/packaging/markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/_vendor/packaging/_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/_vendor/packaging/_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/_vendor/packaging/__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/compat/py310.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/_vendor/tomli/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/_vendor/tomli/_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/_vendor/tomli/_types.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/_vendor/tomli/_re.py',
   'PYMODULE'),
  ('tomllib',
   '/Users/<USER>/miniconda3/lib/python3.12/tomllib/__init__.py',
   'PYMODULE'),
  ('tomllib._parser',
   '/Users/<USER>/miniconda3/lib/python3.12/tomllib/_parser.py',
   'PYMODULE'),
  ('tomllib._types',
   '/Users/<USER>/miniconda3/lib/python3.12/tomllib/_types.py',
   'PYMODULE'),
  ('tomllib._re',
   '/Users/<USER>/miniconda3/lib/python3.12/tomllib/_re.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/config/_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/config/__init__.py',
   'PYMODULE'),
  ('glob', '/Users/<USER>/miniconda3/lib/python3.12/glob.py', 'PYMODULE'),
  ('setuptools._shutil',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/_shutil.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/windows_support.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   '/Users/<USER>/miniconda3/lib/python3.12/ctypes/wintypes.py',
   'PYMODULE'),
  ('setuptools.command',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/command/__init__.py',
   'PYMODULE'),
  ('setuptools.command.build_ext',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/command/build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/_distutils/command/bdist.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/_entry_points.py',
   'PYMODULE'),
  ('setuptools._itertools',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/_itertools.py',
   'PYMODULE'),
  ('setuptools.discovery',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/discovery.py',
   'PYMODULE'),
  ('setuptools.depends',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/depends.py',
   'PYMODULE'),
  ('setuptools._imp',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/_imp.py',
   'PYMODULE'),
  ('setuptools.logging',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/monkey.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/_core_metadata.py',
   'PYMODULE'),
  ('setuptools._reqs',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/_reqs.py',
   'PYMODULE'),
  ('setuptools._normalization',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/_normalization.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/_distutils_hack/override.py',
   'PYMODULE'),
  ('__future__',
   '/Users/<USER>/miniconda3/lib/python3.12/__future__.py',
   'PYMODULE'),
  ('audio', '/Users/<USER>/Local/zip/audio.py', 'PYMODULE'),
  ('numpy.typing',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/typing/__init__.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/_pytesttester.py',
   'PYMODULE'),
  ('numpy.testing',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/testing/__init__.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/testing/overrides.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/lib/recfunctions.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/lib/_iotools.py',
   'PYMODULE'),
  ('numpy._utils',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/_utils/__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/_utils/_convertions.py',
   'PYMODULE'),
  ('numpy._core.numeric',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/_core/numeric.py',
   'PYMODULE'),
  ('numpy._core._asarray',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/_core/_asarray.py',
   'PYMODULE'),
  ('numpy._core.arrayprint',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/_core/arrayprint.py',
   'PYMODULE'),
  ('numpy._core.fromnumeric',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/_core/fromnumeric.py',
   'PYMODULE'),
  ('numpy._core._methods',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/_core/_methods.py',
   'PYMODULE'),
  ('numpy.lib._stride_tricks_impl',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/lib/_stride_tricks_impl.py',
   'PYMODULE'),
  ('numpy._globals',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/_globals.py',
   'PYMODULE'),
  ('numpy._core._exceptions',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/_core/_exceptions.py',
   'PYMODULE'),
  ('numpy._core._ufunc_config',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/_core/_ufunc_config.py',
   'PYMODULE'),
  ('numpy.exceptions',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/exceptions.py',
   'PYMODULE'),
  ('numpy._core.shape_base',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/_core/shape_base.py',
   'PYMODULE'),
  ('numpy._core.numerictypes',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/_core/numerictypes.py',
   'PYMODULE'),
  ('numpy._core._dtype',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/_core/_dtype.py',
   'PYMODULE'),
  ('numpy._core._type_aliases',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/_core/_type_aliases.py',
   'PYMODULE'),
  ('numpy._core._string_helpers',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/_core/_string_helpers.py',
   'PYMODULE'),
  ('numpy._core.multiarray',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/_core/multiarray.py',
   'PYMODULE'),
  ('numpy._core',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/_core/__init__.py',
   'PYMODULE'),
  ('numpy._core._internal',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/_core/_internal.py',
   'PYMODULE'),
  ('numpy._core._dtype_ctypes',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/_core/_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs_scalars',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/_core/_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/_core/_add_newdocs.py',
   'PYMODULE'),
  ('numpy._core.einsumfunc',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/_core/einsumfunc.py',
   'PYMODULE'),
  ('numpy._core._machar',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/_core/_machar.py',
   'PYMODULE'),
  ('numpy._core.function_base',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/_core/function_base.py',
   'PYMODULE'),
  ('numpy._core.memmap',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/_core/memmap.py',
   'PYMODULE'),
  ('numpy._core.getlimits',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/_core/getlimits.py',
   'PYMODULE'),
  ('numpy.version',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/version.py',
   'PYMODULE'),
  ('numpy._core.records',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/_core/records.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/ma/mrecords.py',
   'PYMODULE'),
  ('numpy.ma',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/ma/__init__.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/ma/extras.py',
   'PYMODULE'),
  ('numpy.lib._index_tricks_impl',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/lib/_index_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/lib/stride_tricks.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/matrixlib/__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/matrixlib/defmatrix.py',
   'PYMODULE'),
  ('numpy.linalg',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/linalg/__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/linalg/linalg.py',
   'PYMODULE'),
  ('numpy.linalg._linalg',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/linalg/_linalg.py',
   'PYMODULE'),
  ('numpy.lib._twodim_base_impl',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/lib/_twodim_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._function_base_impl',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/lib/_function_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._histograms_impl',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/lib/_histograms_impl.py',
   'PYMODULE'),
  ('numpy.lib.array_utils',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/lib/array_utils.py',
   'PYMODULE'),
  ('numpy.lib._array_utils_impl',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/lib/_array_utils_impl.py',
   'PYMODULE'),
  ('numpy.ma.core',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/ma/core.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/_utils/_inspect.py',
   'PYMODULE'),
  ('numpy.lib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/lib/__init__.py',
   'PYMODULE'),
  ('numpy.lib._version',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/lib/_version.py',
   'PYMODULE'),
  ('numpy.lib._arraypad_impl',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/lib/_arraypad_impl.py',
   'PYMODULE'),
  ('numpy.lib._arrayterator_impl',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/lib/_arrayterator_impl.py',
   'PYMODULE'),
  ('numpy.lib._npyio_impl',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/lib/_npyio_impl.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/lib/_datasource.py',
   'PYMODULE'),
  ('numpy.lib._polynomial_impl',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/lib/_polynomial_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraysetops_impl',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/lib/_arraysetops_impl.py',
   'PYMODULE'),
  ('numpy.lib._utils_impl',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/lib/_utils_impl.py',
   'PYMODULE'),
  ('threadpoolctl',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/threadpoolctl.py',
   'PYMODULE'),
  ('ctypes.util',
   '/Users/<USER>/miniconda3/lib/python3.12/ctypes/util.py',
   'PYMODULE'),
  ('ctypes._aix',
   '/Users/<USER>/miniconda3/lib/python3.12/ctypes/_aix.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   '/Users/<USER>/miniconda3/lib/python3.12/ctypes/macholib/dyld.py',
   'PYMODULE'),
  ('ctypes.macholib',
   '/Users/<USER>/miniconda3/lib/python3.12/ctypes/macholib/__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/ctypes/macholib/dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   '/Users/<USER>/miniconda3/lib/python3.12/ctypes/macholib/framework.py',
   'PYMODULE'),
  ('numpy.lib._ufunclike_impl',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/lib/_ufunclike_impl.py',
   'PYMODULE'),
  ('numpy.lib._shape_base_impl',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/lib/_shape_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._nanfunctions_impl',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/lib/_nanfunctions_impl.py',
   'PYMODULE'),
  ('numpy.lib._type_check_impl',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/lib/_type_check_impl.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/lib/scimath.py',
   'PYMODULE'),
  ('numpy.lib._scimath_impl',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/lib/_scimath_impl.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/lib/npyio.py',
   'PYMODULE'),
  ('numpy.lib.format',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/lib/format.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/lib/mixins.py',
   'PYMODULE'),
  ('numpy.lib.introspect',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/lib/introspect.py',
   'PYMODULE'),
  ('numpy._core.umath',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/_core/umath.py',
   'PYMODULE'),
  ('numpy._core.overrides',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/_core/overrides.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/testing/_private/extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/testing/_private/utils.py',
   'PYMODULE'),
  ('doctest',
   '/Users/<USER>/miniconda3/lib/python3.12/doctest.py',
   'PYMODULE'),
  ('pdb', '/Users/<USER>/miniconda3/lib/python3.12/pdb.py', 'PYMODULE'),
  ('code', '/Users/<USER>/miniconda3/lib/python3.12/code.py', 'PYMODULE'),
  ('codeop', '/Users/<USER>/miniconda3/lib/python3.12/codeop.py', 'PYMODULE'),
  ('bdb', '/Users/<USER>/miniconda3/lib/python3.12/bdb.py', 'PYMODULE'),
  ('cmd', '/Users/<USER>/miniconda3/lib/python3.12/cmd.py', 'PYMODULE'),
  ('numpy.testing._private',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/testing/_private/__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/_typing/_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/_typing/_array_like.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/_typing/_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/_typing/__init__.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/_typing/_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/_typing/_shape.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/_typing/_scalars.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/_typing/_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/_typing/_nbit.py',
   'PYMODULE'),
  ('analyzer', '/Users/<USER>/Local/zip/analyzer.py', 'PYMODULE'),
  ('hifiscan.io_',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/hifiscan/io_.py',
   'PYMODULE'),
  ('wave', '/Users/<USER>/miniconda3/lib/python3.12/wave.py', 'PYMODULE'),
  ('uuid', '/Users/<USER>/miniconda3/lib/python3.12/uuid.py', 'PYMODULE'),
  ('hifiscan.audio',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/hifiscan/audio.py',
   'PYMODULE'),
  ('hifiscan.analyzer',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/hifiscan/analyzer.py',
   'PYMODULE'),
  ('numba.core',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/__init__.py',
   'PYMODULE'),
  ('numba.core.config',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/config.py',
   'PYMODULE'),
  ('numba.np.ufunc.parallel',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/np/ufunc/parallel.py',
   'PYMODULE'),
  ('numba.extending',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/extending.py',
   'PYMODULE'),
  ('numba.core.extending',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/extending.py',
   'PYMODULE'),
  ('numba.core.pythonapi',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/pythonapi.py',
   'PYMODULE'),
  ('numba.core.boxing',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/boxing.py',
   'PYMODULE'),
  ('numba.cpython.slicing',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cpython/slicing.py',
   'PYMODULE'),
  ('numba.np',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/np/__init__.py',
   'PYMODULE'),
  ('numba.np.npyimpl',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/np/npyimpl.py',
   'PYMODULE'),
  ('numba.core.typing.npydecl',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/typing/npydecl.py',
   'PYMODULE'),
  ('numba.np.ufunc.sigparse',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/np/ufunc/sigparse.py',
   'PYMODULE'),
  ('numba.np.arrayobj',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/np/arrayobj.py',
   'PYMODULE'),
  ('numba.cpython.unsafe.tuple',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cpython/unsafe/tuple.py',
   'PYMODULE'),
  ('numba.cpython.unsafe',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cpython/unsafe/__init__.py',
   'PYMODULE'),
  ('numba.misc.mergesort',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/misc/mergesort.py',
   'PYMODULE'),
  ('numba.misc.quicksort',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/misc/quicksort.py',
   'PYMODULE'),
  ('numba.misc',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/misc/__init__.py',
   'PYMODULE'),
  ('numba.misc.cffiimpl',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/misc/cffiimpl.py',
   'PYMODULE'),
  ('numba.misc.dummyarray',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/misc/dummyarray.py',
   'PYMODULE'),
  ('numba.misc.literal',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/misc/literal.py',
   'PYMODULE'),
  ('numba.misc.gdb_hook',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/misc/gdb_hook.py',
   'PYMODULE'),
  ('numba.misc.special',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/misc/special.py',
   'PYMODULE'),
  ('numba.np.npdatetime',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/np/npdatetime.py',
   'PYMODULE'),
  ('numba.np.npyfuncs',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/np/npyfuncs.py',
   'PYMODULE'),
  ('numba.np.math.numbers',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/np/math/numbers.py',
   'PYMODULE'),
  ('numba.cpython.mathimpl',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cpython/mathimpl.py',
   'PYMODULE'),
  ('numba.cpython.unsafe.numbers',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cpython/unsafe/numbers.py',
   'PYMODULE'),
  ('numba.np.math.mathimpl',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/np/math/mathimpl.py',
   'PYMODULE'),
  ('numba.np.math.cmathimpl',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/np/math/cmathimpl.py',
   'PYMODULE'),
  ('numba.np.math',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/np/math/__init__.py',
   'PYMODULE'),
  ('numba.np.arraymath',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/np/arraymath.py',
   'PYMODULE'),
  ('numba.np.linalg',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/np/linalg.py',
   'PYMODULE'),
  ('numba.np.ufunc_db',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/np/ufunc_db.py',
   'PYMODULE'),
  ('numba.np.npdatetime_helpers',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/np/npdatetime_helpers.py',
   'PYMODULE'),
  ('numba.cpython.listobj',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cpython/listobj.py',
   'PYMODULE'),
  ('numba.cpython.setobj',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cpython/setobj.py',
   'PYMODULE'),
  ('numba.cpython',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cpython/__init__.py',
   'PYMODULE'),
  ('numba.cpython.printimpl',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cpython/printimpl.py',
   'PYMODULE'),
  ('numba.cpython.cmathimpl',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cpython/cmathimpl.py',
   'PYMODULE'),
  ('numba.cpython.tupleobj',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cpython/tupleobj.py',
   'PYMODULE'),
  ('numba.cpython.rangeobj',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cpython/rangeobj.py',
   'PYMODULE'),
  ('numba.parfors.parfor',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/parfors/parfor.py',
   'PYMODULE'),
  ('numba.stencils',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/stencils/__init__.py',
   'PYMODULE'),
  ('numba.parfors.array_analysis',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/parfors/array_analysis.py',
   'PYMODULE'),
  ('numba.stencils.stencil',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/stencils/stencil.py',
   'PYMODULE'),
  ('numba.core.typed_passes',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/typed_passes.py',
   'PYMODULE'),
  ('numba.core.annotations.type_annotations',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/annotations/type_annotations.py',
   'PYMODULE'),
  ('jinja2',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/jinja2/__init__.py',
   'PYMODULE'),
  ('jinja2.ext',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/jinja2/ext.py',
   'PYMODULE'),
  ('jinja2.parser',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/jinja2/parser.py',
   'PYMODULE'),
  ('jinja2.lexer',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/jinja2/lexer.py',
   'PYMODULE'),
  ('jinja2._identifier',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/jinja2/_identifier.py',
   'PYMODULE'),
  ('jinja2.defaults',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/jinja2/defaults.py',
   'PYMODULE'),
  ('jinja2.tests',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/jinja2/tests.py',
   'PYMODULE'),
  ('jinja2.filters',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/jinja2/filters.py',
   'PYMODULE'),
  ('jinja2.sandbox',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/jinja2/sandbox.py',
   'PYMODULE'),
  ('jinja2.async_utils',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/jinja2/async_utils.py',
   'PYMODULE'),
  ('markupsafe',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/markupsafe/__init__.py',
   'PYMODULE'),
  ('markupsafe._native',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/markupsafe/_native.py',
   'PYMODULE'),
  ('jinja2.utils',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/jinja2/utils.py',
   'PYMODULE'),
  ('jinja2.constants',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/jinja2/constants.py',
   'PYMODULE'),
  ('jinja2.runtime',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/jinja2/runtime.py',
   'PYMODULE'),
  ('jinja2.loaders',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/jinja2/loaders.py',
   'PYMODULE'),
  ('jinja2.exceptions',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/jinja2/exceptions.py',
   'PYMODULE'),
  ('jinja2.environment',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/jinja2/environment.py',
   'PYMODULE'),
  ('jinja2.debug',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/jinja2/debug.py',
   'PYMODULE'),
  ('jinja2.compiler',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/jinja2/compiler.py',
   'PYMODULE'),
  ('jinja2.visitor',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/jinja2/visitor.py',
   'PYMODULE'),
  ('jinja2.optimizer',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/jinja2/optimizer.py',
   'PYMODULE'),
  ('jinja2.idtracking',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/jinja2/idtracking.py',
   'PYMODULE'),
  ('jinja2.bccache',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/jinja2/bccache.py',
   'PYMODULE'),
  ('jinja2.nodes',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/jinja2/nodes.py',
   'PYMODULE'),
  ('numba.core.annotations',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/annotations/__init__.py',
   'PYMODULE'),
  ('numba.core.compiler_machinery',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/compiler_machinery.py',
   'PYMODULE'),
  ('numba.core.tracing',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/tracing.py',
   'PYMODULE'),
  ('numba.core.compiler_lock',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/compiler_lock.py',
   'PYMODULE'),
  ('timeit', '/Users/<USER>/miniconda3/lib/python3.12/timeit.py', 'PYMODULE'),
  ('numba.parfors.parfor_lowering',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/parfors/parfor_lowering.py',
   'PYMODULE'),
  ('numba.parfors.parfor_lowering_utils',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/parfors/parfor_lowering_utils.py',
   'PYMODULE'),
  ('numba.core.types.functions',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/types/functions.py',
   'PYMODULE'),
  ('numba.core.target_extension',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/target_extension.py',
   'PYMODULE'),
  ('numba.core.decorators',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/decorators.py',
   'PYMODULE'),
  ('numba.core.ccallback',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/ccallback.py',
   'PYMODULE'),
  ('cffi',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/cffi/__init__.py',
   'PYMODULE'),
  ('cffi.error',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/cffi/error.py',
   'PYMODULE'),
  ('cffi.api',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/cffi/api.py',
   'PYMODULE'),
  ('cffi.recompiler',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/cffi/recompiler.py',
   'PYMODULE'),
  ('cffi.cffi_opcode',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/cffi/cffi_opcode.py',
   'PYMODULE'),
  ('cffi._shimmed_dist_utils',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/cffi/_shimmed_dist_utils.py',
   'PYMODULE'),
  ('cffi.verifier',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/cffi/verifier.py',
   'PYMODULE'),
  ('cffi.lock',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/cffi/lock.py',
   'PYMODULE'),
  ('cffi.pkgconfig',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/cffi/pkgconfig.py',
   'PYMODULE'),
  ('cffi.vengine_cpy',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/cffi/vengine_cpy.py',
   'PYMODULE'),
  ('cffi._imp_emulation',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/cffi/_imp_emulation.py',
   'PYMODULE'),
  ('cffi.vengine_gen',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/cffi/vengine_gen.py',
   'PYMODULE'),
  ('cffi.ffiplatform',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/cffi/ffiplatform.py',
   'PYMODULE'),
  ('cffi.cparser',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/cffi/cparser.py',
   'PYMODULE'),
  ('pycparser.lextab',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pycparser/lextab.py',
   'PYMODULE'),
  ('pycparser.yacctab',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pycparser/yacctab.py',
   'PYMODULE'),
  ('pycparser',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pycparser/__init__.py',
   'PYMODULE'),
  ('pycparser.c_parser',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pycparser/c_parser.py',
   'PYMODULE'),
  ('pycparser.ast_transforms',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pycparser/ast_transforms.py',
   'PYMODULE'),
  ('pycparser.plyparser',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pycparser/plyparser.py',
   'PYMODULE'),
  ('pycparser.c_lexer',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pycparser/c_lexer.py',
   'PYMODULE'),
  ('pycparser.ply.lex',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pycparser/ply/lex.py',
   'PYMODULE'),
  ('pycparser.ply.yacc',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pycparser/ply/yacc.py',
   'PYMODULE'),
  ('pycparser.ply',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pycparser/ply/__init__.py',
   'PYMODULE'),
  ('pycparser.c_ast',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pycparser/c_ast.py',
   'PYMODULE'),
  ('cffi.commontypes',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/cffi/commontypes.py',
   'PYMODULE'),
  ('cffi.model',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/cffi/model.py',
   'PYMODULE'),
  ('numba.core.typing.ctypes_utils',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/typing/ctypes_utils.py',
   'PYMODULE'),
  ('numba.core.caching',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/caching.py',
   'PYMODULE'),
  ('numba.core.base',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/base.py',
   'PYMODULE'),
  ('numba.core.runtime.context',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/runtime/context.py',
   'PYMODULE'),
  ('numba.core.runtime',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/runtime/__init__.py',
   'PYMODULE'),
  ('numba.core.runtime.nrt',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/runtime/nrt.py',
   'PYMODULE'),
  ('numba.core.runtime.nrtdynmod',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/runtime/nrtdynmod.py',
   'PYMODULE'),
  ('numba.misc.appdirs',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/misc/appdirs.py',
   'PYMODULE'),
  ('numba.cuda',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cuda/__init__.py',
   'PYMODULE'),
  ('numba.cuda.compiler',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cuda/compiler.py',
   'PYMODULE'),
  ('numba.cuda.descriptor',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cuda/descriptor.py',
   'PYMODULE'),
  ('numba.core.options',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/options.py',
   'PYMODULE'),
  ('numba.core.descriptors',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/descriptors.py',
   'PYMODULE'),
  ('numba.cuda.target',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cuda/target.py',
   'PYMODULE'),
  ('numba.np.unsafe.ndarray',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/np/unsafe/ndarray.py',
   'PYMODULE'),
  ('numba.np.unsafe',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/np/unsafe/__init__.py',
   'PYMODULE'),
  ('numba.cuda.mathimpl',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cuda/mathimpl.py',
   'PYMODULE'),
  ('numba.types',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/types/__init__.py',
   'PYMODULE'),
  ('numba.cuda.dispatcher',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cuda/dispatcher.py',
   'PYMODULE'),
  ('numba.cuda.types',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cuda/types.py',
   'PYMODULE'),
  ('numba.cuda.errors',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cuda/errors.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv.devices',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cuda/cudadrv/devices.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv.driver',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cuda/cudadrv/driver.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv.devicearray',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cuda/cudadrv/devicearray.py',
   'PYMODULE'),
  ('numba.cuda.kernels.transpose',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cuda/kernels/transpose.py',
   'PYMODULE'),
  ('numba.cuda.kernels',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cuda/kernels/__init__.py',
   'PYMODULE'),
  ('numba.cuda.kernels.reduction',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cuda/kernels/reduction.py',
   'PYMODULE'),
  ('numba.cuda.api_util',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cuda/api_util.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv.nvrtc',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cuda/cudadrv/nvrtc.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv.libs',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cuda/cudadrv/libs.py',
   'PYMODULE'),
  ('numba.cuda.cuda_paths',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cuda/cuda_paths.py',
   'PYMODULE'),
  ('numba.misc.findlib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/misc/findlib.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv.enums',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cuda/cudadrv/enums.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv.drvapi',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cuda/cudadrv/drvapi.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv.error',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cuda/cudadrv/error.py',
   'PYMODULE'),
  ('numba.cuda.args',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cuda/args.py',
   'PYMODULE'),
  ('numba.core.typing.cffi_utils',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/typing/cffi_utils.py',
   'PYMODULE'),
  ('numba.core.typing.enumdecl',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/typing/enumdecl.py',
   'PYMODULE'),
  ('numba.cuda.models',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cuda/models.py',
   'PYMODULE'),
  ('numba.core.datamodel.registry',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/datamodel/registry.py',
   'PYMODULE'),
  ('numba.core.datamodel.manager',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/datamodel/manager.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv.nvvm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cuda/cudadrv/nvvm.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv.runtime',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cuda/cudadrv/runtime.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv.rtapi',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cuda/cudadrv/rtapi.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cuda/cudadrv/__init__.py',
   'PYMODULE'),
  ('numba.core.typing.cmathdecl',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/typing/cmathdecl.py',
   'PYMODULE'),
  ('numba.cuda.api',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cuda/api.py',
   'PYMODULE'),
  ('numba.cuda.device_init',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cuda/device_init.py',
   'PYMODULE'),
  ('numba.cuda.intrinsic_wrapper',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cuda/intrinsic_wrapper.py',
   'PYMODULE'),
  ('numba.cuda.decorators',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cuda/decorators.py',
   'PYMODULE'),
  ('numba.cuda.simulator.kernel',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cuda/simulator/kernel.py',
   'PYMODULE'),
  ('numba.cuda.simulator',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cuda/simulator/__init__.py',
   'PYMODULE'),
  ('numba.cuda.simulator.compiler',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cuda/simulator/compiler.py',
   'PYMODULE'),
  ('numba.cuda.simulator.cudadrv.runtime',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cuda/simulator/cudadrv/runtime.py',
   'PYMODULE'),
  ('numba.cuda.simulator.cudadrv.devices',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cuda/simulator/cudadrv/devices.py',
   'PYMODULE'),
  ('numba.cuda.simulator.cudadrv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cuda/simulator/cudadrv/__init__.py',
   'PYMODULE'),
  ('numba.cuda.simulator.cudadrv.nvvm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cuda/simulator/cudadrv/nvvm.py',
   'PYMODULE'),
  ('numba.cuda.simulator.cudadrv.error',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cuda/simulator/cudadrv/error.py',
   'PYMODULE'),
  ('numba.cuda.simulator.cudadrv.drvapi',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cuda/simulator/cudadrv/drvapi.py',
   'PYMODULE'),
  ('numba.cuda.simulator.cudadrv.driver',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cuda/simulator/cudadrv/driver.py',
   'PYMODULE'),
  ('numba.cuda.simulator.reduction',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cuda/simulator/reduction.py',
   'PYMODULE'),
  ('numba.cuda.simulator.vector_types',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cuda/simulator/vector_types.py',
   'PYMODULE'),
  ('numba.cuda.simulator.api',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cuda/simulator/api.py',
   'PYMODULE'),
  ('numba.cuda.simulator.kernelapi',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cuda/simulator/kernelapi.py',
   'PYMODULE'),
  ('numba.cuda.simulator.cudadrv.devicearray',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cuda/simulator/cudadrv/devicearray.py',
   'PYMODULE'),
  ('numba.cuda.intrinsics',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cuda/intrinsics.py',
   'PYMODULE'),
  ('numba.cuda.extending',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cuda/extending.py',
   'PYMODULE'),
  ('numba.cuda.stubs',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cuda/stubs.py',
   'PYMODULE'),
  ('numba.cuda.initialize',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cuda/initialize.py',
   'PYMODULE'),
  ('numba.cuda.libdeviceimpl',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cuda/libdeviceimpl.py',
   'PYMODULE'),
  ('numba.cuda.printimpl',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cuda/printimpl.py',
   'PYMODULE'),
  ('numba.cuda.cudaimpl',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cuda/cudaimpl.py',
   'PYMODULE'),
  ('numba.cuda.vector_types',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cuda/vector_types.py',
   'PYMODULE'),
  ('numba.cuda.libdevicedecl',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cuda/libdevicedecl.py',
   'PYMODULE'),
  ('numba.cuda.libdevicefuncs',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cuda/libdevicefuncs.py',
   'PYMODULE'),
  ('numba.cuda.cudamath',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cuda/cudamath.py',
   'PYMODULE'),
  ('numba.cuda.cudadecl',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cuda/cudadecl.py',
   'PYMODULE'),
  ('numba.cuda.ufuncs',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cuda/ufuncs.py',
   'PYMODULE'),
  ('numba.cuda.libdevice',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cuda/libdevice.py',
   'PYMODULE'),
  ('numba.cuda.codegen',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cuda/codegen.py',
   'PYMODULE'),
  ('numba.cuda.cg',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cuda/cg.py',
   'PYMODULE'),
  ('numba.cuda.nvvmutils',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cuda/nvvmutils.py',
   'PYMODULE'),
  ('numba.cuda.simulator_init',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cuda/simulator_init.py',
   'PYMODULE'),
  ('numba.runtests',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/runtests.py',
   'PYMODULE'),
  ('numba.testing._runtests',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/testing/_runtests.py',
   'PYMODULE'),
  ('numba.tests.support',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/tests/support.py',
   'PYMODULE'),
  ('numba.tests',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/tests/__init__.py',
   'PYMODULE'),
  ('numba.pycc.platform',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/pycc/platform.py',
   'PYMODULE'),
  ('numba.pycc',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/pycc/__init__.py',
   'PYMODULE'),
  ('numba.pycc.decorators',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/pycc/decorators.py',
   'PYMODULE'),
  ('numba.pycc.compiler',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/pycc/compiler.py',
   'PYMODULE'),
  ('numba.pycc.cc',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/pycc/cc.py',
   'PYMODULE'),
  ('numba.cext',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cext/__init__.py',
   'PYMODULE'),
  ('numba.pycc.llvm_types',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/pycc/llvm_types.py',
   'PYMODULE'),
  ('numba.core.untyped_passes',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/untyped_passes.py',
   'PYMODULE'),
  ('numba.core.rvsdg_frontend.bcinterp',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/rvsdg_frontend/bcinterp.py',
   'PYMODULE'),
  ('numba.core.rvsdg_frontend.rvsdg.regionpasses',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/rvsdg_frontend/rvsdg/regionpasses.py',
   'PYMODULE'),
  ('numba.core.rvsdg_frontend.rvsdg',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/rvsdg_frontend/rvsdg/__init__.py',
   'PYMODULE'),
  ('numba.core.rvsdg_frontend.rvsdg.bc2rvsdg',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/rvsdg_frontend/rvsdg/bc2rvsdg.py',
   'PYMODULE'),
  ('numba.core.rvsdg_frontend.rvsdg.regionrenderer',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/rvsdg_frontend/rvsdg/regionrenderer.py',
   'PYMODULE'),
  ('numba.core.rvsdg_frontend',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/rvsdg_frontend/__init__.py',
   'PYMODULE'),
  ('numba.core.ssa',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/ssa.py',
   'PYMODULE'),
  ('numba.testing',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/testing/__init__.py',
   'PYMODULE'),
  ('numba.testing.main',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/testing/main.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   '/Users/<USER>/miniconda3/lib/python3.12/xml/etree/ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   '/Users/<USER>/miniconda3/lib/python3.12/xml/etree/cElementTree.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   '/Users/<USER>/miniconda3/lib/python3.12/xml/etree/ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   '/Users/<USER>/miniconda3/lib/python3.12/xml/etree/ElementPath.py',
   'PYMODULE'),
  ('xml.etree',
   '/Users/<USER>/miniconda3/lib/python3.12/xml/etree/__init__.py',
   'PYMODULE'),
  ('numba.cuda.testing',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cuda/testing.py',
   'PYMODULE'),
  ('numba.testing.loader',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/testing/loader.py',
   'PYMODULE'),
  ('cProfile',
   '/Users/<USER>/miniconda3/lib/python3.12/cProfile.py',
   'PYMODULE'),
  ('optparse',
   '/Users/<USER>/miniconda3/lib/python3.12/optparse.py',
   'PYMODULE'),
  ('pstats', '/Users/<USER>/miniconda3/lib/python3.12/pstats.py', 'PYMODULE'),
  ('profile',
   '/Users/<USER>/miniconda3/lib/python3.12/profile.py',
   'PYMODULE'),
  ('numba.core.typeconv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/typeconv/__init__.py',
   'PYMODULE'),
  ('numba.core.typeconv.rules',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/typeconv/rules.py',
   'PYMODULE'),
  ('numba.core.typeconv.typeconv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/typeconv/typeconv.py',
   'PYMODULE'),
  ('numba.core.typeconv.castgraph',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/typeconv/castgraph.py',
   'PYMODULE'),
  ('numba.core.types.misc',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/types/misc.py',
   'PYMODULE'),
  ('numba.core.types.common',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/types/common.py',
   'PYMODULE'),
  ('numba.core.types.iterators',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/types/iterators.py',
   'PYMODULE'),
  ('numba.core.types.abstract',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/types/abstract.py',
   'PYMODULE'),
  ('numba.core.controlflow',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/controlflow.py',
   'PYMODULE'),
  ('numba.stencils.stencilparfor',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/stencils/stencilparfor.py',
   'PYMODULE'),
  ('numba.parfors',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/parfors/__init__.py',
   'PYMODULE'),
  ('numba.cpython.numbers',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cpython/numbers.py',
   'PYMODULE'),
  ('numba.cpython.iterators',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cpython/iterators.py',
   'PYMODULE'),
  ('numba.cpython.heapq',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cpython/heapq.py',
   'PYMODULE'),
  ('numba.cpython.enumimpl',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cpython/enumimpl.py',
   'PYMODULE'),
  ('numba.cpython.charseq',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cpython/charseq.py',
   'PYMODULE'),
  ('numba.cpython.unicode',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cpython/unicode.py',
   'PYMODULE'),
  ('numba.cpython.unicode_support',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cpython/unicode_support.py',
   'PYMODULE'),
  ('numba.core.unsafe.bytes',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/unsafe/bytes.py',
   'PYMODULE'),
  ('numba.core.unsafe',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/unsafe/__init__.py',
   'PYMODULE'),
  ('numba.core.unsafe.eh',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/unsafe/eh.py',
   'PYMODULE'),
  ('numba.cpython.hashing',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cpython/hashing.py',
   'PYMODULE'),
  ('numba.cpython.randomimpl',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cpython/randomimpl.py',
   'PYMODULE'),
  ('numba.cpython.builtins',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cpython/builtins.py',
   'PYMODULE'),
  ('numba.core.typing.builtins',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/typing/builtins.py',
   'PYMODULE'),
  ('numba.core.datamodel.models',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/datamodel/models.py',
   'PYMODULE'),
  ('numba.core.datamodel',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/datamodel/__init__.py',
   'PYMODULE'),
  ('numba.core.datamodel.packer',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/datamodel/packer.py',
   'PYMODULE'),
  ('numba.core.typing.templates',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/typing/templates.py',
   'PYMODULE'),
  ('numba.core.cpu_options',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/cpu_options.py',
   'PYMODULE'),
  ('numba.core.typing.asnumbatype',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/typing/asnumbatype.py',
   'PYMODULE'),
  ('numba.core.typing.typeof',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/typing/typeof.py',
   'PYMODULE'),
  ('numba.typed',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/typed/__init__.py',
   'PYMODULE'),
  ('numba.typed.typedlist',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/typed/typedlist.py',
   'PYMODULE'),
  ('numba.typed.listobject',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/typed/listobject.py',
   'PYMODULE'),
  ('numba.typed.typedobjectutils',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/typed/typedobjectutils.py',
   'PYMODULE'),
  ('numba.typed.dictimpl',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/typed/dictimpl.py',
   'PYMODULE'),
  ('numba.typed.typeddict',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/typed/typeddict.py',
   'PYMODULE'),
  ('numba.typed.dictobject',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/typed/dictobject.py',
   'PYMODULE'),
  ('numba.core.typing.bufproto',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/typing/bufproto.py',
   'PYMODULE'),
  ('numba.np.ufunc.ufuncbuilder',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/np/ufunc/ufuncbuilder.py',
   'PYMODULE'),
  ('numba.np.ufunc.wrappers',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/np/ufunc/wrappers.py',
   'PYMODULE'),
  ('numba.core.typing',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/typing/__init__.py',
   'PYMODULE'),
  ('numba.core.typing.context',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/typing/context.py',
   'PYMODULE'),
  ('numba.core.typing.dictdecl',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/typing/dictdecl.py',
   'PYMODULE'),
  ('numba.core.typing.setdecl',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/typing/setdecl.py',
   'PYMODULE'),
  ('numba.core.typing.mathdecl',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/typing/mathdecl.py',
   'PYMODULE'),
  ('numba.core.typing.listdecl',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/typing/listdecl.py',
   'PYMODULE'),
  ('numba.core.typing.npdatetime',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/typing/npdatetime.py',
   'PYMODULE'),
  ('numba.core.typing.arraydecl',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/typing/arraydecl.py',
   'PYMODULE'),
  ('numba.core.typing.collections',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/typing/collections.py',
   'PYMODULE'),
  ('numba.np.numpy_support',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/np/numpy_support.py',
   'PYMODULE'),
  ('llvmlite.ir',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/llvmlite/ir/__init__.py',
   'PYMODULE'),
  ('llvmlite.ir.transforms',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/llvmlite/ir/transforms.py',
   'PYMODULE'),
  ('llvmlite.ir.builder',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/llvmlite/ir/builder.py',
   'PYMODULE'),
  ('llvmlite.ir.instructions',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/llvmlite/ir/instructions.py',
   'PYMODULE'),
  ('llvmlite.ir._utils',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/llvmlite/ir/_utils.py',
   'PYMODULE'),
  ('llvmlite.ir.module',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/llvmlite/ir/module.py',
   'PYMODULE'),
  ('llvmlite.ir.context',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/llvmlite/ir/context.py',
   'PYMODULE'),
  ('llvmlite.ir.values',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/llvmlite/ir/values.py',
   'PYMODULE'),
  ('llvmlite.ir.types',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/llvmlite/ir/types.py',
   'PYMODULE'),
  ('llvmlite',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/llvmlite/__init__.py',
   'PYMODULE'),
  ('llvmlite._version',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/llvmlite/_version.py',
   'PYMODULE'),
  ('numba.np.ufunc',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/np/ufunc/__init__.py',
   'PYMODULE'),
  ('numba.cuda.vectorizers',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cuda/vectorizers.py',
   'PYMODULE'),
  ('numba.np.ufunc.deviceufunc',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/np/ufunc/deviceufunc.py',
   'PYMODULE'),
  ('numba.np.ufunc.array_exprs',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/np/ufunc/array_exprs.py',
   'PYMODULE'),
  ('numba.np.ufunc.decorators',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/np/ufunc/decorators.py',
   'PYMODULE'),
  ('numba.np.ufunc.gufunc',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/np/ufunc/gufunc.py',
   'PYMODULE'),
  ('numba.np.ufunc.ufunc_base',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/np/ufunc/ufunc_base.py',
   'PYMODULE'),
  ('numba.np.ufunc.dufunc',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/np/ufunc/dufunc.py',
   'PYMODULE'),
  ('llvmlite.binding',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/llvmlite/binding/__init__.py',
   'PYMODULE'),
  ('llvmlite.binding.orcjit',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/llvmlite/binding/orcjit.py',
   'PYMODULE'),
  ('llvmlite.binding.context',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/llvmlite/binding/context.py',
   'PYMODULE'),
  ('llvmlite.binding.analysis',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/llvmlite/binding/analysis.py',
   'PYMODULE'),
  ('llvmlite.binding.typeref',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/llvmlite/binding/typeref.py',
   'PYMODULE'),
  ('llvmlite.binding.value',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/llvmlite/binding/value.py',
   'PYMODULE'),
  ('llvmlite.binding.common',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/llvmlite/binding/common.py',
   'PYMODULE'),
  ('llvmlite.binding.transforms',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/llvmlite/binding/transforms.py',
   'PYMODULE'),
  ('llvmlite.binding.passmanagers',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/llvmlite/binding/passmanagers.py',
   'PYMODULE'),
  ('llvmlite.binding.options',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/llvmlite/binding/options.py',
   'PYMODULE'),
  ('llvmlite.binding.module',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/llvmlite/binding/module.py',
   'PYMODULE'),
  ('llvmlite.binding.linker',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/llvmlite/binding/linker.py',
   'PYMODULE'),
  ('llvmlite.binding.initfini',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/llvmlite/binding/initfini.py',
   'PYMODULE'),
  ('llvmlite.binding.executionengine',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/llvmlite/binding/executionengine.py',
   'PYMODULE'),
  ('llvmlite.binding.object_file',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/llvmlite/binding/object_file.py',
   'PYMODULE'),
  ('llvmlite.binding.targets',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/llvmlite/binding/targets.py',
   'PYMODULE'),
  ('llvmlite.binding.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/llvmlite/binding/dylib.py',
   'PYMODULE'),
  ('llvmlite.binding.ffi',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/llvmlite/binding/ffi.py',
   'PYMODULE'),
  ('llvmlite.utils',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/llvmlite/utils.py',
   'PYMODULE'),
  ('yaml',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/yaml/__init__.py',
   'PYMODULE'),
  ('yaml.cyaml',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/yaml/cyaml.py',
   'PYMODULE'),
  ('yaml.resolver',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/yaml/resolver.py',
   'PYMODULE'),
  ('yaml.representer',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/yaml/representer.py',
   'PYMODULE'),
  ('yaml.serializer',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/yaml/serializer.py',
   'PYMODULE'),
  ('yaml.constructor',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/yaml/constructor.py',
   'PYMODULE'),
  ('yaml.dumper',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/yaml/dumper.py',
   'PYMODULE'),
  ('yaml.emitter',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/yaml/emitter.py',
   'PYMODULE'),
  ('yaml.loader',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/yaml/loader.py',
   'PYMODULE'),
  ('yaml.composer',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/yaml/composer.py',
   'PYMODULE'),
  ('yaml.parser',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/yaml/parser.py',
   'PYMODULE'),
  ('yaml.scanner',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/yaml/scanner.py',
   'PYMODULE'),
  ('yaml.reader',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/yaml/reader.py',
   'PYMODULE'),
  ('yaml.nodes',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/yaml/nodes.py',
   'PYMODULE'),
  ('yaml.events',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/yaml/events.py',
   'PYMODULE'),
  ('yaml.tokens',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/yaml/tokens.py',
   'PYMODULE'),
  ('yaml.error',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/yaml/error.py',
   'PYMODULE'),
  ('numba.core.lowering',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/lowering.py',
   'PYMODULE'),
  ('numba.experimental.function_type',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/experimental/function_type.py',
   'PYMODULE'),
  ('numba.experimental',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/experimental/__init__.py',
   'PYMODULE'),
  ('numba.experimental.jitclass',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/experimental/jitclass/__init__.py',
   'PYMODULE'),
  ('numba.experimental.jitclass.overloads',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/experimental/jitclass/overloads.py',
   'PYMODULE'),
  ('numba.experimental.jitclass.boxing',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/experimental/jitclass/boxing.py',
   'PYMODULE'),
  ('numba.experimental.jitclass.decorators',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/experimental/jitclass/decorators.py',
   'PYMODULE'),
  ('numba.experimental.jitclass.base',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/experimental/jitclass/base.py',
   'PYMODULE'),
  ('numba.misc.firstlinefinder',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/misc/firstlinefinder.py',
   'PYMODULE'),
  ('numba.core.environment',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/environment.py',
   'PYMODULE'),
  ('numba.core.removerefctpass',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/removerefctpass.py',
   'PYMODULE'),
  ('numba.core.registry',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/registry.py',
   'PYMODULE'),
  ('numba.core.dispatcher',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/dispatcher.py',
   'PYMODULE'),
  ('numba.core.annotations.pretty_annotate',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/annotations/pretty_annotate.py',
   'PYMODULE'),
  ('numba.core.entrypoints',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/entrypoints.py',
   'PYMODULE'),
  ('numba.core.compiler',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/compiler.py',
   'PYMODULE'),
  ('numba.core.object_mode_passes',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/object_mode_passes.py',
   'PYMODULE'),
  ('numba.core.pylowering',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/pylowering.py',
   'PYMODULE'),
  ('numba.core.cpu',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/cpu.py',
   'PYMODULE'),
  ('numba.np.polynomial.polynomial_functions',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/np/polynomial/polynomial_functions.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/polynomial/polyutils.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/polynomial/polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/polynomial/_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/polynomial/__init__.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/polynomial/laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/polynomial/hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/polynomial/hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/polynomial/legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/polynomial/chebyshev.py',
   'PYMODULE'),
  ('numba.np.polynomial.polynomial_core',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/np/polynomial/polynomial_core.py',
   'PYMODULE'),
  ('numba.np.polynomial',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/np/polynomial/__init__.py',
   'PYMODULE'),
  ('numba.np.random.generator_methods',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/np/random/generator_methods.py',
   'PYMODULE'),
  ('numba.np.random.random_methods',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/np/random/random_methods.py',
   'PYMODULE'),
  ('numba.np.random._constants',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/np/random/_constants.py',
   'PYMODULE'),
  ('numba.np.random.distributions',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/np/random/distributions.py',
   'PYMODULE'),
  ('numba.core.types.containers',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/types/containers.py',
   'PYMODULE'),
  ('numba.np.random.generator_core',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/np/random/generator_core.py',
   'PYMODULE'),
  ('numba.np.random',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/np/random/__init__.py',
   'PYMODULE'),
  ('numba.core.callwrapper',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/callwrapper.py',
   'PYMODULE'),
  ('numba.core.inline_closurecall',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/inline_closurecall.py',
   'PYMODULE'),
  ('numba.core.transforms',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/transforms.py',
   'PYMODULE'),
  ('numba.core.optional',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/optional.py',
   'PYMODULE'),
  ('numba.core.fastmathpass',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/fastmathpass.py',
   'PYMODULE'),
  ('numba.core.externals',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/externals.py',
   'PYMODULE'),
  ('numba.core.intrinsics',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/intrinsics.py',
   'PYMODULE'),
  ('numba.core.codegen',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/codegen.py',
   'PYMODULE'),
  ('numba.misc.dump_style',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/misc/dump_style.py',
   'PYMODULE'),
  ('numba.misc.llvm_pass_timings',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/misc/llvm_pass_timings.py',
   'PYMODULE'),
  ('numba.misc.inspection',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/misc/inspection.py',
   'PYMODULE'),
  ('numba.core.runtime.nrtopt',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/runtime/nrtopt.py',
   'PYMODULE'),
  ('numba.core.llvm_bindings',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/llvm_bindings.py',
   'PYMODULE'),
  ('numba.core.sigutils',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/sigutils.py',
   'PYMODULE'),
  ('numba.core.callconv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/callconv.py',
   'PYMODULE'),
  ('numba.core.bytecode',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/bytecode.py',
   'PYMODULE'),
  ('numba.core.interpreter',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/interpreter.py',
   'PYMODULE'),
  ('numba.core.byteflow',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/byteflow.py',
   'PYMODULE'),
  ('numba.core.serialize',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/serialize.py',
   'PYMODULE'),
  ('numba.cloudpickle',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cloudpickle/__init__.py',
   'PYMODULE'),
  ('numba.cloudpickle.cloudpickle',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cloudpickle/cloudpickle.py',
   'PYMODULE'),
  ('numba.core.ir_utils',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/ir_utils.py',
   'PYMODULE'),
  ('numba.core.typeinfer',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/typeinfer.py',
   'PYMODULE'),
  ('numba.core.rewrites',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/rewrites/__init__.py',
   'PYMODULE'),
  ('numba.core.rewrites.ir_print',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/rewrites/ir_print.py',
   'PYMODULE'),
  ('numba.core.rewrites.static_binop',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/rewrites/static_binop.py',
   'PYMODULE'),
  ('numba.core.rewrites.static_raise',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/rewrites/static_raise.py',
   'PYMODULE'),
  ('numba.core.rewrites.static_getitem',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/rewrites/static_getitem.py',
   'PYMODULE'),
  ('numba.core.rewrites.registry',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/rewrites/registry.py',
   'PYMODULE'),
  ('numba.core.postproc',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/postproc.py',
   'PYMODULE'),
  ('numba.core.analysis',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/analysis.py',
   'PYMODULE'),
  ('numba.core.generators',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/generators.py',
   'PYMODULE'),
  ('numba.core.event',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/event.py',
   'PYMODULE'),
  ('numba.core.imputils',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/imputils.py',
   'PYMODULE'),
  ('numba.core.debuginfo',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/debuginfo.py',
   'PYMODULE'),
  ('numba.core.cgutils',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/cgutils.py',
   'PYMODULE'),
  ('numba.core.funcdesc',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/funcdesc.py',
   'PYMODULE'),
  ('numba.core.itanium_mangler',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/itanium_mangler.py',
   'PYMODULE'),
  ('numba.core.consts',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/consts.py',
   'PYMODULE'),
  ('numba.core.ir',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/ir.py',
   'PYMODULE'),
  ('numba.core.targetconfig',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/targetconfig.py',
   'PYMODULE'),
  ('numba.core.errors',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/errors.py',
   'PYMODULE'),
  ('numba.core.types',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/types/__init__.py',
   'PYMODULE'),
  ('numba.core.types.function_type',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/types/function_type.py',
   'PYMODULE'),
  ('numba.core.types.scalars',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/types/scalars.py',
   'PYMODULE'),
  ('numba.core.types.npytypes',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/types/npytypes.py',
   'PYMODULE'),
  ('numba.core.utils',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/utils.py',
   'PYMODULE'),
  ('numba',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/__init__.py',
   'PYMODULE'),
  ('numba.cloudpickle.cloudpickle_fast',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cloudpickle/cloudpickle_fast.py',
   'PYMODULE'),
  ('numba.core.withcontexts',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/withcontexts.py',
   'PYMODULE'),
  ('numba.misc.init_utils',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/misc/init_utils.py',
   'PYMODULE'),
  ('numba._version',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/_version.py',
   'PYMODULE'),
  ('eventkit',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/eventkit/__init__.py',
   'PYMODULE'),
  ('eventkit.version',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/eventkit/version.py',
   'PYMODULE'),
  ('eventkit.ops.transform',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/eventkit/ops/transform.py',
   'PYMODULE'),
  ('eventkit.ops',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/eventkit/ops/__init__.py',
   'PYMODULE'),
  ('eventkit.util',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/eventkit/util.py',
   'PYMODULE'),
  ('eventkit.ops.timing',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/eventkit/ops/timing.py',
   'PYMODULE'),
  ('eventkit.ops.select',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/eventkit/ops/select.py',
   'PYMODULE'),
  ('eventkit.ops.op',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/eventkit/ops/op.py',
   'PYMODULE'),
  ('eventkit.ops.misc',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/eventkit/ops/misc.py',
   'PYMODULE'),
  ('eventkit.ops.create',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/eventkit/ops/create.py',
   'PYMODULE'),
  ('eventkit.ops.combine',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/eventkit/ops/combine.py',
   'PYMODULE'),
  ('eventkit.ops.array',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/eventkit/ops/array.py',
   'PYMODULE'),
  ('eventkit.ops.aggregate',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/eventkit/ops/aggregate.py',
   'PYMODULE'),
  ('eventkit.event',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/eventkit/event.py',
   'PYMODULE'),
  ('pyqtgraph.widgets',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/widgets/__init__.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.VerticalLabel',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/widgets/VerticalLabel.py',
   'PYMODULE'),
  ('pyqtgraph.Qt.QtWidgets',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/Qt/QtWidgets/__init__.py',
   'PYMODULE'),
  ('pyqtgraph.Qt.QtGui',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/Qt/QtGui/__init__.py',
   'PYMODULE'),
  ('pyqtgraph.Qt.QtCore',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/Qt/QtCore/__init__.py',
   'PYMODULE'),
  ('pyqtgraph.Qt',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/Qt/__init__.py',
   'PYMODULE'),
  ('pyqtgraph.Qt.internals',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/Qt/internals.py',
   'PYMODULE'),
  ('PyQt6.uic',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/uic/__init__.py',
   'PYMODULE'),
  ('PyQt6.uic.ui_file',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/uic/ui_file.py',
   'PYMODULE'),
  ('PyQt6.uic.exceptions',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/uic/exceptions.py',
   'PYMODULE'),
  ('PyQt6.uic.objcreator',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/uic/objcreator.py',
   'PYMODULE'),
  ('PyQt6.uic.load_ui',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/uic/load_ui.py',
   'PYMODULE'),
  ('PyQt6.uic.Loader.loader',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/uic/Loader/loader.py',
   'PYMODULE'),
  ('PyQt6.uic.Loader',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/uic/Loader/__init__.py',
   'PYMODULE'),
  ('PyQt6.uic.Loader.qobjectcreator',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/uic/Loader/qobjectcreator.py',
   'PYMODULE'),
  ('PyQt6.uic.uiparser',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/uic/uiparser.py',
   'PYMODULE'),
  ('PyQt6.uic.properties',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/uic/properties.py',
   'PYMODULE'),
  ('PyQt6.uic.icon_cache',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/uic/icon_cache.py',
   'PYMODULE'),
  ('PyQt6.uic.enum_map',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/uic/enum_map.py',
   'PYMODULE'),
  ('PyQt6.uic.Compiler.compiler',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/uic/Compiler/compiler.py',
   'PYMODULE'),
  ('PyQt6.uic.Compiler.qobjectcreator',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/uic/Compiler/qobjectcreator.py',
   'PYMODULE'),
  ('PyQt6.uic.Compiler.as_string',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/uic/Compiler/as_string.py',
   'PYMODULE'),
  ('PyQt6.uic.Compiler.indenter',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/uic/Compiler/indenter.py',
   'PYMODULE'),
  ('PyQt6.uic.Compiler.qtproxies',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/uic/Compiler/qtproxies.py',
   'PYMODULE'),
  ('PyQt6.uic.Compiler.proxy_metaclass',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/uic/Compiler/proxy_metaclass.py',
   'PYMODULE'),
  ('PyQt6.uic.Compiler.misc',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/uic/Compiler/misc.py',
   'PYMODULE'),
  ('PyQt6.uic.Compiler',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/uic/Compiler/__init__.py',
   'PYMODULE'),
  ('PyQt6.uic.compile_ui',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/uic/compile_ui.py',
   'PYMODULE'),
  ('pyqtgraph.Qt.compat',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/Qt/compat/__init__.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.MatplotlibWidget',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/widgets/MatplotlibWidget.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/graphicsItems/__init__.py',
   'PYMODULE'),
  ('numpy.random',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/random/__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/random/_pickle.py',
   'PYMODULE'),
  ('numpy.fft',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/fft/__init__.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/fft/helper.py',
   'PYMODULE'),
  ('numpy.fft._helper',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/fft/_helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/fft/_pocketfft.py',
   'PYMODULE'),
  ('tracemalloc',
   '/Users/<USER>/miniconda3/lib/python3.12/tracemalloc.py',
   'PYMODULE'),
  ('_py_abc',
   '/Users/<USER>/miniconda3/lib/python3.12/_py_abc.py',
   'PYMODULE'),
  ('stringprep',
   '/Users/<USER>/miniconda3/lib/python3.12/stringprep.py',
   'PYMODULE'),
  ('_strptime',
   '/Users/<USER>/miniconda3/lib/python3.12/_strptime.py',
   'PYMODULE'),
  ('threading',
   '/Users/<USER>/miniconda3/lib/python3.12/threading.py',
   'PYMODULE'),
  ('_threading_local',
   '/Users/<USER>/miniconda3/lib/python3.12/_threading_local.py',
   'PYMODULE'),
  ('random', '/Users/<USER>/miniconda3/lib/python3.12/random.py', 'PYMODULE'),
  ('statistics',
   '/Users/<USER>/miniconda3/lib/python3.12/statistics.py',
   'PYMODULE'),
  ('fractions',
   '/Users/<USER>/miniconda3/lib/python3.12/fractions.py',
   'PYMODULE'),
  ('io_', '/Users/<USER>/Local/zip/io_.py', 'PYMODULE'),
  ('hifiscan',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/hifiscan/__init__.py',
   'PYMODULE'),
  ('sounddevice',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/sounddevice.py',
   'PYMODULE'),
  ('_sounddevice_data',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/_sounddevice_data/__init__.py',
   'PYMODULE'),
  ('_sounddevice',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/_sounddevice.py',
   'PYMODULE'),
  ('pyqtgraph',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/__init__.py',
   'PYMODULE'),
  ('pyqtgraph.multiprocess.bootstrap',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/multiprocess/bootstrap.py',
   'PYMODULE'),
  ('pyqtgraph.imageview.ImageViewTemplate_generic',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/imageview/ImageViewTemplate_generic.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.ViewBox.axisCtrlTemplate_generic',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/graphicsItems/ViewBox/axisCtrlTemplate_generic.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.PlotItem.plotConfigTemplate_generic',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/graphicsItems/PlotItem/plotConfigTemplate_generic.py',
   'PYMODULE'),
  ('pyqtgraph.flowchart.FlowchartCtrlTemplate_generic',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/flowchart/FlowchartCtrlTemplate_generic.py',
   'PYMODULE'),
  ('pyqtgraph.flowchart',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/flowchart/__init__.py',
   'PYMODULE'),
  ('pyqtgraph.flowchart.library',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/flowchart/library/__init__.py',
   'PYMODULE'),
  ('pyqtgraph.flowchart.library.Operators',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/flowchart/library/Operators.py',
   'PYMODULE'),
  ('pyqtgraph.flowchart.library.common',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/flowchart/library/common.py',
   'PYMODULE'),
  ('pyqtgraph.flowchart.Node',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/flowchart/Node.py',
   'PYMODULE'),
  ('pyqtgraph.flowchart.Terminal',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/flowchart/Terminal.py',
   'PYMODULE'),
  ('pyqtgraph.flowchart.library.Filters',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/flowchart/library/Filters.py',
   'PYMODULE'),
  ('pyqtgraph.flowchart.library.Display',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/flowchart/library/Display.py',
   'PYMODULE'),
  ('pyqtgraph.flowchart.library.Data',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/flowchart/library/Data.py',
   'PYMODULE'),
  ('pyqtgraph.flowchart.library.functions',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/flowchart/library/functions.py',
   'PYMODULE'),
  ('pyqtgraph.flowchart.NodeLibrary',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/flowchart/NodeLibrary.py',
   'PYMODULE'),
  ('pyqtgraph.flowchart.Flowchart',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/flowchart/Flowchart.py',
   'PYMODULE'),
  ('pyqtgraph.flowchart.FlowchartGraphicsView',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/flowchart/FlowchartGraphicsView.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.ViewBox.ViewBox',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/graphicsItems/ViewBox/ViewBox.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.ViewBox.ViewBoxMenu',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/graphicsItems/ViewBox/ViewBoxMenu.py',
   'PYMODULE'),
  ('pyqtgraph.dockarea',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/dockarea/__init__.py',
   'PYMODULE'),
  ('pyqtgraph.dockarea.DockArea',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/dockarea/DockArea.py',
   'PYMODULE'),
  ('pyqtgraph.dockarea.DockDrop',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/dockarea/DockDrop.py',
   'PYMODULE'),
  ('pyqtgraph.dockarea.Container',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/dockarea/Container.py',
   'PYMODULE'),
  ('pyqtgraph.dockarea.Dock',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/dockarea/Dock.py',
   'PYMODULE'),
  ('pyqtgraph.configfile',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/configfile.py',
   'PYMODULE'),
  ('pyqtgraph.units',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/units.py',
   'PYMODULE'),
  ('pyqtgraph.canvas.TransformGuiTemplate_generic',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/canvas/TransformGuiTemplate_generic.py',
   'PYMODULE'),
  ('pyqtgraph.canvas',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/canvas/__init__.py',
   'PYMODULE'),
  ('pyqtgraph.canvas.CanvasItem',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/canvas/CanvasItem.py',
   'PYMODULE'),
  ('pyqtgraph.canvas.Canvas',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/canvas/Canvas.py',
   'PYMODULE'),
  ('pyqtgraph.canvas.CanvasManager',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/canvas/CanvasManager.py',
   'PYMODULE'),
  ('pyqtgraph.canvas.CanvasTemplate_generic',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/canvas/CanvasTemplate_generic.py',
   'PYMODULE'),
  ('pyqtgraph.GraphicsScene.exportDialogTemplate_generic',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/GraphicsScene/exportDialogTemplate_generic.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.ParameterTree',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/parametertree/ParameterTree.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.ParameterItem',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/parametertree/ParameterItem.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/parametertree/parameterTypes/__init__.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.text',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/parametertree/parameterTypes/text.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.str',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/parametertree/parameterTypes/str.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.slider',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/parametertree/parameterTypes/slider.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.qtenum',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/parametertree/parameterTypes/qtenum.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.progress',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/parametertree/parameterTypes/progress.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.pen',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/parametertree/parameterTypes/pen.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.PenPreviewLabel',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/widgets/PenPreviewLabel.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.numeric',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/parametertree/parameterTypes/numeric.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.list',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/parametertree/parameterTypes/list.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.font',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/parametertree/parameterTypes/font.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.file',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/parametertree/parameterTypes/file.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.colormaplut',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/parametertree/parameterTypes/colormaplut.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.ColorMapButton',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/widgets/ColorMapButton.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.colormap',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/parametertree/parameterTypes/colormap.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.color',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/parametertree/parameterTypes/color.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.checklist',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/parametertree/parameterTypes/checklist.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.calendar',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/parametertree/parameterTypes/calendar.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.bool',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/parametertree/parameterTypes/bool.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.basetypes',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/parametertree/parameterTypes/basetypes.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.actiongroup',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/parametertree/parameterTypes/actiongroup.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.action',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/parametertree/parameterTypes/action.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.Parameter',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/parametertree/Parameter.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/parametertree/__init__.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.interactive',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/parametertree/interactive.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.ParameterSystem',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/parametertree/ParameterSystem.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.SystemSolver',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/parametertree/SystemSolver.py',
   'PYMODULE'),
  ('pyqtgraph.console',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/console/__init__.py',
   'PYMODULE'),
  ('pyqtgraph.console.Console',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/console/Console.py',
   'PYMODULE'),
  ('pyqtgraph.console.exception_widget',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/console/exception_widget.py',
   'PYMODULE'),
  ('pyqtgraph.console.stackwidget',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/console/stackwidget.py',
   'PYMODULE'),
  ('pyqtgraph.console.repl_widget',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/console/repl_widget.py',
   'PYMODULE'),
  ('pyqtgraph.console.CmdInput',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/console/CmdInput.py',
   'PYMODULE'),
  ('pyqtgraph.exceptionHandling',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/exceptionHandling.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.ValueLabel',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/widgets/ValueLabel.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.TreeWidget',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/widgets/TreeWidget.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.TableWidget',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/widgets/TableWidget.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.SpinBox',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/widgets/SpinBox.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.ScatterPlotWidget',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/widgets/ScatterPlotWidget.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.RemoteGraphicsView',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/widgets/RemoteGraphicsView.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.RawImageWidget',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/widgets/RawImageWidget.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.ProgressDialog',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/widgets/ProgressDialog.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.PlotWidget',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/widgets/PlotWidget.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.PlotItem.PlotItem',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/graphicsItems/PlotItem/PlotItem.py',
   'PYMODULE'),
  ('pyqtgraph.exporters.ImageExporter',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/exporters/ImageExporter.py',
   'PYMODULE'),
  ('pyqtgraph.exporters.Exporter',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/exporters/Exporter.py',
   'PYMODULE'),
  ('pyqtgraph.exporters.SVGExporter',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/exporters/SVGExporter.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   '/Users/<USER>/miniconda3/lib/python3.12/xml/dom/minidom.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   '/Users/<USER>/miniconda3/lib/python3.12/xml/dom/pulldom.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   '/Users/<USER>/miniconda3/lib/python3.12/xml/dom/expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   '/Users/<USER>/miniconda3/lib/python3.12/xml/dom/NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   '/Users/<USER>/miniconda3/lib/python3.12/xml/dom/xmlbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   '/Users/<USER>/miniconda3/lib/python3.12/xml/dom/minicompat.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   '/Users/<USER>/miniconda3/lib/python3.12/xml/dom/domreg.py',
   'PYMODULE'),
  ('xml.dom',
   '/Users/<USER>/miniconda3/lib/python3.12/xml/dom/__init__.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.PathButton',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/widgets/PathButton.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.MultiPlotWidget',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/widgets/MultiPlotWidget.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.LayoutWidget',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/widgets/LayoutWidget.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.JoystickButton',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/widgets/JoystickButton.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.HistogramLUTWidget',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/widgets/HistogramLUTWidget.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.GroupBox',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/widgets/GroupBox.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.GraphicsView',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/widgets/GraphicsView.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.GraphicsLayoutWidget',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/widgets/GraphicsLayoutWidget.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.GradientWidget',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/widgets/GradientWidget.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.FileDialog',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/widgets/FileDialog.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.FeedbackButton',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/widgets/FeedbackButton.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.DiffTreeWidget',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/widgets/DiffTreeWidget.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.DataTreeWidget',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/widgets/DataTreeWidget.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.DataFilterWidget',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/widgets/DataFilterWidget.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.ComboBox',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/widgets/ComboBox.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.ColorMapWidget',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/widgets/ColorMapWidget.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.ColorMapMenu',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/widgets/ColorMapMenu.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.GradientPresets',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/graphicsItems/GradientPresets.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.ColorButton',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/widgets/ColorButton.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.CheckTable',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/widgets/CheckTable.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.BusyCursor',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/widgets/BusyCursor.py',
   'PYMODULE'),
  ('pyqtgraph.WidgetGroup',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/WidgetGroup.py',
   'PYMODULE'),
  ('pyqtgraph.Vector',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/Vector.py',
   'PYMODULE'),
  ('pyqtgraph.util.cupy_helper',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/util/cupy_helper.py',
   'PYMODULE'),
  ('pyqtgraph.util',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/util/__init__.py',
   'PYMODULE'),
  ('pyqtgraph.util.cprint',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/util/cprint.py',
   'PYMODULE'),
  ('pyqtgraph.util.colorama.winterm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/util/colorama/winterm.py',
   'PYMODULE'),
  ('pyqtgraph.util.colorama',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/util/colorama/__init__.py',
   'PYMODULE'),
  ('pyqtgraph.util.colorama.win32',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/util/colorama/win32.py',
   'PYMODULE'),
  ('pyqtgraph.Transform3D',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/Transform3D.py',
   'PYMODULE'),
  ('pyqtgraph.ThreadsafeTimer',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/ThreadsafeTimer.py',
   'PYMODULE'),
  ('pyqtgraph.SignalProxy',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/SignalProxy.py',
   'PYMODULE'),
  ('pyqtgraph.Point',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/Point.py',
   'PYMODULE'),
  ('pyqtgraph.metaarray.MetaArray',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/metaarray/MetaArray.py',
   'PYMODULE'),
  ('pyqtgraph.metaarray',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/metaarray/__init__.py',
   'PYMODULE'),
  ('pyqtgraph.imageview',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/imageview/__init__.py',
   'PYMODULE'),
  ('pyqtgraph.imageview.ImageView',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/imageview/ImageView.py',
   'PYMODULE'),
  ('pyqtgraph.GraphicsScene.GraphicsScene',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/GraphicsScene/GraphicsScene.py',
   'PYMODULE'),
  ('pyqtgraph.GraphicsScene.exportDialog',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/GraphicsScene/exportDialog.py',
   'PYMODULE'),
  ('pyqtgraph.GraphicsScene.mouseEvents',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/GraphicsScene/mouseEvents.py',
   'PYMODULE'),
  ('pyqtgraph.GraphicsScene',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/GraphicsScene/__init__.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.VTickGroup',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/graphicsItems/VTickGroup.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.ViewBox',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/graphicsItems/ViewBox/__init__.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.UIGraphicsItem',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/graphicsItems/UIGraphicsItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.TextItem',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/graphicsItems/TextItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.TargetItem',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/graphicsItems/TargetItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.ScatterPlotItem',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/graphicsItems/ScatterPlotItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.ScaleBar',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/graphicsItems/ScaleBar.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.ROI',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/graphicsItems/ROI.py',
   'PYMODULE'),
  ('pyqtgraph.SRTTransform3D',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/SRTTransform3D.py',
   'PYMODULE'),
  ('pyqtgraph.SRTTransform',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/SRTTransform.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.PlotItem',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/graphicsItems/PlotItem/__init__.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.PlotDataItem',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/graphicsItems/PlotDataItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.PlotCurveItem',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/graphicsItems/PlotCurveItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.PColorMeshItem',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/graphicsItems/PColorMeshItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.MultiPlotItem',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/graphicsItems/MultiPlotItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.LinearRegionItem',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/graphicsItems/LinearRegionItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.LegendItem',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/graphicsItems/LegendItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.LabelItem',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/graphicsItems/LabelItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.ItemGroup',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/graphicsItems/ItemGroup.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.IsocurveItem',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/graphicsItems/IsocurveItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.InfiniteLine',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/graphicsItems/InfiniteLine.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.ImageItem',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/graphicsItems/ImageItem.py',
   'PYMODULE'),
  ('pyqtgraph.functions_qimage',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/functions_qimage.py',
   'PYMODULE'),
  ('pyqtgraph.util.numba_helper',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/util/numba_helper.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.HistogramLUTItem',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/graphicsItems/HistogramLUTItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.GridItem',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/graphicsItems/GridItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.GraphItem',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/graphicsItems/GraphItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.GraphicsWidgetAnchor',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/graphicsItems/GraphicsWidgetAnchor.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.GraphicsWidget',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/graphicsItems/GraphicsWidget.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.GraphicsObject',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/graphicsItems/GraphicsObject.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.GraphicsLayout',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/graphicsItems/GraphicsLayout.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.GraphicsItem',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/graphicsItems/GraphicsItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.GradientLegend',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/graphicsItems/GradientLegend.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.GradientEditorItem',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/graphicsItems/GradientEditorItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.FillBetweenItem',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/graphicsItems/FillBetweenItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.ErrorBarItem',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/graphicsItems/ErrorBarItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.DateAxisItem',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/graphicsItems/DateAxisItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.CurvePoint',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/graphicsItems/CurvePoint.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.ColorBarItem',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/graphicsItems/ColorBarItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.ButtonItem',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/graphicsItems/ButtonItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.BarGraphItem',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/graphicsItems/BarGraphItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.AxisItem',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/graphicsItems/AxisItem.py',
   'PYMODULE'),
  ('pyqtgraph.exporters',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/exporters/__init__.py',
   'PYMODULE'),
  ('pyqtgraph.exporters.PrintExporter',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/exporters/PrintExporter.py',
   'PYMODULE'),
  ('pyqtgraph.exporters.Matplotlib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/exporters/Matplotlib.py',
   'PYMODULE'),
  ('pyqtgraph.exporters.HDF5Exporter',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/exporters/HDF5Exporter.py',
   'PYMODULE'),
  ('pyqtgraph.exporters.CSVExporter',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/exporters/CSVExporter.py',
   'PYMODULE'),
  ('pyqtgraph.icons',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/icons/__init__.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.ArrowItem',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/graphicsItems/ArrowItem.py',
   'PYMODULE'),
  ('pyqtgraph.functions',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/functions.py',
   'PYMODULE'),
  ('pyqtgraph.colormap',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colormap.py',
   'PYMODULE'),
  ('pyqtgraph.functions_numba',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/functions_numba.py',
   'PYMODULE'),
  ('pyqtgraph.multiprocess',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/multiprocess/__init__.py',
   'PYMODULE'),
  ('pyqtgraph.multiprocess.remoteproxy',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/multiprocess/remoteproxy.py',
   'PYMODULE'),
  ('pyqtgraph.multiprocess.processes',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/multiprocess/processes.py',
   'PYMODULE'),
  ('pyqtgraph.multiprocess.parallelizer',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/multiprocess/parallelizer.py',
   'PYMODULE'),
  ('pyqtgraph.reload',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/reload.py',
   'PYMODULE'),
  ('pyqtgraph.debug',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/debug.py',
   'PYMODULE'),
  ('pyqtgraph.util.mutex',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/util/mutex.py',
   'PYMODULE'),
  ('pyqtgraph.colors.palette',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/palette.py',
   'PYMODULE'),
  ('pyqtgraph.colors',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/__init__.py',
   'PYMODULE'),
  ('numpy',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/__init__.py',
   'PYMODULE'),
  ('numpy.strings',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/strings/__init__.py',
   'PYMODULE'),
  ('numpy._core.strings',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/_core/strings.py',
   'PYMODULE'),
  ('numpy.core',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/core/__init__.py',
   'PYMODULE'),
  ('numpy.core._utils',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/core/_utils.py',
   'PYMODULE'),
  ('numpy.char',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/char/__init__.py',
   'PYMODULE'),
  ('numpy._core.defchararray',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/_core/defchararray.py',
   'PYMODULE'),
  ('numpy.rec',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/rec/__init__.py',
   'PYMODULE'),
  ('numpy.f2py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/f2py/__init__.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/f2py/diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/f2py/f2py2e.py',
   'PYMODULE'),
  ('numpy.f2py._backends',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/f2py/_backends/__init__.py',
   'PYMODULE'),
  ('numpy.f2py._backends._distutils',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/f2py/_backends/_distutils.py',
   'PYMODULE'),
  ('numpy.f2py._backends._backend',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/f2py/_backends/_backend.py',
   'PYMODULE'),
  ('numpy.f2py._backends._meson',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/f2py/_backends/_meson.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/f2py/auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/f2py/f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/f2py/rules.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/f2py/use_rules.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/f2py/common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/f2py/func2subr.py',
   'PYMODULE'),
  ('numpy.f2py._isocbind',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/f2py/_isocbind.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/f2py/crackfortran.py',
   'PYMODULE'),
  ('charset_normalizer',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/charset_normalizer/__init__.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/charset_normalizer/models.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/charset_normalizer/cd.py',
   'PYMODULE'),
  ('charset_normalizer.assets',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/charset_normalizer/assets/__init__.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/charset_normalizer/utils.py',
   'PYMODULE'),
  ('charset_normalizer.md',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/charset_normalizer/md.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/charset_normalizer/constant.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/charset_normalizer/version.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/charset_normalizer/legacy.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/charset_normalizer/api.py',
   'PYMODULE'),
  ('fileinput',
   '/Users/<USER>/miniconda3/lib/python3.12/fileinput.py',
   'PYMODULE'),
  ('numpy.f2py.symbolic',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/f2py/symbolic.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/f2py/cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/f2py/capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/f2py/cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/f2py/__version__.py',
   'PYMODULE'),
  ('numpy.matlib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/matlib.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/ctypeslib.py',
   'PYMODULE'),
  ('numpy.dtypes',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/dtypes.py',
   'PYMODULE'),
  ('numpy.__config__',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/__config__.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/_distributor_init.py',
   'PYMODULE'),
  ('numpy._expired_attrs_2_0',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/_expired_attrs_2_0.py',
   'PYMODULE'),
  ('PyQt6',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/__init__.py',
   'PYMODULE'),
  ('json',
   '/Users/<USER>/miniconda3/lib/python3.12/json/__init__.py',
   'PYMODULE'),
  ('json.encoder',
   '/Users/<USER>/miniconda3/lib/python3.12/json/encoder.py',
   'PYMODULE'),
  ('json.decoder',
   '/Users/<USER>/miniconda3/lib/python3.12/json/decoder.py',
   'PYMODULE'),
  ('json.scanner',
   '/Users/<USER>/miniconda3/lib/python3.12/json/scanner.py',
   'PYMODULE'),
  ('base64', '/Users/<USER>/miniconda3/lib/python3.12/base64.py', 'PYMODULE'),
  ('pathlib',
   '/Users/<USER>/miniconda3/lib/python3.12/pathlib.py',
   'PYMODULE'),
  ('signal', '/Users/<USER>/miniconda3/lib/python3.12/signal.py', 'PYMODULE'),
  ('pickle', '/Users/<USER>/miniconda3/lib/python3.12/pickle.py', 'PYMODULE'),
  ('_compat_pickle',
   '/Users/<USER>/miniconda3/lib/python3.12/_compat_pickle.py',
   'PYMODULE'),
  ('logging',
   '/Users/<USER>/miniconda3/lib/python3.12/logging/__init__.py',
   'PYMODULE'),
  ('datetime',
   '/Users/<USER>/miniconda3/lib/python3.12/datetime.py',
   'PYMODULE'),
  ('_pydatetime',
   '/Users/<USER>/miniconda3/lib/python3.12/_pydatetime.py',
   'PYMODULE'),
  ('copy', '/Users/<USER>/miniconda3/lib/python3.12/copy.py', 'PYMODULE'),
  ('asyncio',
   '/Users/<USER>/miniconda3/lib/python3.12/asyncio/__init__.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   '/Users/<USER>/miniconda3/lib/python3.12/asyncio/unix_events.py',
   'PYMODULE'),
  ('asyncio.log',
   '/Users/<USER>/miniconda3/lib/python3.12/asyncio/log.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   '/Users/<USER>/miniconda3/lib/python3.12/asyncio/windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   '/Users/<USER>/miniconda3/lib/python3.12/asyncio/windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   '/Users/<USER>/miniconda3/lib/python3.12/asyncio/selector_events.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   '/Users/<USER>/miniconda3/lib/python3.12/asyncio/proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   '/Users/<USER>/miniconda3/lib/python3.12/asyncio/base_subprocess.py',
   'PYMODULE'),
  ('asyncio.threads',
   '/Users/<USER>/miniconda3/lib/python3.12/asyncio/threads.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   '/Users/<USER>/miniconda3/lib/python3.12/asyncio/taskgroups.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   '/Users/<USER>/miniconda3/lib/python3.12/asyncio/subprocess.py',
   'PYMODULE'),
  ('asyncio.streams',
   '/Users/<USER>/miniconda3/lib/python3.12/asyncio/streams.py',
   'PYMODULE'),
  ('asyncio.queues',
   '/Users/<USER>/miniconda3/lib/python3.12/asyncio/queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   '/Users/<USER>/miniconda3/lib/python3.12/asyncio/runners.py',
   'PYMODULE'),
  ('asyncio.base_events',
   '/Users/<USER>/miniconda3/lib/python3.12/asyncio/base_events.py',
   'PYMODULE'),
  ('asyncio.trsock',
   '/Users/<USER>/miniconda3/lib/python3.12/asyncio/trsock.py',
   'PYMODULE'),
  ('asyncio.staggered',
   '/Users/<USER>/miniconda3/lib/python3.12/asyncio/staggered.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   '/Users/<USER>/miniconda3/lib/python3.12/asyncio/timeouts.py',
   'PYMODULE'),
  ('asyncio.tasks',
   '/Users/<USER>/miniconda3/lib/python3.12/asyncio/tasks.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   '/Users/<USER>/miniconda3/lib/python3.12/asyncio/base_tasks.py',
   'PYMODULE'),
  ('asyncio.locks',
   '/Users/<USER>/miniconda3/lib/python3.12/asyncio/locks.py',
   'PYMODULE'),
  ('asyncio.mixins',
   '/Users/<USER>/miniconda3/lib/python3.12/asyncio/mixins.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   '/Users/<USER>/miniconda3/lib/python3.12/asyncio/sslproto.py',
   'PYMODULE'),
  ('asyncio.transports',
   '/Users/<USER>/miniconda3/lib/python3.12/asyncio/transports.py',
   'PYMODULE'),
  ('asyncio.protocols',
   '/Users/<USER>/miniconda3/lib/python3.12/asyncio/protocols.py',
   'PYMODULE'),
  ('asyncio.futures',
   '/Users/<USER>/miniconda3/lib/python3.12/asyncio/futures.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   '/Users/<USER>/miniconda3/lib/python3.12/asyncio/base_futures.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   '/Users/<USER>/miniconda3/lib/python3.12/asyncio/exceptions.py',
   'PYMODULE'),
  ('asyncio.events',
   '/Users/<USER>/miniconda3/lib/python3.12/asyncio/events.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   '/Users/<USER>/miniconda3/lib/python3.12/asyncio/format_helpers.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   '/Users/<USER>/miniconda3/lib/python3.12/asyncio/coroutines.py',
   'PYMODULE'),
  ('asyncio.constants',
   '/Users/<USER>/miniconda3/lib/python3.12/asyncio/constants.py',
   'PYMODULE')],
 [('libpython3.12.dylib',
   '/Users/<USER>/miniconda3/lib/libpython3.12.dylib',
   'BINARY'),
  ('llvmlite/binding/libllvmlite.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/llvmlite/binding/libllvmlite.dylib',
   'BINARY'),
  ('_sounddevice_data/portaudio-binaries/libportaudio.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/_sounddevice_data/portaudio-binaries/libportaudio.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/styles/libqmacstyle.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/plugins/styles/libqmacstyle.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/imageformats/libqtiff.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/plugins/imageformats/libqtiff.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/imageformats/libqpdf.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/plugins/imageformats/libqpdf.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/platforms/libqoffscreen.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/plugins/platforms/libqoffscreen.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/imageformats/libqgif.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/plugins/imageformats/libqgif.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/imageformats/libqico.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/plugins/imageformats/libqico.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/platforms/libqminimal.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/plugins/platforms/libqminimal.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/imageformats/libqwbmp.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/plugins/imageformats/libqwbmp.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/iconengines/libqsvgicon.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/plugins/iconengines/libqsvgicon.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/imageformats/libqsvg.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/plugins/imageformats/libqsvg.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/imageformats/libqicns.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/plugins/imageformats/libqicns.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/imageformats/libqmacheif.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/plugins/imageformats/libqmacheif.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/generic/libqtuiotouchplugin.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/plugins/generic/libqtuiotouchplugin.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/imageformats/libqmacjp2.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/plugins/imageformats/libqmacjp2.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/imageformats/libqwebp.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/plugins/imageformats/libqwebp.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/imageformats/libqjpeg.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/plugins/imageformats/libqjpeg.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/platforms/libqcocoa.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/plugins/platforms/libqcocoa.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/imageformats/libqtga.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/plugins/imageformats/libqtga.dylib',
   'BINARY'),
  ('lib-dynload/_struct.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_struct.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/grp.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/grp.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_lzma.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_lzma.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_bz2.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_bz2.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/zlib.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/zlib.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/unicodedata.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/unicodedata.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/math.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/math.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/array.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/array.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/select.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/select.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_socket.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_socket.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/binascii.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/binascii.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_opcode.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_opcode.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_csv.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_csv.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/resource.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/resource.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/syslog.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/syslog.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_queue.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_queue.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_scproxy.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_scproxy.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/termios.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/termios.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_ssl.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_ssl.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_hashlib.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_hashlib.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha3.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_sha3.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_blake2.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_blake2.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha2.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_sha2.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_md5.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_md5.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha1.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_sha1.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_bisect.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_bisect.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/pyexpat.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/pyexpat.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_posixsubprocess.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_posixsubprocess.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/fcntl.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/fcntl.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_contextvars.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_contextvars.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/mmap.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/mmap.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_posixshmem.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_posixshmem.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_ctypes.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_ctypes.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_multiprocessing.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_multiprocessing.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_decimal.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_decimal.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/readline.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/readline.cpython-312-darwin.so',
   'EXTENSION'),
  ('numpy/_core/_multiarray_umath.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/_core/_multiarray_umath.cpython-312-darwin.so',
   'EXTENSION'),
  ('numpy/linalg/_umath_linalg.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/linalg/_umath_linalg.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_uuid.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_uuid.cpython-312-darwin.so',
   'EXTENSION'),
  ('numba/np/ufunc/workqueue.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/np/ufunc/workqueue.cpython-312-darwin.so',
   'EXTENSION'),
  ('numba/np/ufunc/omppool.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/np/ufunc/omppool.cpython-312-darwin.so',
   'EXTENSION'),
  ('numba/_helperlib.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/_helperlib.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/cmath.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/cmath.cpython-312-darwin.so',
   'EXTENSION'),
  ('markupsafe/_speedups.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/markupsafe/_speedups.cpython-312-darwin.so',
   'EXTENSION'),
  ('_cffi_backend.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/_cffi_backend.cpython-312-darwin.so',
   'EXTENSION'),
  ('numba/core/runtime/_nrt_python.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/runtime/_nrt_python.cpython-312-darwin.so',
   'EXTENSION'),
  ('numba/_dynfunc.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/_dynfunc.cpython-312-darwin.so',
   'EXTENSION'),
  ('numba/_dispatcher.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/_dispatcher.cpython-312-darwin.so',
   'EXTENSION'),
  ('numba/_devicearray.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/_devicearray.cpython-312-darwin.so',
   'EXTENSION'),
  ('numba/cuda/cudadrv/_extras.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cuda/cudadrv/_extras.cpython-312-darwin.so',
   'EXTENSION'),
  ('numba/mviewbuf.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/mviewbuf.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_elementtree.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_elementtree.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_lsprof.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_lsprof.cpython-312-darwin.so',
   'EXTENSION'),
  ('numba/core/typeconv/_typeconv.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/typeconv/_typeconv.cpython-312-darwin.so',
   'EXTENSION'),
  ('numpy/random/bit_generator.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/random/bit_generator.cpython-312-darwin.so',
   'EXTENSION'),
  ('numba/np/ufunc/_internal.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/np/ufunc/_internal.cpython-312-darwin.so',
   'EXTENSION'),
  ('yaml/_yaml.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/yaml/_yaml.cpython-312-darwin.so',
   'EXTENSION'),
  ('numba/experimental/jitclass/_box.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/experimental/jitclass/_box.cpython-312-darwin.so',
   'EXTENSION'),
  ('PyQt6/QtTest.abi3.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/QtTest.abi3.so',
   'EXTENSION'),
  ('PyQt6/QtOpenGLWidgets.abi3.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/QtOpenGLWidgets.abi3.so',
   'EXTENSION'),
  ('PyQt6/QtOpenGL.abi3.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/QtOpenGL.abi3.so',
   'EXTENSION'),
  ('PyQt6/QtSvg.abi3.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/QtSvg.abi3.so',
   'EXTENSION'),
  ('numpy/random/mtrand.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/random/mtrand.cpython-312-darwin.so',
   'EXTENSION'),
  ('numpy/random/_sfc64.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/random/_sfc64.cpython-312-darwin.so',
   'EXTENSION'),
  ('numpy/random/_philox.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/random/_philox.cpython-312-darwin.so',
   'EXTENSION'),
  ('numpy/random/_pcg64.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/random/_pcg64.cpython-312-darwin.so',
   'EXTENSION'),
  ('numpy/random/_mt19937.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/random/_mt19937.cpython-312-darwin.so',
   'EXTENSION'),
  ('numpy/random/_generator.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/random/_generator.cpython-312-darwin.so',
   'EXTENSION'),
  ('numpy/random/_bounded_integers.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/random/_bounded_integers.cpython-312-darwin.so',
   'EXTENSION'),
  ('numpy/random/_common.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/random/_common.cpython-312-darwin.so',
   'EXTENSION'),
  ('numpy/fft/_pocketfft_umath.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/fft/_pocketfft_umath.cpython-312-darwin.so',
   'EXTENSION'),
  ('PyQt6/sip.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/sip.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_heapq.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_heapq.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_multibytecodec.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_multibytecodec.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_jp.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_codecs_jp.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_kr.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_codecs_kr.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_iso2022.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_codecs_iso2022.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_cn.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_codecs_cn.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_tw.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_codecs_tw.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_hk.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_codecs_hk.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_statistics.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_statistics.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_random.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_random.cpython-312-darwin.so',
   'EXTENSION'),
  ('numpy/_core/_multiarray_tests.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/_core/_multiarray_tests.cpython-312-darwin.so',
   'EXTENSION'),
  ('PyQt6/QtWidgets.abi3.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/QtWidgets.abi3.so',
   'EXTENSION'),
  ('PyQt6/QtGui.abi3.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/QtGui.abi3.so',
   'EXTENSION'),
  ('PyQt6/QtDBus.abi3.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/QtDBus.abi3.so',
   'EXTENSION'),
  ('PyQt6/QtCore.abi3.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/QtCore.abi3.so',
   'EXTENSION'),
  ('lib-dynload/_json.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_json.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_pickle.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_pickle.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_datetime.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_datetime.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_asyncio.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_asyncio.cpython-312-darwin.so',
   'EXTENSION'),
  ('libz.1.dylib', '/Users/<USER>/miniconda3/lib/libz.1.dylib', 'BINARY'),
  ('libc++.1.dylib', '/Users/<USER>/miniconda3/lib/libc++.1.dylib', 'BINARY'),
  ('PyQt6/Qt6/lib/QtCore.framework/Versions/A/QtCore',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/lib/QtCore.framework/Versions/A/QtCore',
   'BINARY'),
  ('PyQt6/Qt6/lib/QtGui.framework/Versions/A/QtGui',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/lib/QtGui.framework/Versions/A/QtGui',
   'BINARY'),
  ('PyQt6/Qt6/lib/QtWidgets.framework/Versions/A/QtWidgets',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/lib/QtWidgets.framework/Versions/A/QtWidgets',
   'BINARY'),
  ('PyQt6/Qt6/lib/QtPdf.framework/Versions/A/QtPdf',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/lib/QtPdf.framework/Versions/A/QtPdf',
   'BINARY'),
  ('PyQt6/Qt6/lib/QtSvg.framework/Versions/A/QtSvg',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/lib/QtSvg.framework/Versions/A/QtSvg',
   'BINARY'),
  ('PyQt6/Qt6/lib/QtNetwork.framework/Versions/A/QtNetwork',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/lib/QtNetwork.framework/Versions/A/QtNetwork',
   'BINARY'),
  ('liblzma.5.dylib',
   '/Users/<USER>/miniconda3/lib/liblzma.5.dylib',
   'BINARY'),
  ('libbz2.dylib', '/Users/<USER>/miniconda3/lib/libbz2.dylib', 'BINARY'),
  ('libssl.3.dylib', '/Users/<USER>/miniconda3/lib/libssl.3.dylib', 'BINARY'),
  ('libcrypto.3.dylib',
   '/Users/<USER>/miniconda3/lib/libcrypto.3.dylib',
   'BINARY'),
  ('libexpat.1.dylib',
   '/Users/<USER>/miniconda3/lib/libexpat.1.dylib',
   'BINARY'),
  ('libffi.8.dylib', '/Users/<USER>/miniconda3/lib/libffi.8.dylib', 'BINARY'),
  ('libreadline.8.dylib',
   '/Users/<USER>/miniconda3/lib/libreadline.8.dylib',
   'BINARY'),
  ('PyQt6/Qt6/lib/QtTest.framework/Versions/A/QtTest',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/lib/QtTest.framework/Versions/A/QtTest',
   'BINARY'),
  ('PyQt6/Qt6/lib/QtOpenGL.framework/Versions/A/QtOpenGL',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/lib/QtOpenGL.framework/Versions/A/QtOpenGL',
   'BINARY'),
  ('PyQt6/Qt6/lib/QtOpenGLWidgets.framework/Versions/A/QtOpenGLWidgets',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/lib/QtOpenGLWidgets.framework/Versions/A/QtOpenGLWidgets',
   'BINARY'),
  ('PyQt6/Qt6/lib/QtDBus.framework/Versions/A/QtDBus',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/lib/QtDBus.framework/Versions/A/QtDBus',
   'BINARY'),
  ('libncursesw.6.dylib',
   '/Users/<USER>/miniconda3/lib/libncursesw.6.dylib',
   'BINARY'),
  ('libtinfow.6.dylib',
   '/Users/<USER>/miniconda3/lib/libtinfow.6.dylib',
   'BINARY')],
 [],
 [],
 [('docs/INSTALL.md', '/Users/<USER>/Local/zip/INSTALL.md', 'DATA'),
  ('docs/真实麦克风校准指南.md', '/Users/<USER>/Local/zip/真实麦克风校准指南.md', 'DATA'),
  ('docs/麦克风校准使用说明.md', '/Users/<USER>/Local/zip/麦克风校准使用说明.md', 'DATA'),
  ('logo.ico', '/Users/<USER>/Local/zip/logo.ico', 'DATA'),
  ('logo.png', '/Users/<USER>/Local/zip/logo.png', 'DATA'),
  ('preset.json', '/Users/<USER>/Local/zip/preset.json', 'DATA'),
  ('start.png', '/Users/<USER>/Local/zip/start.png', 'DATA'),
  ('base_library.zip',
   '/Users/<USER>/Local/zip/build/hifiscan-windows/base_library.zip',
   'DATA'),
  ('setuptools/_vendor/jaraco/text/Lorem ipsum.txt',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/_vendor/jaraco/text/Lorem '
   'ipsum.txt',
   'DATA'),
  ('importlib_metadata-8.6.1.dist-info/RECORD',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/importlib_metadata-8.6.1.dist-info/RECORD',
   'DATA'),
  ('importlib_metadata-8.6.1.dist-info/top_level.txt',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/importlib_metadata-8.6.1.dist-info/top_level.txt',
   'DATA'),
  ('importlib_metadata-8.6.1.dist-info/LICENSE',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/importlib_metadata-8.6.1.dist-info/LICENSE',
   'DATA'),
  ('importlib_metadata-8.6.1.dist-info/WHEEL',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/importlib_metadata-8.6.1.dist-info/WHEEL',
   'DATA'),
  ('importlib_metadata-8.6.1.dist-info/INSTALLER',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/importlib_metadata-8.6.1.dist-info/INSTALLER',
   'DATA'),
  ('importlib_metadata-8.6.1.dist-info/METADATA',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/importlib_metadata-8.6.1.dist-info/METADATA',
   'DATA'),
  ('PyQt6/uic/widget-plugins/qaxcontainer.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/uic/widget-plugins/qaxcontainer.py',
   'DATA'),
  ('PyQt6/uic/widget-plugins/qtquickwidgets.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/uic/widget-plugins/qtquickwidgets.py',
   'DATA'),
  ('PyQt6/uic/widget-plugins/qtcharts.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/uic/widget-plugins/qtcharts.py',
   'DATA'),
  ('PyQt6/uic/widget-plugins/qtprintsupport.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/uic/widget-plugins/qtprintsupport.py',
   'DATA'),
  ('PyQt6/uic/widget-plugins/qtopenglwidgets.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/uic/widget-plugins/qtopenglwidgets.py',
   'DATA'),
  ('PyQt6/uic/widget-plugins/qtwebenginewidgets.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/uic/widget-plugins/qtwebenginewidgets.py',
   'DATA'),
  ('PyQt6/uic/widget-plugins/qscintilla.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/uic/widget-plugins/qscintilla.py',
   'DATA'),
  ('_sounddevice_data/portaudio-binaries/README.md',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/_sounddevice_data/portaudio-binaries/README.md',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-D2.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-D2.csv',
   'DATA'),
  ('pyqtgraph/icons/peegee/peegee_256px.png',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/icons/peegee/peegee_256px.png',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-I2.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-I2.csv',
   'DATA'),
  ('pyqtgraph/icons/peegee/peegee_128px.png',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/icons/peegee/peegee_128px.png',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-R4.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-R4.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/PAL-relaxed.hex',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/PAL-relaxed.hex',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-R3.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-R3.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-C7s.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-C7s.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-C5.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-C5.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-L2.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-L2.csv',
   'DATA'),
  ('pyqtgraph/icons/icons.svg',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/icons/icons.svg',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-D11.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-D11.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-CBL2.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-CBL2.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-D13.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-D13.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-CBL1.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-CBL1.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-I1.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-I1.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-C2s.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-C2s.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-CBTL2.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-CBTL2.csv',
   'DATA'),
  ('pyqtgraph/Qt/QtTest.pyi',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/Qt/QtTest.pyi',
   'DATA'),
  ('pyqtgraph/colors/maps/PAL-relaxed_bright.hex',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/PAL-relaxed_bright.hex',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-R1.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-R1.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-C3.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-C3.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-C6.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-C6.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-D9.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-D9.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CC0 legal code - applies to virids, magma, plasma, '
   'inferno and cividis.txt',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CC0 '
   'legal code - applies to virids, magma, plasma, inferno and cividis.txt',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-CBTC2.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-CBTC2.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-L12.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-L12.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-D8.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-D8.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-L6.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-L6.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-D10.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-D10.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-L5.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-L5.csv',
   'DATA'),
  ('pyqtgraph/icons/peegee/<EMAIL>',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/icons/peegee/<EMAIL>',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-D12.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-D12.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-C1s.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-C1s.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-R2.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-R2.csv',
   'DATA'),
  ('pyqtgraph/icons/auto.png',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/icons/auto.png',
   'DATA'),
  ('pyqtgraph/icons/ctrl.png',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/icons/ctrl.png',
   'DATA'),
  ('pyqtgraph/colors/maps/turbo.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/turbo.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-C7.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-C7.csv',
   'DATA'),
  ('pyqtgraph/icons/peegee/<EMAIL>',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/icons/peegee/<EMAIL>',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-D4.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-D4.csv',
   'DATA'),
  ('pyqtgraph/Qt/__init__.pyi',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/Qt/__init__.pyi',
   'DATA'),
  ('pyqtgraph/icons/peegee/peegee.svg',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/icons/peegee/peegee.svg',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-C4s.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-C4s.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-D3.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-D3.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-L7.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-L7.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-D1.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-D1.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-CBTL1.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-CBTL1.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/cividis.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/cividis.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-L10.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-L10.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-C1.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-C1.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-I3.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-I3.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-CBC2.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-CBC2.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-D7.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-D7.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-D6.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-D6.csv',
   'DATA'),
  ('pyqtgraph/Qt/QtCore/__init__.pyi',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/Qt/QtCore/__init__.pyi',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-C5s.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-C5s.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-L15.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-L15.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-L14.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-L14.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-CBTC1.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-CBTC1.csv',
   'DATA'),
  ('pyqtgraph/icons/invisibleEye.svg',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/icons/invisibleEye.svg',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-C4.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-C4.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-L13.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-L13.csv',
   'DATA'),
  ('pyqtgraph/icons/peegee/<EMAIL>',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/icons/peegee/<EMAIL>',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-C6s.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-C6s.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-CBC1.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-CBC1.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-CBTD1.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-CBTD1.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-D1A.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-D1A.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-L9.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-L9.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CC-BY license - applies to CET color map data.txt',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CC-BY '
   'license - applies to CET color map data.txt',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-L18.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-L18.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-L1.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-L1.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/viridis.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/viridis.csv',
   'DATA'),
  ('pyqtgraph/Qt/QtWidgets/__init__.pyi',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/Qt/QtWidgets/__init__.pyi',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-CBD1.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-CBD1.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-L3.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-L3.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/inferno.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/inferno.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-L4.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-L4.csv',
   'DATA'),
  ('pyqtgraph/icons/peegee/peegee_512px.png',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/icons/peegee/peegee_512px.png',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-L16.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-L16.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-L11.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-L11.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-L8.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-L8.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-C2.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-C2.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/magma.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/magma.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/plasma.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/plasma.csv',
   'DATA'),
  ('pyqtgraph/icons/default.png',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/icons/default.png',
   'DATA'),
  ('pyqtgraph/icons/peegee/peegee_192px.png',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/icons/peegee/peegee_192px.png',
   'DATA'),
  ('pyqtgraph/Qt/QtGui/__init__.pyi',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/Qt/QtGui/__init__.pyi',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-C3s.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-C3s.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-L19.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-L19.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-L17.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-L17.csv',
   'DATA'),
  ('pyqtgraph/icons/lock.png',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/icons/lock.png',
   'DATA'),
  ('pyqtgraph/Qt/QtSvg.pyi',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/Qt/QtSvg.pyi',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_he.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_he.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_es.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_es.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_ja.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_ja.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_nn.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_nn.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_ja.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_ja.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_lv.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_lv.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_tr.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_tr.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_pl.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_pl.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_zh_TW.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_fa.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_fa.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_it.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_it.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_nl.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_nl.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_ca.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_ca.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_lv.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_lv.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_hu.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_hu.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_da.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_da.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_zh_CN.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_gd.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_gd.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_zh_CN.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_zh_CN.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_hr.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_hr.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_cs.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_cs.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_pl.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_pl.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_he.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_he.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_en.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_en.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_cs.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_cs.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_en.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_en.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_it.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_it.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_sk.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_sk.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_gl.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_gl.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_pt_PT.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_pt_PT.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_es.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_es.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_pl.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_pl.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_nl.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_nl.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_ka.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_ka.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_uk.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_uk.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_lt.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_lt.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_nn.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_nn.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_sk.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_sk.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_de.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_de.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_da.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_da.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_it.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_it.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_bg.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_bg.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_de.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_de.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_gd.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_gd.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_uk.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_uk.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_fi.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_fi.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_bg.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_bg.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_ko.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_ko.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_hr.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_hr.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_fi.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_fi.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_ca.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_ca.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_pt_BR.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_pt_BR.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_ru.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_ru.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_hr.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_hr.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_ko.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_ko.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_nn.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_nn.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_nl.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_nl.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_tr.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_tr.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_en.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_en.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_hu.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_hu.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_fa.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_fa.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_de.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_de.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_es.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_es.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_pt_BR.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_pt_BR.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_bg.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_bg.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_zh_CN.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_zh_CN.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_fr.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_fr.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_zh_TW.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_zh_TW.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_uk.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_uk.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_sv.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_sv.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_fr.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_fr.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_ru.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_ru.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_ru.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_ru.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_zh_TW.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_ka.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_ka.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_pt_BR.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_pt_BR.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_ar.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_ar.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_fr.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_fr.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_tr.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_tr.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_ar.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_ar.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_sl.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_sl.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_cs.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_cs.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_ka.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_ka.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_ja.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_ja.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_da.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_da.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_ar.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_ar.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_sk.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_sk.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_sl.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_sl.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_ko.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_ko.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_hu.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_hu.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_ca.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_ca.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_gl.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_gl.qm',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info/RECORD',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/MarkupSafe-3.0.2.dist-info/RECORD',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info/METADATA',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/MarkupSafe-3.0.2.dist-info/METADATA',
   'DATA'),
  ('wheel-0.45.1.dist-info/entry_points.txt',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/wheel-0.45.1.dist-info/entry_points.txt',
   'DATA'),
  ('wheel-0.45.1.dist-info/LICENSE.txt',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/wheel-0.45.1.dist-info/LICENSE.txt',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info/WHEEL',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/MarkupSafe-3.0.2.dist-info/WHEEL',
   'DATA'),
  ('wheel-0.45.1.dist-info/METADATA',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/wheel-0.45.1.dist-info/METADATA',
   'DATA'),
  ('wheel-0.45.1.dist-info/WHEEL',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/wheel-0.45.1.dist-info/WHEEL',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info/INSTALLER',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/MarkupSafe-3.0.2.dist-info/INSTALLER',
   'DATA'),
  ('wheel-0.45.1.dist-info/REQUESTED',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/wheel-0.45.1.dist-info/REQUESTED',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info/top_level.txt',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/MarkupSafe-3.0.2.dist-info/top_level.txt',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info/LICENSE.txt',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/MarkupSafe-3.0.2.dist-info/LICENSE.txt',
   'DATA'),
  ('wheel-0.45.1.dist-info/INSTALLER',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/wheel-0.45.1.dist-info/INSTALLER',
   'DATA'),
  ('wheel-0.45.1.dist-info/RECORD',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/wheel-0.45.1.dist-info/RECORD',
   'DATA'),
  ('QtCore', 'PyQt6/Qt6/lib/QtCore.framework/Versions/A/QtCore', 'SYMLINK'),
  ('QtGui', 'PyQt6/Qt6/lib/QtGui.framework/Versions/A/QtGui', 'SYMLINK'),
  ('QtWidgets',
   'PyQt6/Qt6/lib/QtWidgets.framework/Versions/A/QtWidgets',
   'SYMLINK'),
  ('QtPdf', 'PyQt6/Qt6/lib/QtPdf.framework/Versions/A/QtPdf', 'SYMLINK'),
  ('QtSvg', 'PyQt6/Qt6/lib/QtSvg.framework/Versions/A/QtSvg', 'SYMLINK'),
  ('QtNetwork',
   'PyQt6/Qt6/lib/QtNetwork.framework/Versions/A/QtNetwork',
   'SYMLINK'),
  ('QtTest', 'PyQt6/Qt6/lib/QtTest.framework/Versions/A/QtTest', 'SYMLINK'),
  ('QtOpenGL',
   'PyQt6/Qt6/lib/QtOpenGL.framework/Versions/A/QtOpenGL',
   'SYMLINK'),
  ('QtOpenGLWidgets',
   'PyQt6/Qt6/lib/QtOpenGLWidgets.framework/Versions/A/QtOpenGLWidgets',
   'SYMLINK'),
  ('QtDBus', 'PyQt6/Qt6/lib/QtDBus.framework/Versions/A/QtDBus', 'SYMLINK'),
  ('PyQt6/Qt6/lib/QtCore.framework/QtCore',
   'Versions/Current/QtCore',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtCore.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtCore.framework/Versions/A/Resources/Info.plist',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/lib/QtCore.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt6/Qt6/lib/QtCore.framework/Versions/Current', 'A', 'SYMLINK'),
  ('PyQt6/Qt6/lib/QtDBus.framework/QtDBus',
   'Versions/Current/QtDBus',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtDBus.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtDBus.framework/Versions/A/Resources/Info.plist',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/lib/QtDBus.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt6/Qt6/lib/QtDBus.framework/Versions/Current', 'A', 'SYMLINK'),
  ('PyQt6/Qt6/lib/QtGui.framework/QtGui', 'Versions/Current/QtGui', 'SYMLINK'),
  ('PyQt6/Qt6/lib/QtGui.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtGui.framework/Versions/A/Resources/Info.plist',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/lib/QtGui.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt6/Qt6/lib/QtGui.framework/Versions/Current', 'A', 'SYMLINK'),
  ('PyQt6/Qt6/lib/QtNetwork.framework/QtNetwork',
   'Versions/Current/QtNetwork',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtNetwork.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtNetwork.framework/Versions/A/Resources/Info.plist',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/lib/QtNetwork.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt6/Qt6/lib/QtNetwork.framework/Versions/Current', 'A', 'SYMLINK'),
  ('PyQt6/Qt6/lib/QtOpenGL.framework/QtOpenGL',
   'Versions/Current/QtOpenGL',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtOpenGL.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtOpenGL.framework/Versions/A/Resources/Info.plist',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/lib/QtOpenGL.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt6/Qt6/lib/QtOpenGL.framework/Versions/Current', 'A', 'SYMLINK'),
  ('PyQt6/Qt6/lib/QtOpenGLWidgets.framework/QtOpenGLWidgets',
   'Versions/Current/QtOpenGLWidgets',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtOpenGLWidgets.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtOpenGLWidgets.framework/Versions/A/Resources/Info.plist',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/lib/QtOpenGLWidgets.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt6/Qt6/lib/QtOpenGLWidgets.framework/Versions/Current', 'A', 'SYMLINK'),
  ('PyQt6/Qt6/lib/QtPdf.framework/QtPdf', 'Versions/Current/QtPdf', 'SYMLINK'),
  ('PyQt6/Qt6/lib/QtPdf.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtPdf.framework/Versions/A/Resources/Info.plist',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/lib/QtPdf.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt6/Qt6/lib/QtPdf.framework/Versions/Current', 'A', 'SYMLINK'),
  ('PyQt6/Qt6/lib/QtSvg.framework/QtSvg', 'Versions/Current/QtSvg', 'SYMLINK'),
  ('PyQt6/Qt6/lib/QtSvg.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtSvg.framework/Versions/A/Resources/Info.plist',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/lib/QtSvg.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt6/Qt6/lib/QtSvg.framework/Versions/Current', 'A', 'SYMLINK'),
  ('PyQt6/Qt6/lib/QtTest.framework/QtTest',
   'Versions/Current/QtTest',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtTest.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtTest.framework/Versions/A/Resources/Info.plist',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/lib/QtTest.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt6/Qt6/lib/QtTest.framework/Versions/Current', 'A', 'SYMLINK'),
  ('PyQt6/Qt6/lib/QtWidgets.framework/QtWidgets',
   'Versions/Current/QtWidgets',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtWidgets.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtWidgets.framework/Versions/A/Resources/Info.plist',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/lib/QtWidgets.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt6/Qt6/lib/QtWidgets.framework/Versions/Current', 'A', 'SYMLINK')])
