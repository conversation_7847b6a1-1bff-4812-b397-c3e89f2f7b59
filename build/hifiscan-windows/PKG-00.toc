('/Users/<USER>/Local/zip/build/hifiscan-windows/HiFiScan.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   '/Users/<USER>/Local/zip/build/hifiscan-windows/PYZ-00.pyz',
   'PYZ'),
  ('lib-dynload/_struct.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_struct.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/zlib.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/zlib.cpython-312-darwin.so',
   'EXTENSION'),
  ('struct',
   '/Users/<USER>/Local/zip/build/hifiscan-windows/localpycs/struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   '/Users/<USER>/Local/zip/build/hifiscan-windows/localpycs/pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   '/Users/<USER>/Local/zip/build/hifiscan-windows/localpycs/pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   '/Users/<USER>/Local/zip/build/hifiscan-windows/localpycs/pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyInstaller/loader/pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyInstaller/hooks/rthooks/pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyInstaller/hooks/rthooks/pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyInstaller/hooks/rthooks/pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_pyqtgraph_multiprocess',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/_pyinstaller_hooks_contrib/rthooks/pyi_rth_pyqtgraph_multiprocess.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt6',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pyqt6.py',
   'PYSOURCE'),
  ('app1', '/Users/<USER>/Local/zip/app1.py', 'PYSOURCE'),
  ('libpython3.12.dylib',
   '/Users/<USER>/miniconda3/lib/libpython3.12.dylib',
   'BINARY'),
  ('llvmlite/binding/libllvmlite.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/llvmlite/binding/libllvmlite.dylib',
   'BINARY'),
  ('_sounddevice_data/portaudio-binaries/libportaudio.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/_sounddevice_data/portaudio-binaries/libportaudio.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/styles/libqmacstyle.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/plugins/styles/libqmacstyle.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/imageformats/libqtiff.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/plugins/imageformats/libqtiff.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/imageformats/libqpdf.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/plugins/imageformats/libqpdf.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/platforms/libqoffscreen.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/plugins/platforms/libqoffscreen.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/imageformats/libqgif.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/plugins/imageformats/libqgif.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/imageformats/libqico.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/plugins/imageformats/libqico.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/platforms/libqminimal.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/plugins/platforms/libqminimal.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/imageformats/libqwbmp.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/plugins/imageformats/libqwbmp.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/iconengines/libqsvgicon.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/plugins/iconengines/libqsvgicon.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/imageformats/libqsvg.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/plugins/imageformats/libqsvg.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/imageformats/libqicns.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/plugins/imageformats/libqicns.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/imageformats/libqmacheif.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/plugins/imageformats/libqmacheif.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/generic/libqtuiotouchplugin.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/plugins/generic/libqtuiotouchplugin.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/imageformats/libqmacjp2.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/plugins/imageformats/libqmacjp2.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/imageformats/libqwebp.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/plugins/imageformats/libqwebp.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/imageformats/libqjpeg.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/plugins/imageformats/libqjpeg.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/platforms/libqcocoa.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/plugins/platforms/libqcocoa.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/imageformats/libqtga.dylib',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/plugins/imageformats/libqtga.dylib',
   'BINARY'),
  ('lib-dynload/grp.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/grp.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_lzma.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_lzma.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_bz2.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_bz2.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/unicodedata.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/unicodedata.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/math.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/math.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/array.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/array.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/select.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/select.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_socket.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_socket.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/binascii.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/binascii.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_opcode.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_opcode.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_csv.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_csv.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/resource.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/resource.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/syslog.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/syslog.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_queue.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_queue.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_scproxy.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_scproxy.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/termios.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/termios.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_ssl.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_ssl.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_hashlib.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_hashlib.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha3.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_sha3.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_blake2.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_blake2.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha2.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_sha2.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_md5.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_md5.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha1.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_sha1.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_bisect.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_bisect.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/pyexpat.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/pyexpat.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_posixsubprocess.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_posixsubprocess.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/fcntl.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/fcntl.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_contextvars.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_contextvars.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/mmap.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/mmap.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_posixshmem.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_posixshmem.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_ctypes.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_ctypes.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_multiprocessing.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_multiprocessing.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_decimal.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_decimal.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/readline.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/readline.cpython-312-darwin.so',
   'EXTENSION'),
  ('numpy/_core/_multiarray_umath.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/_core/_multiarray_umath.cpython-312-darwin.so',
   'EXTENSION'),
  ('numpy/linalg/_umath_linalg.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/linalg/_umath_linalg.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_uuid.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_uuid.cpython-312-darwin.so',
   'EXTENSION'),
  ('numba/np/ufunc/workqueue.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/np/ufunc/workqueue.cpython-312-darwin.so',
   'EXTENSION'),
  ('numba/np/ufunc/omppool.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/np/ufunc/omppool.cpython-312-darwin.so',
   'EXTENSION'),
  ('numba/_helperlib.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/_helperlib.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/cmath.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/cmath.cpython-312-darwin.so',
   'EXTENSION'),
  ('markupsafe/_speedups.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/markupsafe/_speedups.cpython-312-darwin.so',
   'EXTENSION'),
  ('_cffi_backend.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/_cffi_backend.cpython-312-darwin.so',
   'EXTENSION'),
  ('numba/core/runtime/_nrt_python.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/runtime/_nrt_python.cpython-312-darwin.so',
   'EXTENSION'),
  ('numba/_dynfunc.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/_dynfunc.cpython-312-darwin.so',
   'EXTENSION'),
  ('numba/_dispatcher.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/_dispatcher.cpython-312-darwin.so',
   'EXTENSION'),
  ('numba/_devicearray.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/_devicearray.cpython-312-darwin.so',
   'EXTENSION'),
  ('numba/cuda/cudadrv/_extras.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/cuda/cudadrv/_extras.cpython-312-darwin.so',
   'EXTENSION'),
  ('numba/mviewbuf.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/mviewbuf.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_elementtree.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_elementtree.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_lsprof.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_lsprof.cpython-312-darwin.so',
   'EXTENSION'),
  ('numba/core/typeconv/_typeconv.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/core/typeconv/_typeconv.cpython-312-darwin.so',
   'EXTENSION'),
  ('numpy/random/bit_generator.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/random/bit_generator.cpython-312-darwin.so',
   'EXTENSION'),
  ('numba/np/ufunc/_internal.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/np/ufunc/_internal.cpython-312-darwin.so',
   'EXTENSION'),
  ('yaml/_yaml.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/yaml/_yaml.cpython-312-darwin.so',
   'EXTENSION'),
  ('numba/experimental/jitclass/_box.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numba/experimental/jitclass/_box.cpython-312-darwin.so',
   'EXTENSION'),
  ('PyQt6/QtTest.abi3.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/QtTest.abi3.so',
   'EXTENSION'),
  ('PyQt6/QtOpenGLWidgets.abi3.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/QtOpenGLWidgets.abi3.so',
   'EXTENSION'),
  ('PyQt6/QtOpenGL.abi3.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/QtOpenGL.abi3.so',
   'EXTENSION'),
  ('PyQt6/QtSvg.abi3.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/QtSvg.abi3.so',
   'EXTENSION'),
  ('numpy/random/mtrand.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/random/mtrand.cpython-312-darwin.so',
   'EXTENSION'),
  ('numpy/random/_sfc64.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/random/_sfc64.cpython-312-darwin.so',
   'EXTENSION'),
  ('numpy/random/_philox.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/random/_philox.cpython-312-darwin.so',
   'EXTENSION'),
  ('numpy/random/_pcg64.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/random/_pcg64.cpython-312-darwin.so',
   'EXTENSION'),
  ('numpy/random/_mt19937.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/random/_mt19937.cpython-312-darwin.so',
   'EXTENSION'),
  ('numpy/random/_generator.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/random/_generator.cpython-312-darwin.so',
   'EXTENSION'),
  ('numpy/random/_bounded_integers.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/random/_bounded_integers.cpython-312-darwin.so',
   'EXTENSION'),
  ('numpy/random/_common.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/random/_common.cpython-312-darwin.so',
   'EXTENSION'),
  ('numpy/fft/_pocketfft_umath.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/fft/_pocketfft_umath.cpython-312-darwin.so',
   'EXTENSION'),
  ('PyQt6/sip.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/sip.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_heapq.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_heapq.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_multibytecodec.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_multibytecodec.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_jp.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_codecs_jp.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_kr.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_codecs_kr.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_iso2022.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_codecs_iso2022.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_cn.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_codecs_cn.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_tw.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_codecs_tw.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_hk.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_codecs_hk.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_statistics.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_statistics.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_random.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_random.cpython-312-darwin.so',
   'EXTENSION'),
  ('numpy/_core/_multiarray_tests.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/numpy/_core/_multiarray_tests.cpython-312-darwin.so',
   'EXTENSION'),
  ('PyQt6/QtWidgets.abi3.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/QtWidgets.abi3.so',
   'EXTENSION'),
  ('PyQt6/QtGui.abi3.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/QtGui.abi3.so',
   'EXTENSION'),
  ('PyQt6/QtDBus.abi3.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/QtDBus.abi3.so',
   'EXTENSION'),
  ('PyQt6/QtCore.abi3.so',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/QtCore.abi3.so',
   'EXTENSION'),
  ('lib-dynload/_json.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_json.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_pickle.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_pickle.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_datetime.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_datetime.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_asyncio.cpython-312-darwin.so',
   '/Users/<USER>/miniconda3/lib/python3.12/lib-dynload/_asyncio.cpython-312-darwin.so',
   'EXTENSION'),
  ('libz.1.dylib', '/Users/<USER>/miniconda3/lib/libz.1.dylib', 'BINARY'),
  ('libc++.1.dylib', '/Users/<USER>/miniconda3/lib/libc++.1.dylib', 'BINARY'),
  ('PyQt6/Qt6/lib/QtCore.framework/Versions/A/QtCore',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/lib/QtCore.framework/Versions/A/QtCore',
   'BINARY'),
  ('PyQt6/Qt6/lib/QtGui.framework/Versions/A/QtGui',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/lib/QtGui.framework/Versions/A/QtGui',
   'BINARY'),
  ('PyQt6/Qt6/lib/QtWidgets.framework/Versions/A/QtWidgets',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/lib/QtWidgets.framework/Versions/A/QtWidgets',
   'BINARY'),
  ('PyQt6/Qt6/lib/QtPdf.framework/Versions/A/QtPdf',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/lib/QtPdf.framework/Versions/A/QtPdf',
   'BINARY'),
  ('PyQt6/Qt6/lib/QtSvg.framework/Versions/A/QtSvg',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/lib/QtSvg.framework/Versions/A/QtSvg',
   'BINARY'),
  ('PyQt6/Qt6/lib/QtNetwork.framework/Versions/A/QtNetwork',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/lib/QtNetwork.framework/Versions/A/QtNetwork',
   'BINARY'),
  ('liblzma.5.dylib',
   '/Users/<USER>/miniconda3/lib/liblzma.5.dylib',
   'BINARY'),
  ('libbz2.dylib', '/Users/<USER>/miniconda3/lib/libbz2.dylib', 'BINARY'),
  ('libssl.3.dylib', '/Users/<USER>/miniconda3/lib/libssl.3.dylib', 'BINARY'),
  ('libcrypto.3.dylib',
   '/Users/<USER>/miniconda3/lib/libcrypto.3.dylib',
   'BINARY'),
  ('libexpat.1.dylib',
   '/Users/<USER>/miniconda3/lib/libexpat.1.dylib',
   'BINARY'),
  ('libffi.8.dylib', '/Users/<USER>/miniconda3/lib/libffi.8.dylib', 'BINARY'),
  ('libreadline.8.dylib',
   '/Users/<USER>/miniconda3/lib/libreadline.8.dylib',
   'BINARY'),
  ('PyQt6/Qt6/lib/QtTest.framework/Versions/A/QtTest',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/lib/QtTest.framework/Versions/A/QtTest',
   'BINARY'),
  ('PyQt6/Qt6/lib/QtOpenGL.framework/Versions/A/QtOpenGL',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/lib/QtOpenGL.framework/Versions/A/QtOpenGL',
   'BINARY'),
  ('PyQt6/Qt6/lib/QtOpenGLWidgets.framework/Versions/A/QtOpenGLWidgets',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/lib/QtOpenGLWidgets.framework/Versions/A/QtOpenGLWidgets',
   'BINARY'),
  ('PyQt6/Qt6/lib/QtDBus.framework/Versions/A/QtDBus',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/lib/QtDBus.framework/Versions/A/QtDBus',
   'BINARY'),
  ('libncursesw.6.dylib',
   '/Users/<USER>/miniconda3/lib/libncursesw.6.dylib',
   'BINARY'),
  ('libtinfow.6.dylib',
   '/Users/<USER>/miniconda3/lib/libtinfow.6.dylib',
   'BINARY'),
  ('docs/INSTALL.md', '/Users/<USER>/Local/zip/INSTALL.md', 'DATA'),
  ('docs/真实麦克风校准指南.md', '/Users/<USER>/Local/zip/真实麦克风校准指南.md', 'DATA'),
  ('docs/麦克风校准使用说明.md', '/Users/<USER>/Local/zip/麦克风校准使用说明.md', 'DATA'),
  ('logo.ico', '/Users/<USER>/Local/zip/logo.ico', 'DATA'),
  ('logo.png', '/Users/<USER>/Local/zip/logo.png', 'DATA'),
  ('preset.json', '/Users/<USER>/Local/zip/preset.json', 'DATA'),
  ('start.png', '/Users/<USER>/Local/zip/start.png', 'DATA'),
  ('base_library.zip',
   '/Users/<USER>/Local/zip/build/hifiscan-windows/base_library.zip',
   'DATA'),
  ('setuptools/_vendor/jaraco/text/Lorem ipsum.txt',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/setuptools/_vendor/jaraco/text/Lorem '
   'ipsum.txt',
   'DATA'),
  ('importlib_metadata-8.6.1.dist-info/RECORD',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/importlib_metadata-8.6.1.dist-info/RECORD',
   'DATA'),
  ('importlib_metadata-8.6.1.dist-info/top_level.txt',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/importlib_metadata-8.6.1.dist-info/top_level.txt',
   'DATA'),
  ('importlib_metadata-8.6.1.dist-info/LICENSE',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/importlib_metadata-8.6.1.dist-info/LICENSE',
   'DATA'),
  ('importlib_metadata-8.6.1.dist-info/WHEEL',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/importlib_metadata-8.6.1.dist-info/WHEEL',
   'DATA'),
  ('importlib_metadata-8.6.1.dist-info/INSTALLER',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/importlib_metadata-8.6.1.dist-info/INSTALLER',
   'DATA'),
  ('importlib_metadata-8.6.1.dist-info/METADATA',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/importlib_metadata-8.6.1.dist-info/METADATA',
   'DATA'),
  ('PyQt6/uic/widget-plugins/qaxcontainer.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/uic/widget-plugins/qaxcontainer.py',
   'DATA'),
  ('PyQt6/uic/widget-plugins/qtquickwidgets.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/uic/widget-plugins/qtquickwidgets.py',
   'DATA'),
  ('PyQt6/uic/widget-plugins/qtcharts.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/uic/widget-plugins/qtcharts.py',
   'DATA'),
  ('PyQt6/uic/widget-plugins/qtprintsupport.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/uic/widget-plugins/qtprintsupport.py',
   'DATA'),
  ('PyQt6/uic/widget-plugins/qtopenglwidgets.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/uic/widget-plugins/qtopenglwidgets.py',
   'DATA'),
  ('PyQt6/uic/widget-plugins/qtwebenginewidgets.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/uic/widget-plugins/qtwebenginewidgets.py',
   'DATA'),
  ('PyQt6/uic/widget-plugins/qscintilla.py',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/uic/widget-plugins/qscintilla.py',
   'DATA'),
  ('_sounddevice_data/portaudio-binaries/README.md',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/_sounddevice_data/portaudio-binaries/README.md',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-D2.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-D2.csv',
   'DATA'),
  ('pyqtgraph/icons/peegee/peegee_256px.png',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/icons/peegee/peegee_256px.png',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-I2.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-I2.csv',
   'DATA'),
  ('pyqtgraph/icons/peegee/peegee_128px.png',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/icons/peegee/peegee_128px.png',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-R4.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-R4.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/PAL-relaxed.hex',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/PAL-relaxed.hex',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-R3.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-R3.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-C7s.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-C7s.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-C5.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-C5.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-L2.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-L2.csv',
   'DATA'),
  ('pyqtgraph/icons/icons.svg',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/icons/icons.svg',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-D11.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-D11.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-CBL2.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-CBL2.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-D13.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-D13.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-CBL1.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-CBL1.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-I1.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-I1.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-C2s.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-C2s.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-CBTL2.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-CBTL2.csv',
   'DATA'),
  ('pyqtgraph/Qt/QtTest.pyi',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/Qt/QtTest.pyi',
   'DATA'),
  ('pyqtgraph/colors/maps/PAL-relaxed_bright.hex',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/PAL-relaxed_bright.hex',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-R1.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-R1.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-C3.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-C3.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-C6.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-C6.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-D9.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-D9.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CC0 legal code - applies to virids, magma, plasma, '
   'inferno and cividis.txt',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CC0 '
   'legal code - applies to virids, magma, plasma, inferno and cividis.txt',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-CBTC2.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-CBTC2.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-L12.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-L12.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-D8.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-D8.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-L6.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-L6.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-D10.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-D10.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-L5.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-L5.csv',
   'DATA'),
  ('pyqtgraph/icons/peegee/<EMAIL>',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/icons/peegee/<EMAIL>',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-D12.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-D12.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-C1s.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-C1s.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-R2.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-R2.csv',
   'DATA'),
  ('pyqtgraph/icons/auto.png',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/icons/auto.png',
   'DATA'),
  ('pyqtgraph/icons/ctrl.png',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/icons/ctrl.png',
   'DATA'),
  ('pyqtgraph/colors/maps/turbo.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/turbo.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-C7.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-C7.csv',
   'DATA'),
  ('pyqtgraph/icons/peegee/<EMAIL>',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/icons/peegee/<EMAIL>',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-D4.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-D4.csv',
   'DATA'),
  ('pyqtgraph/Qt/__init__.pyi',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/Qt/__init__.pyi',
   'DATA'),
  ('pyqtgraph/icons/peegee/peegee.svg',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/icons/peegee/peegee.svg',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-C4s.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-C4s.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-D3.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-D3.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-L7.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-L7.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-D1.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-D1.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-CBTL1.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-CBTL1.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/cividis.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/cividis.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-L10.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-L10.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-C1.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-C1.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-I3.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-I3.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-CBC2.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-CBC2.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-D7.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-D7.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-D6.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-D6.csv',
   'DATA'),
  ('pyqtgraph/Qt/QtCore/__init__.pyi',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/Qt/QtCore/__init__.pyi',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-C5s.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-C5s.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-L15.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-L15.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-L14.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-L14.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-CBTC1.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-CBTC1.csv',
   'DATA'),
  ('pyqtgraph/icons/invisibleEye.svg',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/icons/invisibleEye.svg',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-C4.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-C4.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-L13.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-L13.csv',
   'DATA'),
  ('pyqtgraph/icons/peegee/<EMAIL>',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/icons/peegee/<EMAIL>',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-C6s.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-C6s.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-CBC1.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-CBC1.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-CBTD1.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-CBTD1.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-D1A.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-D1A.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-L9.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-L9.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CC-BY license - applies to CET color map data.txt',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CC-BY '
   'license - applies to CET color map data.txt',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-L18.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-L18.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-L1.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-L1.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/viridis.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/viridis.csv',
   'DATA'),
  ('pyqtgraph/Qt/QtWidgets/__init__.pyi',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/Qt/QtWidgets/__init__.pyi',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-CBD1.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-CBD1.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-L3.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-L3.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/inferno.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/inferno.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-L4.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-L4.csv',
   'DATA'),
  ('pyqtgraph/icons/peegee/peegee_512px.png',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/icons/peegee/peegee_512px.png',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-L16.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-L16.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-L11.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-L11.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-L8.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-L8.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-C2.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-C2.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/magma.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/magma.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/plasma.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/plasma.csv',
   'DATA'),
  ('pyqtgraph/icons/default.png',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/icons/default.png',
   'DATA'),
  ('pyqtgraph/icons/peegee/peegee_192px.png',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/icons/peegee/peegee_192px.png',
   'DATA'),
  ('pyqtgraph/Qt/QtGui/__init__.pyi',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/Qt/QtGui/__init__.pyi',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-C3s.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-C3s.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-L19.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-L19.csv',
   'DATA'),
  ('pyqtgraph/colors/maps/CET-L17.csv',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/colors/maps/CET-L17.csv',
   'DATA'),
  ('pyqtgraph/icons/lock.png',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/icons/lock.png',
   'DATA'),
  ('pyqtgraph/Qt/QtSvg.pyi',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/pyqtgraph/Qt/QtSvg.pyi',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_he.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_he.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_es.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_es.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_ja.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_ja.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_nn.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_nn.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_ja.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_ja.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_lv.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_lv.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_tr.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_tr.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_pl.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_pl.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_zh_TW.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_fa.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_fa.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_it.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_it.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_nl.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_nl.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_ca.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_ca.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_lv.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_lv.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_hu.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_hu.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_da.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_da.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_zh_CN.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_gd.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_gd.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_zh_CN.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_zh_CN.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_hr.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_hr.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_cs.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_cs.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_pl.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_pl.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_he.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_he.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_en.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_en.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_cs.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_cs.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_en.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_en.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_it.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_it.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_sk.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_sk.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_gl.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_gl.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_pt_PT.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_pt_PT.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_es.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_es.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_pl.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_pl.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_nl.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_nl.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_ka.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_ka.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_uk.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_uk.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_lt.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_lt.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_nn.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_nn.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_sk.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_sk.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_de.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_de.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_da.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_da.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_it.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_it.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_bg.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_bg.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_de.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_de.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_gd.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_gd.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_uk.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_uk.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_fi.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_fi.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_bg.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_bg.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_ko.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_ko.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_hr.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_hr.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_fi.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_fi.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_ca.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_ca.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_pt_BR.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_pt_BR.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_ru.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_ru.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_hr.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_hr.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_ko.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_ko.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_nn.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_nn.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_nl.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_nl.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_tr.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_tr.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_en.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_en.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_hu.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_hu.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_fa.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_fa.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_de.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_de.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_es.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_es.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_pt_BR.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_pt_BR.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_bg.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_bg.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_zh_CN.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_zh_CN.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_fr.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_fr.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_zh_TW.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_zh_TW.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_uk.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_uk.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_sv.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_sv.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_fr.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_fr.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_ru.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_ru.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_ru.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_ru.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_zh_TW.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_ka.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_ka.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_pt_BR.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_pt_BR.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_ar.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_ar.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_fr.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_fr.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_tr.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_tr.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_ar.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_ar.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_sl.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_sl.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_cs.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_cs.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_ka.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_ka.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_ja.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_ja.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_da.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_da.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_ar.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_ar.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_sk.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qtbase_sk.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_sl.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_sl.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_ko.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_ko.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_hu.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_hu.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_ca.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_help_ca.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_gl.qm',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/translations/qt_gl.qm',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info/RECORD',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/MarkupSafe-3.0.2.dist-info/RECORD',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info/METADATA',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/MarkupSafe-3.0.2.dist-info/METADATA',
   'DATA'),
  ('wheel-0.45.1.dist-info/entry_points.txt',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/wheel-0.45.1.dist-info/entry_points.txt',
   'DATA'),
  ('wheel-0.45.1.dist-info/LICENSE.txt',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/wheel-0.45.1.dist-info/LICENSE.txt',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info/WHEEL',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/MarkupSafe-3.0.2.dist-info/WHEEL',
   'DATA'),
  ('wheel-0.45.1.dist-info/METADATA',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/wheel-0.45.1.dist-info/METADATA',
   'DATA'),
  ('wheel-0.45.1.dist-info/WHEEL',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/wheel-0.45.1.dist-info/WHEEL',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info/INSTALLER',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/MarkupSafe-3.0.2.dist-info/INSTALLER',
   'DATA'),
  ('wheel-0.45.1.dist-info/REQUESTED',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/wheel-0.45.1.dist-info/REQUESTED',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info/top_level.txt',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/MarkupSafe-3.0.2.dist-info/top_level.txt',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info/LICENSE.txt',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/MarkupSafe-3.0.2.dist-info/LICENSE.txt',
   'DATA'),
  ('wheel-0.45.1.dist-info/INSTALLER',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/wheel-0.45.1.dist-info/INSTALLER',
   'DATA'),
  ('wheel-0.45.1.dist-info/RECORD',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/wheel-0.45.1.dist-info/RECORD',
   'DATA'),
  ('QtCore', 'PyQt6/Qt6/lib/QtCore.framework/Versions/A/QtCore', 'SYMLINK'),
  ('QtGui', 'PyQt6/Qt6/lib/QtGui.framework/Versions/A/QtGui', 'SYMLINK'),
  ('QtWidgets',
   'PyQt6/Qt6/lib/QtWidgets.framework/Versions/A/QtWidgets',
   'SYMLINK'),
  ('QtPdf', 'PyQt6/Qt6/lib/QtPdf.framework/Versions/A/QtPdf', 'SYMLINK'),
  ('QtSvg', 'PyQt6/Qt6/lib/QtSvg.framework/Versions/A/QtSvg', 'SYMLINK'),
  ('QtNetwork',
   'PyQt6/Qt6/lib/QtNetwork.framework/Versions/A/QtNetwork',
   'SYMLINK'),
  ('QtTest', 'PyQt6/Qt6/lib/QtTest.framework/Versions/A/QtTest', 'SYMLINK'),
  ('QtOpenGL',
   'PyQt6/Qt6/lib/QtOpenGL.framework/Versions/A/QtOpenGL',
   'SYMLINK'),
  ('QtOpenGLWidgets',
   'PyQt6/Qt6/lib/QtOpenGLWidgets.framework/Versions/A/QtOpenGLWidgets',
   'SYMLINK'),
  ('QtDBus', 'PyQt6/Qt6/lib/QtDBus.framework/Versions/A/QtDBus', 'SYMLINK'),
  ('PyQt6/Qt6/lib/QtCore.framework/QtCore',
   'Versions/Current/QtCore',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtCore.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtCore.framework/Versions/A/Resources/Info.plist',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/lib/QtCore.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt6/Qt6/lib/QtCore.framework/Versions/Current', 'A', 'SYMLINK'),
  ('PyQt6/Qt6/lib/QtDBus.framework/QtDBus',
   'Versions/Current/QtDBus',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtDBus.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtDBus.framework/Versions/A/Resources/Info.plist',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/lib/QtDBus.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt6/Qt6/lib/QtDBus.framework/Versions/Current', 'A', 'SYMLINK'),
  ('PyQt6/Qt6/lib/QtGui.framework/QtGui', 'Versions/Current/QtGui', 'SYMLINK'),
  ('PyQt6/Qt6/lib/QtGui.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtGui.framework/Versions/A/Resources/Info.plist',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/lib/QtGui.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt6/Qt6/lib/QtGui.framework/Versions/Current', 'A', 'SYMLINK'),
  ('PyQt6/Qt6/lib/QtNetwork.framework/QtNetwork',
   'Versions/Current/QtNetwork',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtNetwork.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtNetwork.framework/Versions/A/Resources/Info.plist',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/lib/QtNetwork.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt6/Qt6/lib/QtNetwork.framework/Versions/Current', 'A', 'SYMLINK'),
  ('PyQt6/Qt6/lib/QtOpenGL.framework/QtOpenGL',
   'Versions/Current/QtOpenGL',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtOpenGL.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtOpenGL.framework/Versions/A/Resources/Info.plist',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/lib/QtOpenGL.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt6/Qt6/lib/QtOpenGL.framework/Versions/Current', 'A', 'SYMLINK'),
  ('PyQt6/Qt6/lib/QtOpenGLWidgets.framework/QtOpenGLWidgets',
   'Versions/Current/QtOpenGLWidgets',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtOpenGLWidgets.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtOpenGLWidgets.framework/Versions/A/Resources/Info.plist',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/lib/QtOpenGLWidgets.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt6/Qt6/lib/QtOpenGLWidgets.framework/Versions/Current', 'A', 'SYMLINK'),
  ('PyQt6/Qt6/lib/QtPdf.framework/QtPdf', 'Versions/Current/QtPdf', 'SYMLINK'),
  ('PyQt6/Qt6/lib/QtPdf.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtPdf.framework/Versions/A/Resources/Info.plist',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/lib/QtPdf.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt6/Qt6/lib/QtPdf.framework/Versions/Current', 'A', 'SYMLINK'),
  ('PyQt6/Qt6/lib/QtSvg.framework/QtSvg', 'Versions/Current/QtSvg', 'SYMLINK'),
  ('PyQt6/Qt6/lib/QtSvg.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtSvg.framework/Versions/A/Resources/Info.plist',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/lib/QtSvg.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt6/Qt6/lib/QtSvg.framework/Versions/Current', 'A', 'SYMLINK'),
  ('PyQt6/Qt6/lib/QtTest.framework/QtTest',
   'Versions/Current/QtTest',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtTest.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtTest.framework/Versions/A/Resources/Info.plist',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/lib/QtTest.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt6/Qt6/lib/QtTest.framework/Versions/Current', 'A', 'SYMLINK'),
  ('PyQt6/Qt6/lib/QtWidgets.framework/QtWidgets',
   'Versions/Current/QtWidgets',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtWidgets.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtWidgets.framework/Versions/A/Resources/Info.plist',
   '/Users/<USER>/miniconda3/lib/python3.12/site-packages/PyQt6/Qt6/lib/QtWidgets.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt6/Qt6/lib/QtWidgets.framework/Versions/Current', 'A', 'SYMLINK')],
 'libpython3.12.dylib',
 False,
 False,
 False,
 [],
 'arm64',
 None,
 None)
