# G-VoTest - 音频测量和麦克风校准软件
# Requirements file for pip installation

# Core GUI Framework
PyQt6==6.7.1
PyQt6-Qt6==6.7.3
PyQt6_sip==13.8.0

# Scientific Computing and Audio Processing
numpy==2.0.1
sounddevice==0.5.1

# Data Visualization
pyqtgraph==0.13.7

# Async Event Handling
eventkit==1.0.3

# Optional: High-performance computing (recommended for better performance)
numba==0.60.0

# Standard Library Dependencies (usually included with Python)
# These are listed for completeness but typically don't need to be installed separately:
# - asyncio (Python 3.4+)
# - array (built-in)
# - collections (built-in)
# - copy (built-in)
# - datetime (built-in)
# - functools (built-in)
# - json (built-in)
# - logging (built-in)
# - os (built-in)
# - pathlib (Python 3.4+)
# - pickle (built-in)
# - signal (built-in)
# - sys (built-in)
# - threading (built-in)
# - time (built-in)
# - typing (Python 3.5+)
# - wave (built-in)

# Development and Testing Dependencies (optional)
# Uncomment if needed for development:
# pytest>=7.0.0
# pytest-qt>=4.0.0
# black>=22.0.0
# flake8>=4.0.0
