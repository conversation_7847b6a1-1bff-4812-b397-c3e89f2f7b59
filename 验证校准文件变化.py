#!/usr/bin/env python3
"""
验证校准文件变化

这个脚本用于验证每次校准生成的文件是否真的不同
"""

import sys
import time
import random
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

def simulate_multiple_calibrations():
    """模拟多次校准过程"""
    
    print("=== 模拟多次校准过程 ===\n")
    
    calibration_files = []
    
    for i in range(3):
        print(f"第 {i+1} 次校准:")
        
        # 模拟校准参数
        target_db = 94
        base_measured_db = 81.5
        
        # 添加随机变化模拟真实测量
        random_variation = random.uniform(-2.0, 2.0)
        simulated_measured_db = base_measured_db + random_variation
        calibration_offset = target_db - simulated_measured_db
        
        print(f"  目标电平: {target_db} dB")
        print(f"  测量电平: {simulated_measured_db:.2f} dB")
        print(f"  校准偏移: {calibration_offset:.2f} dB")
        
        # 创建校准数据
        calibration_data = [
            (20, calibration_offset),
            (1000, calibration_offset),
            (20000, calibration_offset)
        ]
        
        # 生成文件名
        import datetime as dt
        timestamp = dt.datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'test_calibration_{i+1}_{timestamp}.txt'
        
        # 保存校准文件（模拟详细保存）
        save_detailed_calibration(filename, calibration_data, target_db, simulated_measured_db, calibration_offset)
        
        calibration_files.append({
            'filename': filename,
            'target_db': target_db,
            'measured_db': simulated_measured_db,
            'offset': calibration_offset,
            'data': calibration_data
        })
        
        print(f"  保存文件: {filename}")
        print()
        
        # 短暂延迟确保时间戳不同
        time.sleep(1)
    
    return calibration_files

def save_detailed_calibration(path, calibration_data, target_db, measured_db, calibration_offset):
    """保存详细的校准文件"""
    
    import datetime as dt
    timestamp = dt.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    content = f"""# 麦克风校准文件
# 生成时间: {timestamp}
# 校准器设置: 1000Hz, {target_db}dB
# 测量电平: {measured_db:.2f}dB
# 校准偏移: {calibration_offset:.2f}dB
# 
# 格式: 频率(Hz) 校准偏移(dB)
"""
    
    # 添加校准数据
    for freq, offset in calibration_data:
        content += f"{freq} {offset:.6f}\n"
    
    # 写入文件
    with open(path, 'w', encoding='utf-8') as f:
        f.write(content)

def compare_calibration_files(calibration_files):
    """比较校准文件的差异"""
    
    print("=== 校准文件比较 ===\n")
    
    print("校准结果对比:")
    print("文件\t\t目标dB\t测量dB\t偏移dB\t校准值")
    print("-" * 60)
    
    for i, cal in enumerate(calibration_files):
        offset_value = cal['data'][1][1]  # 1000Hz处的偏移值
        print(f"文件{i+1}\t\t{cal['target_db']}\t{cal['measured_db']:.2f}\t{cal['offset']:.2f}\t{offset_value:.6f}")
    
    print()
    
    # 检查是否有差异
    offsets = [cal['offset'] for cal in calibration_files]
    if len(set(f"{offset:.6f}" for offset in offsets)) == 1:
        print("⚠️  警告: 所有校准文件的偏移值完全相同!")
        print("这表明校准算法可能有问题，没有反映真实的测量变化。")
    else:
        print("✅ 校准文件显示了不同的偏移值，这是正常的。")
        print("每次测量的微小差异导致了不同的校准结果。")
    
    print()
    
    # 显示文件内容差异
    print("文件内容预览:")
    for i, cal in enumerate(calibration_files):
        print(f"\n文件 {i+1} ({cal['filename']}):")
        if Path(cal['filename']).exists():
            with open(cal['filename'], 'r', encoding='utf-8') as f:
                lines = f.readlines()
                # 显示前几行和数据行
                for j, line in enumerate(lines):
                    if j < 7 or not line.startswith('#'):
                        print(f"  {line.rstrip()}")
        else:
            print("  文件不存在")

def cleanup_test_files(calibration_files):
    """清理测试文件"""
    
    print("\n=== 清理测试文件 ===\n")
    
    for cal in calibration_files:
        filename = cal['filename']
        if Path(filename).exists():
            Path(filename).unlink()
            print(f"删除: {filename}")
        else:
            print(f"文件不存在: {filename}")

def analyze_problem():
    """分析问题原因"""
    
    print("\n=== 问题分析 ===\n")
    
    print("如果您的校准文件完全相同，可能的原因:")
    print()
    print("1. **使用测试校准功能**:")
    print("   - 修复前: 测试校准总是使用固定值 81.5dB")
    print("   - 修复后: 测试校准现在会添加随机变化")
    print()
    print("2. **真实测量环境稳定**:")
    print("   - 如果使用真实校准器，环境非常稳定")
    print("   - 麦克风和校准器位置没有变化")
    print("   - 这种情况下，微小差异是正常的")
    print()
    print("3. **校准器精度高**:")
    print("   - 高质量校准器输出非常稳定")
    print("   - 连续测量结果应该非常接近")
    print("   - 差异可能在小数点后几位")
    print()
    print("建议:")
    print("- 使用修复后的测试校准功能验证算法")
    print("- 检查真实测量时的环境变化")
    print("- 查看校准文件的详细数值（小数点后6位）")

def main():
    """主函数"""
    
    print("验证校准文件变化\n")
    
    # 模拟多次校准
    calibration_files = simulate_multiple_calibrations()
    
    # 比较文件差异
    compare_calibration_files(calibration_files)
    
    # 分析问题
    analyze_problem()
    
    # 清理测试文件
    cleanup_test_files(calibration_files)
    
    print("\n=== 验证完成 ===")
    print("\n现在您可以:")
    print("1. 运行 app1.py 测试修复后的校准功能")
    print("2. 使用'测试校准'按钮验证每次结果不同")
    print("3. 检查保存的校准文件是否包含详细信息")

if __name__ == "__main__":
    main()
