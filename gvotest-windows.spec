# -*- mode: python ; coding: utf-8 -*-
"""
G-VoTest Windows 打包配置文件
用于创建Windows可执行文件
"""

import sys
from pathlib import Path

# 项目信息
APP_NAME = 'G-VoTest'
APP_VERSION = '1.0.0'
APP_DESCRIPTION = '音频测量和麦克风校准软件'

# 获取项目根目录
project_root = Path.cwd()

# 数据文件列表
datas = [
    # 图标和资源文件
    ('logo.png', '.'),
    ('logo.ico', '.'),
    ('start.png', '.'),
    
    # 配置文件
    ('preset.json', '.'),
    
    # 文档文件
    ('麦克风校准使用说明.md', 'docs'),
    ('真实麦克风校准指南.md', 'docs'),
    ('INSTALL.md', 'docs'),
]

# 隐藏导入 - PyInstaller可能无法自动检测的模块
hiddenimports = [
    # PyQt6相关
    'PyQt6.QtCore',
    'PyQt6.QtGui', 
    'PyQt6.QtWidgets',
    'PyQt6.sip',
    
    # 音频处理
    'sounddevice',
    'sounddevice._internal',
    
    # 数值计算
    'numpy',
    'numpy.fft',
    'numpy.random',
    
    # 图形界面
    'pyqtgraph',
    'pyqtgraph.graphicsItems',
    'pyqtgraph.widgets',
    
    # 异步处理
    'eventkit',
    
    # 可选的高性能模块
    'numba',
    'numba.core',
    
    # 项目内部模块
    'gvotest',
    'gvotest.analyzer',
    'gvotest.audio',
    'gvotest.io_',
    'analyzer',
    'audio',
    'io_',
]

# 排除的模块 - 减少打包大小
excludes = [
    # 开发工具
    'pytest',
    'black',
    'flake8',
    'mypy',
    
    # 文档工具
    'sphinx',
    'docutils',
    
    # 不需要的GUI工具包
    'tkinter',
    'matplotlib.backends._backend_tk',
    
    # 不需要的科学计算包
    'scipy',
    'pandas',
    'matplotlib',
    
    # 其他不需要的包
    'IPython',
    'jupyter',
]

# 二进制文件
binaries = []

# 分析配置
a = Analysis(
    ['app1.py'],  # 主程序文件
    pathex=[str(project_root)],  # 搜索路径
    binaries=binaries,
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    noarchive=False,
    optimize=0,
)

# 创建PYZ文件
pyz = PYZ(a.pure, a.zipped_data, cipher=None)

# 创建可执行文件
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name=APP_NAME,
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,  # 使用UPX压缩
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 不显示控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='logo.ico',  # 应用程序图标
    version='version_info.txt',  # 版本信息文件
)

# Windows特定配置
if sys.platform == 'win32':
    # 可以添加版本信息
    exe.version = APP_VERSION
    exe.description = APP_DESCRIPTION
