#!/usr/bin/env python3
"""
验证 write_correction 修复是否有效
"""

import sys
from pathlib import Path

def test_direct_import():
    """测试直接导入 write_correction"""
    
    print("=== 测试直接导入 write_correction ===\n")
    
    try:
        # 直接从 io_ 模块导入
        from io_ import write_correction, read_correction
        print("✅ 成功直接导入 write_correction 和 read_correction")
        
        # 创建测试数据
        test_calibration = [
            (20, 12.5),
            (1000, 12.5),
            (20000, 12.5)
        ]
        
        # 测试保存
        test_file = "test_calibration_direct.txt"
        write_correction(test_file, test_calibration)
        print(f"✅ 成功保存测试文件: {test_file}")
        
        # 验证文件内容
        if Path(test_file).exists():
            with open(test_file, 'r') as f:
                content = f.read()
                print(f"📄 文件内容:\n{content}")
            
            # 测试读取
            loaded_data = read_correction(test_file)
            print(f"✅ 成功读取数据: {loaded_data}")
            
            # 验证数据一致性
            if loaded_data == test_calibration:
                print("✅ 数据完全一致")
            else:
                print("⚠️ 数据不一致")
                print(f"原始: {test_calibration}")
                print(f"读取: {loaded_data}")
            
            # 清理
            Path(test_file).unlink()
            print("🧹 清理测试文件")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def simulate_app_usage():
    """模拟 app1.py 中的使用方式"""
    
    print("\n=== 模拟 app1.py 使用方式 ===\n")
    
    try:
        # 模拟 app1.py 中的导入
        from io_ import write_correction
        import datetime as dt
        from pathlib import Path
        
        print("✅ 模拟导入成功")
        
        # 模拟校准数据
        calibration_target_db = 94
        calibration_result = [
            (20, 12.42),
            (1000, 12.42),
            (20000, 12.42)
        ]
        
        # 模拟文件名生成
        timestamp = dt.datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'mic_calibration_1000Hz_{calibration_target_db}dB_{timestamp}.txt'
        
        print(f"生成文件名: {filename}")
        
        # 模拟保存过程
        write_correction(filename, calibration_result)
        print(f"✅ 保存成功: {filename}")
        
        # 验证文件
        if Path(filename).exists():
            print("✅ 文件确实存在")
            
            with open(filename, 'r') as f:
                content = f.read()
                print(f"📄 文件内容:\n{content}")
            
            # 清理
            Path(filename).unlink()
            print("🧹 清理完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 模拟测试失败: {e}")
        return False

def create_usage_guide():
    """创建使用指南"""
    
    print("\n=== 使用指南 ===\n")
    
    print("修复说明:")
    print("1. 在 __init__.py 中添加了 write_correction 的导入")
    print("2. 在 app1.py 中添加了直接导入: from io_ import write_correction")
    print("3. 修改了保存代码，使用直接导入的函数而不是 hifi.write_correction")
    print()
    
    print("现在的保存代码:")
    print("```python")
    print("# 直接使用导入的函数")
    print("write_correction(path, self.calibration_result)")
    print("```")
    print()
    
    print("而不是之前的:")
    print("```python")
    print("# 这会导致错误")
    print("hifi.write_correction(path, self.calibration_result)")
    print("```")
    print()
    
    print("测试步骤:")
    print("1. 运行 app1.py")
    print("2. 打开校准对话框")
    print("3. 点击 '直接启用保存' 按钮")
    print("4. 点击 '保存校准' 按钮")
    print("5. 选择保存位置")
    print("6. 应该能够成功保存文件")

def main():
    """主函数"""
    
    print("验证 write_correction 修复\n")
    
    # 测试直接导入
    test1_passed = test_direct_import()
    
    # 模拟应用使用
    test2_passed = simulate_app_usage()
    
    # 显示结果
    print(f"\n=== 测试结果 ===")
    print(f"直接导入测试: {'✅ 通过' if test1_passed else '❌ 失败'}")
    print(f"应用模拟测试: {'✅ 通过' if test2_passed else '❌ 失败'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 所有测试通过！write_correction 修复成功！")
        print("现在您可以在 app1.py 中正常保存校准文件了。")
    else:
        print("\n❌ 部分测试失败，可能需要进一步调试。")
    
    # 显示使用指南
    create_usage_guide()

if __name__ == "__main__":
    main()
