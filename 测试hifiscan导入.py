#!/usr/bin/env python3
"""
测试hifiscan模块导入

验证打包时hifiscan模块能否正确导入
"""

import sys
from pathlib import Path

def test_hifiscan_import():
    """测试hifiscan导入"""
    
    print("🔍 测试hifiscan模块导入...")
    
    try:
        # 测试主要导入
        print("1. 测试 import hifiscan as hifi")
        import hifiscan as hifi
        print("   ✅ 成功")
        
        # 测试子模块导入
        print("2. 测试子模块:")
        
        try:
            from hifiscan import Analyzer
            print("   ✅ Analyzer 导入成功")
        except Exception as e:
            print(f"   ❌ Analyzer 导入失败: {e}")
        
        try:
            from hifiscan import Audio
            print("   ✅ Audio 导入成功")
        except Exception as e:
            print(f"   ❌ Audio 导入失败: {e}")
        
        try:
            from hifiscan import Sound, read_correction, write_correction
            print("   ✅ IO 函数导入成功")
        except Exception as e:
            print(f"   ❌ IO 函数导入失败: {e}")
        
        # 测试直接模块导入
        print("3. 测试直接模块导入:")
        
        try:
            import analyzer
            print("   ✅ analyzer 模块导入成功")
        except Exception as e:
            print(f"   ❌ analyzer 模块导入失败: {e}")
        
        try:
            import audio
            print("   ✅ audio 模块导入成功")
        except Exception as e:
            print(f"   ❌ audio 模块导入失败: {e}")
        
        try:
            import io_
            print("   ✅ io_ 模块导入成功")
        except Exception as e:
            print(f"   ❌ io_ 模块导入失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ hifiscan 主模块导入失败: {e}")
        print(f"错误类型: {type(e).__name__}")
        
        # 显示模块搜索路径
        print("\n📁 Python 模块搜索路径:")
        for i, path in enumerate(sys.path):
            print(f"   {i+1}. {path}")
        
        # 检查当前目录文件
        print("\n📋 当前目录文件:")
        current_dir = Path('.')
        python_files = list(current_dir.glob('*.py'))
        for file in sorted(python_files):
            print(f"   - {file.name}")
        
        return False

def test_app1_imports():
    """测试app1.py中的导入"""
    
    print("\n🔍 测试app1.py中的导入...")
    
    try:
        # 模拟app1.py中的导入
        print("1. 测试 import hifiscan as hifi")
        import hifiscan as hifi
        print("   ✅ 成功")
        
        print("2. 测试 from io_ import write_correction")
        from io_ import write_correction
        print("   ✅ 成功")
        
        # 测试实际使用
        print("3. 测试模块使用:")
        
        # 测试Audio类
        try:
            audio_class = hifi.Audio
            print("   ✅ hifi.Audio 可访问")
        except Exception as e:
            print(f"   ❌ hifi.Audio 访问失败: {e}")
        
        # 测试Analyzer类
        try:
            analyzer_class = hifi.Analyzer
            print("   ✅ hifi.Analyzer 可访问")
        except Exception as e:
            print(f"   ❌ hifi.Analyzer 访问失败: {e}")
        
        # 测试write_correction函数
        try:
            write_func = write_correction
            print("   ✅ write_correction 函数可访问")
        except Exception as e:
            print(f"   ❌ write_correction 函数访问失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ app1.py 导入测试失败: {e}")
        return False

def show_module_info():
    """显示模块信息"""
    
    print("\n📊 模块信息:")
    
    try:
        import hifiscan
        print(f"hifiscan 模块位置: {hifiscan.__file__ if hasattr(hifiscan, '__file__') else '内置模块'}")
        
        # 显示hifiscan模块的属性
        attrs = [attr for attr in dir(hifiscan) if not attr.startswith('_')]
        print(f"hifiscan 可用属性: {', '.join(attrs)}")
        
    except Exception as e:
        print(f"无法获取hifiscan模块信息: {e}")

def main():
    """主函数"""
    
    print("=" * 50)
    print("HiFiScan 模块导入测试")
    print("=" * 50)
    
    # 测试hifiscan导入
    test1_passed = test_hifiscan_import()
    
    # 测试app1导入
    test2_passed = test_app1_imports()
    
    # 显示模块信息
    if test1_passed:
        show_module_info()
    
    print(f"\n=== 测试结果 ===")
    print(f"hifiscan 导入测试: {'✅ 通过' if test1_passed else '❌ 失败'}")
    print(f"app1.py 导入测试: {'✅ 通过' if test2_passed else '❌ 失败'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 所有导入测试通过！")
        print("现在可以重新打包，应该不会出现 'No module named hifiscan' 错误。")
    else:
        print("\n❌ 部分测试失败，需要进一步修复。")

if __name__ == "__main__":
    main()
