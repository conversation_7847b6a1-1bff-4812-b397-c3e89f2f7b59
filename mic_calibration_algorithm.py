#!/usr/bin/env python3
"""
麦克风校准算法说明和独立测试模块

使用1000Hz声校准器（94dB和114dB两档）校准麦克风的算法实现
"""

import numpy as np
import matplotlib.pyplot as plt
from typing import List, Tuple, Optional

class MicrophoneCalibrator:
    """麦克风校准器类"""
    
    def __init__(self, sample_rate: int = 48000):
        self.sample_rate = sample_rate
        self.target_frequency = 1000.0  # 校准器频率
        self.frequency_tolerance = 50.0  # ±50Hz容差
        
    def analyze_calibration_signal(self, audio_data: np.ndarray, 
                                 target_db: float) -> Tuple[float, float]:
        """
        分析校准信号并计算校准偏移
        
        Args:
            audio_data: 录制的音频数据
            target_db: 目标dB电平 (94 或 114)
            
        Returns:
            (measured_db, calibration_offset): 测量电平和校准偏移
        """
        
        # 1. 使用FFT分析频谱
        fft_data = np.fft.fft(audio_data)
        freqs = np.fft.fftfreq(len(audio_data), 1/self.sample_rate)
        
        # 2. 找到1000Hz附近的频率成分
        freq_mask = (np.abs(freqs - self.target_frequency) <= self.frequency_tolerance) & (freqs > 0)
        
        if not np.any(freq_mask):
            raise ValueError(f"未找到{self.target_frequency}Hz信号")
        
        # 3. 计算目标频率的幅度
        target_amplitude = np.mean(np.abs(fft_data[freq_mask]))
        
        if target_amplitude <= 0:
            raise ValueError("测量到的信号幅度为零")
        
        # 4. 转换为dB (相对于满量程)
        measured_db = 20 * np.log10(target_amplitude)
        
        # 5. 计算校准偏移
        calibration_offset = target_db - measured_db
        
        return measured_db, calibration_offset
    
    def create_calibration_curve(self, calibration_offset: float, 
                               frequency_range: Optional[List[float]] = None) -> List[Tuple[float, float]]:
        """
        创建校准曲线
        
        Args:
            calibration_offset: 在1000Hz处的校准偏移
            frequency_range: 频率范围，默认为[20, 1000, 20000]
            
        Returns:
            校准曲线数据 [(频率, dB偏移), ...]
        """
        
        if frequency_range is None:
            frequency_range = [20, 1000, 20000]
        
        # 创建简单的平坦校准曲线
        # 在实际应用中，可能需要更复杂的插值
        calibration_curve = []
        for freq in frequency_range:
            calibration_curve.append((freq, calibration_offset))
        
        return calibration_curve
    
    def validate_calibration(self, audio_data: np.ndarray, 
                           calibration_curve: List[Tuple[float, float]],
                           target_db: float) -> bool:
        """
        验证校准结果
        
        Args:
            audio_data: 验证用的音频数据
            calibration_curve: 校准曲线
            target_db: 目标dB电平
            
        Returns:
            校准是否有效
        """
        
        try:
            # 应用校准并重新测量
            measured_db, _ = self.analyze_calibration_signal(audio_data, target_db)
            
            # 从校准曲线中获取1000Hz的偏移
            calibration_offset = 0
            for freq, offset in calibration_curve:
                if abs(freq - 1000) < 100:  # 找到最接近1000Hz的点
                    calibration_offset = offset
                    break
            
            # 应用校准偏移
            calibrated_db = measured_db + calibration_offset
            
            # 检查误差是否在可接受范围内 (±1dB)
            error = abs(calibrated_db - target_db)
            return error <= 1.0
            
        except Exception:
            return False

def generate_test_signal(frequency: float = 1000.0, 
                        amplitude_db: float = 94.0,
                        duration: float = 3.0,
                        sample_rate: int = 48000,
                        noise_level: float = 0.01) -> np.ndarray:
    """
    生成测试用的1000Hz校准信号
    
    Args:
        frequency: 信号频率
        amplitude_db: 信号幅度 (dB)
        duration: 信号时长 (秒)
        sample_rate: 采样率
        noise_level: 噪声水平
        
    Returns:
        生成的音频信号
    """
    
    t = np.linspace(0, duration, int(duration * sample_rate), False)
    
    # 将dB转换为线性幅度 (假设0dB = 1.0)
    amplitude = 10 ** (amplitude_db / 20) / 1000  # 缩放到合理范围
    
    # 生成1000Hz正弦波
    signal = amplitude * np.sin(2 * np.pi * frequency * t)
    
    # 添加少量噪声
    noise = noise_level * np.random.randn(len(signal))
    signal += noise
    
    return signal

def test_calibration_algorithm():
    """测试校准算法"""
    
    print("=== 麦克风校准算法测试 ===\n")
    
    # 创建校准器实例
    calibrator = MicrophoneCalibrator()
    
    # 测试94dB校准
    print("1. 测试94dB校准:")
    test_signal_94 = generate_test_signal(amplitude_db=94.0)
    
    try:
        measured_db, offset = calibrator.analyze_calibration_signal(test_signal_94, 94.0)
        print(f"   目标电平: 94.0 dB")
        print(f"   测量电平: {measured_db:.2f} dB")
        print(f"   校准偏移: {offset:.2f} dB")
        
        # 创建校准曲线
        calibration_curve = calibrator.create_calibration_curve(offset)
        print(f"   校准曲线: {calibration_curve}")
        
        # 验证校准
        is_valid = calibrator.validate_calibration(test_signal_94, calibration_curve, 94.0)
        print(f"   校准验证: {'通过' if is_valid else '失败'}")
        
    except Exception as e:
        print(f"   错误: {e}")
    
    print()
    
    # 测试114dB校准
    print("2. 测试114dB校准:")
    test_signal_114 = generate_test_signal(amplitude_db=114.0)
    
    try:
        measured_db, offset = calibrator.analyze_calibration_signal(test_signal_114, 114.0)
        print(f"   目标电平: 114.0 dB")
        print(f"   测量电平: {measured_db:.2f} dB")
        print(f"   校准偏移: {offset:.2f} dB")
        
        calibration_curve = calibrator.create_calibration_curve(offset)
        print(f"   校准曲线: {calibration_curve}")
        
        is_valid = calibrator.validate_calibration(test_signal_114, calibration_curve, 114.0)
        print(f"   校准验证: {'通过' if is_valid else '失败'}")
        
    except Exception as e:
        print(f"   错误: {e}")
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    test_calibration_algorithm()
