# G-VoTest Windows 打包说明

## 📦 快速打包

### 方法1: 使用自动化脚本（推荐）
```bash
# 运行打包脚本
python build_windows.py

# 或者使用批处理文件
build.bat
```

### 方法2: 手动打包
```bash
# 安装PyInstaller
pip install pyinstaller

# 使用spec文件打包
pyinstaller --clean --noconfirm hifiscan-windows.spec
```

## 🔧 环境准备

### 1. 安装依赖
```bash
# 安装所有依赖
pip install -r requirements.txt

# 安装打包工具
pip install pyinstaller
```

### 2. 检查必要文件
确保以下文件存在：
- `app1.py` - 主程序
- `logo.ico` - 应用图标
- `hifiscan-windows.spec` - 打包配置
- `version_info.txt` - 版本信息

## 📋 打包配置说明

### spec文件配置
`hifiscan-windows.spec` 包含以下配置：

#### 数据文件
- 图标文件: `logo.png`, `logo.ico`
- 配置文件: `preset.json`
- 文档文件: 各种说明文档

#### 隐藏导入
自动包含可能被遗漏的模块：
- PyQt6相关模块
- 音频处理模块
- 数值计算模块
- 项目内部模块

#### 排除模块
减少打包大小，排除不需要的模块：
- 开发工具 (pytest, black等)
- 文档工具 (sphinx等)
- 不需要的GUI工具包

## 🎯 打包结果

### 输出文件
打包完成后，在 `dist` 目录中会生成：
- `G-VoTest.exe` - 主可执行文件
- 各种DLL文件 - 运行时依赖

### 文件大小
- 预期大小: 80-150 MB
- 包含所有必要的依赖库
- 可以在没有Python环境的Windows机器上运行

## 🚀 分发方式

### 方法1: 直接分发
1. 将整个 `dist` 目录打包为ZIP文件
2. 用户解压后直接运行 `HiFiScan.exe`

### 方法2: 创建安装程序
1. 安装 NSIS: https://nsis.sourceforge.io/
2. 运行: `makensis hifiscan-installer.nsi`
3. 生成专业的安装程序 `HiFiScan-Setup.exe`

## 🔍 测试建议

### 本地测试
1. 在开发机器上运行 `dist/HiFiScan.exe`
2. 测试所有主要功能
3. 检查音频设备访问权限

### 目标机器测试
1. 在干净的Windows机器上测试
2. 确保没有安装Python或相关依赖
3. 测试音频功能和麦克风校准

## ⚠️ 常见问题

### 1. 打包失败
**问题**: PyInstaller报错
**解决**:
```bash
# 清理缓存
pyinstaller --clean hifiscan-windows.spec

# 更新PyInstaller
pip install --upgrade pyinstaller
```

### 2. 缺少模块
**问题**: 运行时提示缺少模块
**解决**: 在spec文件的 `hiddenimports` 中添加缺少的模块

### 3. 文件过大
**问题**: 可执行文件太大
**解决**: 在spec文件的 `excludes` 中添加不需要的模块

### 4. 音频设备问题
**问题**: 在目标机器上无法访问音频设备
**解决**: 
- 检查Windows音频驱动
- 确认麦克风权限设置
- 安装Microsoft Visual C++ Redistributable

## 🛠️ 高级配置

### UPX压缩
默认启用UPX压缩以减小文件大小：
```python
upx=True,  # 启用压缩
```

### 控制台窗口
默认不显示控制台窗口：
```python
console=False,  # 不显示控制台
```

如需调试，可以临时设置为 `True`。

### 版本信息
包含详细的版本信息：
- 文件版本: *******
- 产品名称: HiFiScan
- 公司名称: HiFiScan Project
- 版权信息: Copyright © 2024

## 📊 性能优化

### 启动时间优化
- 使用 `--onefile` 创建单文件（可选）
- 排除不必要的模块
- 使用UPX压缩

### 运行时优化
- 包含numba以提高数值计算性能
- 优化音频缓冲区设置
- 减少内存使用

## 🔐 代码签名（可选）

对于商业分发，建议进行代码签名：
```bash
# 使用signtool.exe签名
signtool sign /f certificate.pfx /p password /t http://timestamp.server dist/HiFiScan.exe
```

## 📝 发布清单

打包完成后的检查清单：
- [ ] 可执行文件正常启动
- [ ] 所有菜单功能正常
- [ ] 音频录制功能正常
- [ ] 麦克风校准功能正常
- [ ] 文件保存和加载正常
- [ ] 在干净系统上测试通过
- [ ] 文件大小合理
- [ ] 包含必要的文档和说明

## 🎉 完成

按照以上步骤，您就可以创建一个完整的Windows可执行文件，可以在任何Windows 10+系统上运行，无需安装Python或其他依赖。
