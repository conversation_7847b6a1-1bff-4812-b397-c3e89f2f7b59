#!/usr/bin/env python3
"""
Windows 打包脚本
自动化创建 Windows 可执行文件的过程
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def check_requirements():
    """检查打包所需的依赖"""
    print("🔍 检查打包环境...")
    
    # 检查PyInstaller
    try:
        import PyInstaller
        print(f"✅ PyInstaller 版本: {PyInstaller.__version__}")
    except ImportError:
        print("❌ PyInstaller 未安装")
        print("请运行: pip install pyinstaller")
        return False
    
    # 检查主要依赖
    required_packages = ['PyQt6', 'numpy', 'sounddevice', 'pyqtgraph', 'eventkit']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package}")
    
    if missing_packages:
        print(f"\n缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements.txt")
        return False
    
    # 检查必要文件
    required_files = ['app1.py', 'logo.ico', 'hifiscan-windows.spec']
    for file in required_files:
        if not Path(file).exists():
            print(f"❌ 缺少文件: {file}")
            return False
        else:
            print(f"✅ {file}")
    
    return True

def clean_build():
    """清理之前的构建文件"""
    print("\n🧹 清理构建目录...")
    
    dirs_to_clean = ['build', 'dist/__pycache__']
    files_to_clean = ['dist/HiFiScan.exe']
    
    for dir_path in dirs_to_clean:
        if Path(dir_path).exists():
            shutil.rmtree(dir_path)
            print(f"✅ 删除目录: {dir_path}")
    
    for file_path in files_to_clean:
        if Path(file_path).exists():
            os.remove(file_path)
            print(f"✅ 删除文件: {file_path}")

def build_executable():
    """构建可执行文件"""
    print("\n🔨 开始构建 Windows 可执行文件...")
    
    try:
        # 运行PyInstaller
        cmd = [
            sys.executable, '-m', 'PyInstaller',
            '--clean',  # 清理临时文件
            '--noconfirm',  # 不询问覆盖
            'hifiscan-windows.spec'
        ]
        
        print(f"执行命令: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 构建成功!")
            return True
        else:
            print("❌ 构建失败!")
            print("错误输出:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 构建过程出错: {e}")
        return False

def verify_build():
    """验证构建结果"""
    print("\n🔍 验证构建结果...")

    # 检查不同平台的可执行文件
    possible_paths = [
        Path('dist/HiFiScan.exe'),  # Windows
        Path('dist/HiFiScan'),     # Linux/macOS
        Path('dist/HiFiScan.app'), # macOS app bundle
    ]

    exe_path = None
    for path in possible_paths:
        if path.exists():
            exe_path = path
            break

    if exe_path:
        if exe_path.is_file():
            size_mb = exe_path.stat().st_size / (1024 * 1024)
            print(f"✅ 可执行文件已创建: {exe_path}")
            print(f"📦 文件大小: {size_mb:.1f} MB")
        else:
            print(f"✅ 应用程序包已创建: {exe_path}")

        # 检查dist目录内容
        dist_files = list(Path('dist').glob('*'))
        print(f"📁 dist目录包含 {len(dist_files)} 个文件/目录")

        # 显示主要文件
        print("📋 主要文件:")
        for file in sorted(dist_files):
            if file.name.startswith('HiFiScan'):
                if file.is_file():
                    size_mb = file.stat().st_size / (1024 * 1024)
                    print(f"   - {file.name} ({size_mb:.1f} MB)")
                else:
                    print(f"   - {file.name}/ (目录)")

        return True
    else:
        print("❌ 可执行文件未找到")
        print("📁 dist目录内容:")
        dist_files = list(Path('dist').glob('*'))
        for file in sorted(dist_files):
            print(f"   - {file.name}")
        return False

def create_installer_script():
    """创建安装程序脚本"""
    print("\n📝 创建安装程序脚本...")
    
    nsis_script = """
; HiFiScan Windows 安装程序脚本
; 使用 NSIS (Nullsoft Scriptable Install System) 创建

!define APP_NAME "HiFiScan"
!define APP_VERSION "1.0.0"
!define APP_PUBLISHER "HiFiScan Project"
!define APP_URL "https://github.com/hifiscan/hifiscan"
!define APP_EXE "HiFiScan.exe"

; 安装程序设置
Name "${APP_NAME}"
OutFile "HiFiScan-Setup.exe"
InstallDir "$PROGRAMFILES\\${APP_NAME}"
RequestExecutionLevel admin

; 页面
Page directory
Page instfiles

; 安装部分
Section "MainSection" SEC01
    SetOutPath "$INSTDIR"
    File "dist\\HiFiScan.exe"
    File "dist\\*.dll"
    
    ; 创建开始菜单快捷方式
    CreateDirectory "$SMPROGRAMS\\${APP_NAME}"
    CreateShortCut "$SMPROGRAMS\\${APP_NAME}\\${APP_NAME}.lnk" "$INSTDIR\\${APP_EXE}"
    CreateShortCut "$SMPROGRAMS\\${APP_NAME}\\卸载.lnk" "$INSTDIR\\uninstall.exe"
    
    ; 创建桌面快捷方式
    CreateShortCut "$DESKTOP\\${APP_NAME}.lnk" "$INSTDIR\\${APP_EXE}"
    
    ; 写入卸载信息
    WriteRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${APP_NAME}" "DisplayName" "${APP_NAME}"
    WriteRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${APP_NAME}" "UninstallString" "$INSTDIR\\uninstall.exe"
    WriteUninstaller "$INSTDIR\\uninstall.exe"
SectionEnd

; 卸载部分
Section "Uninstall"
    Delete "$INSTDIR\\${APP_EXE}"
    Delete "$INSTDIR\\*.dll"
    Delete "$INSTDIR\\uninstall.exe"
    RMDir "$INSTDIR"
    
    Delete "$SMPROGRAMS\\${APP_NAME}\\*.*"
    RMDir "$SMPROGRAMS\\${APP_NAME}"
    Delete "$DESKTOP\\${APP_NAME}.lnk"
    
    DeleteRegKey HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${APP_NAME}"
SectionEnd
"""
    
    with open('hifiscan-installer.nsi', 'w', encoding='utf-8') as f:
        f.write(nsis_script)
    
    print("✅ 安装程序脚本已创建: hifiscan-installer.nsi")
    print("💡 要创建安装程序，请安装 NSIS 并运行:")
    print("   makensis hifiscan-installer.nsi")

def show_completion_info():
    """显示完成信息"""
    import platform

    system = platform.system()
    print(f"\n🎉 {system} 打包完成!")

    # 检查实际生成的文件
    possible_executables = [
        'dist/HiFiScan.exe',
        'dist/HiFiScan',
        'dist/HiFiScan.app'
    ]

    found_executable = None
    for exe in possible_executables:
        if Path(exe).exists():
            found_executable = exe
            break

    print("\n📁 输出文件:")
    if found_executable:
        print(f"   - {found_executable} (主程序)")
    print("   - hifiscan-installer.nsi (安装程序脚本)")

    print("\n📋 使用说明:")
    if system == "Windows":
        print("1. 直接运行: 双击 dist/HiFiScan.exe")
        print("2. 创建安装程序:")
        print("   - 安装 NSIS: https://nsis.sourceforge.io/")
        print("   - 运行: makensis hifiscan-installer.nsi")
    elif system == "Darwin":  # macOS
        print("1. 直接运行: ./dist/HiFiScan 或双击 HiFiScan.app")
        print("2. 在Windows上创建安装程序:")
        print("   - 将项目复制到Windows机器")
        print("   - 在Windows上运行打包脚本")
    else:  # Linux
        print("1. 直接运行: ./dist/HiFiScan")
        print("2. 创建AppImage或其他Linux包格式")

    print("\n🚀 分发建议:")
    print("- 将整个 dist 目录打包为 ZIP 文件")
    if system == "Windows":
        print("- 或者使用 NSIS 创建专业的安装程序")
    print("- 在目标机器上测试运行")

    if system != "Windows":
        print(f"\n💡 注意: 当前在 {system} 上打包")
        print("   - 生成的可执行文件适用于当前平台")
        print("   - 要创建Windows可执行文件，请在Windows机器上运行此脚本")

def main():
    """主函数"""
    print("🏗️  HiFiScan Windows 打包工具")
    print("=" * 50)
    
    # 检查环境
    if not check_requirements():
        print("\n❌ 环境检查失败，请解决上述问题后重试")
        return 1
    
    # 清理构建
    clean_build()
    
    # 构建可执行文件
    if not build_executable():
        print("\n❌ 构建失败")
        return 1
    
    # 验证构建
    if not verify_build():
        print("\n❌ 验证失败")
        return 1
    
    # 创建安装程序脚本
    create_installer_script()
    
    # 显示完成信息
    show_completion_info()
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
