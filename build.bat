@echo off
echo ========================================
echo HiFiScan Windows 打包工具
echo ========================================

REM 检查Python是否可用
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: Python 未安装或不在 PATH 中
    pause
    exit /b 1
)

REM 检查PyInstaller是否安装
python -c "import PyInstaller" >nul 2>&1
if errorlevel 1 (
    echo 安装 PyInstaller...
    pip install pyinstaller
)

REM 运行打包脚本
echo 开始打包...
python build_windows.py

echo.
echo 打包完成! 请检查 dist 目录
pause
