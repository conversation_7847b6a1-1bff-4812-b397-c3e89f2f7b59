#!/usr/bin/env python3
"""
测试音频录制功能

用于诊断校准测量中音频录制的问题
"""

import sys
import asyncio
import numpy as np
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

def test_simple_audio_recording():
    """测试简单的音频录制"""
    
    print("=== 测试简单音频录制 ===\n")
    
    try:
        import sounddevice as sd
        
        print("1. 测试sounddevice基本功能:")
        
        # 检查可用设备
        devices = sd.query_devices()
        print(f"   可用音频设备数量: {len(devices)}")
        
        # 获取默认输入设备
        default_input = sd.query_devices(kind='input')
        print(f"   默认输入设备: {default_input['name']}")
        print(f"   最大输入声道: {default_input['max_input_channels']}")
        print(f"   默认采样率: {default_input['default_samplerate']}")
        
        # 简单录制测试
        print("\n2. 执行3秒录制测试:")
        duration = 3.0
        samplerate = 48000
        
        print(f"   开始录制 {duration} 秒...")
        recording = sd.rec(int(duration * samplerate), 
                          samplerate=samplerate, 
                          channels=1, 
                          dtype='float32')
        sd.wait()  # 等待录制完成
        print(f"   录制完成!")
        
        # 分析录制结果
        print(f"   录制数据形状: {recording.shape}")
        print(f"   数据类型: {recording.dtype}")
        print(f"   最大值: {np.max(np.abs(recording)):.6f}")
        print(f"   RMS值: {np.sqrt(np.mean(recording**2)):.6f}")
        
        if np.max(np.abs(recording)) > 0.001:
            print("   ✅ 检测到音频信号")
            
            # 简单频谱分析
            fft_data = np.fft.fft(recording.flatten())
            freqs = np.fft.fftfreq(len(recording), 1/samplerate)
            
            # 找到最强的频率成分
            positive_freqs = freqs[:len(freqs)//2]
            positive_fft = np.abs(fft_data[:len(fft_data)//2])
            max_freq_idx = np.argmax(positive_fft)
            dominant_freq = positive_freqs[max_freq_idx]
            
            print(f"   主要频率成分: {dominant_freq:.1f} Hz")
            
            # 检查1000Hz附近
            freq_1000_mask = (np.abs(positive_freqs - 1000) <= 50)
            if np.any(freq_1000_mask):
                freq_1000_power = np.mean(positive_fft[freq_1000_mask])
                print(f"   1000Hz附近信号强度: {freq_1000_power:.6f}")
            else:
                print("   未检测到1000Hz信号")
        else:
            print("   ❌ 未检测到音频信号")
            
        return True
        
    except Exception as e:
        print(f"❌ 简单录制测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_hifi_audio_recording():
    """测试HiFi Audio类的录制功能"""
    
    print("\n=== 测试HiFi Audio类录制 ===\n")
    
    try:
        import hifiscan as hifi
        
        print("1. 创建Audio对象:")
        audio = hifi.Audio(48000)
        print(f"   采样率: {audio.rate}")
        
        print("\n2. 开始异步录制:")
        samples = []
        start_time = asyncio.get_event_loop().time()
        duration = 3.0
        
        print(f"   目标录制时长: {duration} 秒")
        
        async for recording in audio.record():
            current_time = asyncio.get_event_loop().time()
            elapsed = current_time - start_time
            
            if recording is not None and len(recording) > 0:
                audio_data = np.array(recording, dtype=np.float32)
                samples.extend(audio_data)
                print(f"   录制进度: {elapsed:.1f}s, 样本数: {len(samples)}")
            
            if elapsed >= duration:
                print(f"   录制完成，总时长: {elapsed:.2f}秒")
                break
        
        audio.close()
        
        if len(samples) > 0:
            print(f"\n3. 分析录制结果:")
            samples_array = np.array(samples)
            print(f"   总样本数: {len(samples_array)}")
            print(f"   最大值: {np.max(np.abs(samples_array)):.6f}")
            print(f"   RMS值: {np.sqrt(np.mean(samples_array**2)):.6f}")
            
            if np.max(np.abs(samples_array)) > 0.001:
                print("   ✅ HiFi Audio录制成功")
                return True
            else:
                print("   ❌ HiFi Audio录制无信号")
                return False
        else:
            print("   ❌ HiFi Audio录制失败，无数据")
            return False
            
    except Exception as e:
        print(f"❌ HiFi Audio测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_calibration_simulation():
    """模拟校准测量过程"""
    
    print("\n=== 模拟校准测量过程 ===\n")
    
    try:
        # 生成1000Hz测试信号
        duration = 3.0
        samplerate = 48000
        t = np.linspace(0, duration, int(duration * samplerate), False)
        
        # 创建1000Hz信号 + 噪声
        signal_1000hz = 0.1 * np.sin(2 * np.pi * 1000 * t)
        noise = 0.01 * np.random.randn(len(t))
        test_signal = signal_1000hz + noise
        
        print(f"1. 生成测试信号:")
        print(f"   信号长度: {len(test_signal)} 样本")
        print(f"   采样率: {samplerate} Hz")
        print(f"   时长: {duration} 秒")
        
        # 模拟校准分析
        print(f"\n2. 分析1000Hz信号:")
        
        fft_data = np.fft.fft(test_signal)
        freqs = np.fft.fftfreq(len(test_signal), 1/samplerate)
        
        # 找到1000Hz附近的频率
        target_freq = 1000.0
        freq_tolerance = 50.0
        freq_mask = (np.abs(freqs - target_freq) <= freq_tolerance) & (freqs > 0)
        
        if np.any(freq_mask):
            target_amplitude = np.mean(np.abs(fft_data[freq_mask]))
            measured_db = 20 * np.log10(target_amplitude)
            
            print(f"   检测到1000Hz信号")
            print(f"   信号幅度: {target_amplitude:.6f}")
            print(f"   测量电平: {measured_db:.2f} dB")
            
            # 计算校准偏移
            target_db = 94
            calibration_offset = target_db - measured_db
            print(f"   目标电平: {target_db} dB")
            print(f"   校准偏移: {calibration_offset:.2f} dB")
            
            print("   ✅ 校准分析算法正常")
            return True
        else:
            print("   ❌ 未检测到1000Hz信号")
            return False
            
    except Exception as e:
        print(f"❌ 校准模拟失败: {e}")
        return False

def main():
    """主函数"""
    
    print("音频录制功能诊断\n")
    
    # 测试简单录制
    test1_passed = test_simple_audio_recording()
    
    # 测试HiFi Audio录制
    test2_passed = asyncio.run(test_hifi_audio_recording())
    
    # 测试校准算法
    test3_passed = test_calibration_simulation()
    
    print(f"\n=== 测试结果总结 ===")
    print(f"简单录制测试: {'✅ 通过' if test1_passed else '❌ 失败'}")
    print(f"HiFi Audio测试: {'✅ 通过' if test2_passed else '❌ 失败'}")
    print(f"校准算法测试: {'✅ 通过' if test3_passed else '❌ 失败'}")
    
    if test1_passed and test2_passed and test3_passed:
        print("\n🎉 所有测试通过！音频录制功能正常。")
        print("问题可能在于:")
        print("- UI线程和异步线程的通信")
        print("- 异常处理掩盖了真实错误")
        print("- 事件循环的设置")
    else:
        print("\n❌ 部分测试失败，需要进一步调试:")
        if not test1_passed:
            print("- 基本音频录制有问题，检查音频设备和权限")
        if not test2_passed:
            print("- HiFi Audio类有问题，检查eventkit和异步实现")
        if not test3_passed:
            print("- 校准算法有问题，检查FFT分析逻辑")

if __name__ == "__main__":
    main()
