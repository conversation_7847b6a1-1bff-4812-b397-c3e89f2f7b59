#!/usr/bin/env python3
"""
测试 write_correction 函数是否可用
"""

import sys
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

def test_write_correction():
    """测试 write_correction 函数"""
    
    print("=== 测试 write_correction 函数 ===\n")
    
    try:
        # 导入 hifiscan 模块
        import hifiscan as hifi
        print("✅ 成功导入 hifiscan 模块")
        
        # 检查 write_correction 函数是否存在
        if hasattr(hifi, 'write_correction'):
            print("✅ write_correction 函数存在")
            
            # 创建测试校准数据
            test_calibration = [
                (20, 12.5),
                (1000, 12.5),
                (20000, 12.5)
            ]
            
            # 测试保存校准文件
            test_file = "test_calibration.txt"
            hifi.write_correction(test_file, test_calibration)
            print(f"✅ 成功保存测试校准文件: {test_file}")
            
            # 验证文件内容
            with open(test_file, 'r') as f:
                content = f.read()
                print(f"📄 文件内容:\n{content}")
            
            # 测试读取校准文件
            loaded_calibration = hifi.read_correction(test_file)
            print(f"✅ 成功读取校准文件: {loaded_calibration}")
            
            # 清理测试文件
            Path(test_file).unlink()
            print("🧹 清理测试文件完成")
            
        else:
            print("❌ write_correction 函数不存在")
            print("可用的函数:", [attr for attr in dir(hifi) if not attr.startswith('_')])
            
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
    except Exception as e:
        print(f"❌ 测试错误: {e}")

def test_calibration_workflow():
    """测试完整的校准工作流程"""
    
    print("\n=== 测试校准工作流程 ===\n")
    
    try:
        import hifiscan as hifi
        
        # 模拟校准数据
        target_db = 94
        measured_db = 81.5
        calibration_offset = target_db - measured_db
        
        print(f"目标电平: {target_db} dB")
        print(f"测量电平: {measured_db} dB")
        print(f"校准偏移: {calibration_offset} dB")
        
        # 创建校准数据
        calibration_data = [
            (20, calibration_offset),
            (1000, calibration_offset),
            (20000, calibration_offset)
        ]
        
        print(f"校准数据: {calibration_data}")
        
        # 生成文件名
        import datetime as dt
        timestamp = dt.datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'mic_calibration_1000Hz_{target_db}dB_{timestamp}.txt'
        
        print(f"文件名: {filename}")
        
        # 保存校准文件
        hifi.write_correction(filename, calibration_data)
        print(f"✅ 校准文件保存成功: {filename}")
        
        # 验证文件
        if Path(filename).exists():
            print("✅ 文件确实存在")
            
            # 读取并验证内容
            loaded_data = hifi.read_correction(filename)
            print(f"✅ 读取校准数据: {loaded_data}")
            
            # 比较数据
            if loaded_data == calibration_data:
                print("✅ 数据完全匹配")
            else:
                print("⚠️ 数据不完全匹配")
                print(f"原始: {calibration_data}")
                print(f"读取: {loaded_data}")
        
        # 清理
        Path(filename).unlink()
        print("🧹 清理完成")
        
    except Exception as e:
        print(f"❌ 工作流程测试错误: {e}")

def main():
    """主函数"""
    
    test_write_correction()
    test_calibration_workflow()
    
    print("\n=== 测试完成 ===")
    print("\n如果所有测试都通过，说明 write_correction 函数现在可以正常使用了。")
    print("您可以重新尝试在 app1.py 中保存校准文件。")

if __name__ == "__main__":
    main()
