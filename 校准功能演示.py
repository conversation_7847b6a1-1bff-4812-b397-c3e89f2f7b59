#!/usr/bin/env python3
"""
麦克风校准功能演示

展示如何使用新添加的麦克风校准器校准功能
"""

import sys
import numpy as np
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

def demonstrate_calibration_workflow():
    """演示校准工作流程"""
    
    print("=== 麦克风校准器校准功能演示 ===\n")
    
    print("1. 功能概述:")
    print("   - 使用1000Hz声校准器（94dB/114dB）校准麦克风")
    print("   - 自动分析音频信号并计算校准偏移")
    print("   - 生成标准格式的校准文件")
    print("   - 支持实时验证和应用\n")
    
    print("2. 使用步骤:")
    print("   a) 在软件中选择 '工具' → '麦克风校准器校准'")
    print("   b) 选择校准器档位（94dB 或 114dB）")
    print("   c) 按照指导放置麦克风")
    print("   d) 点击'开始测量'进行校准")
    print("   e) 保存校准文件并应用\n")
    
    print("3. 技术特点:")
    print("   - FFT频谱分析确保精度")
    print("   - 自动噪声过滤")
    print("   - 实时结果验证")
    print("   - 标准校准文件格式\n")
    
    print("4. 校准算法核心:")
    print("   校准偏移 = 标准电平(dB) - 测量电平(dB)")
    print("   测量电平(dB) = 20 × log10(1000Hz幅度)")
    print("   校准后电平 = 原始测量值 + 校准偏移\n")
    
    # 模拟校准过程
    simulate_calibration_process()

def simulate_calibration_process():
    """模拟校准过程"""
    
    print("5. 模拟校准过程:")
    
    # 模拟94dB校准
    print("\n   94dB校准示例:")
    target_db_94 = 94.0
    simulated_measured_94 = 81.5  # 模拟测量值
    offset_94 = target_db_94 - simulated_measured_94
    
    print(f"   目标电平: {target_db_94} dB")
    print(f"   测量电平: {simulated_measured_94} dB")
    print(f"   校准偏移: {offset_94} dB")
    
    # 模拟114dB校准
    print("\n   114dB校准示例:")
    target_db_114 = 114.0
    simulated_measured_114 = 101.8  # 模拟测量值
    offset_114 = target_db_114 - simulated_measured_114
    
    print(f"   目标电平: {target_db_114} dB")
    print(f"   测量电平: {simulated_measured_114} dB")
    print(f"   校准偏移: {offset_114} dB")
    
    # 生成校准文件示例
    print("\n6. 校准文件示例:")
    calibration_data = [
        (20, offset_94),
        (1000, offset_94),
        (20000, offset_94)
    ]
    
    print("   文件内容:")
    for freq, offset in calibration_data:
        print(f"   {freq} {offset:.2f}")
    
    print("\n7. 应用效果:")
    original_measurement = 85.0
    calibrated_measurement = original_measurement + offset_94
    print(f"   原始测量: {original_measurement} dB")
    print(f"   校准后: {calibrated_measurement} dB")
    print(f"   误差: {abs(calibrated_measurement - target_db_94):.1f} dB")

def show_integration_example():
    """显示集成示例"""
    
    print("\n=== 代码集成示例 ===\n")
    
    integration_code = '''
# 在app1.py中添加的主要功能:

1. 菜单项添加:
   tools.addAction('麦克风校准器校准', self.micCalibrationTool)

2. 校准对话框:
   def micCalibrationTool(self):
       # 创建校准界面
       # 处理用户交互
       # 启动测量过程

3. 异步测量:
   async def async_calibration_measurement(self, audio, duration):
       # 录制音频数据
       # 分析1000Hz信号
       # 计算校准偏移

4. 数据分析:
   def analyzeCalibrationData(self, audio_data, sample_rate):
       # FFT频谱分析
       # 1000Hz幅度提取
       # dB转换和偏移计算

5. 结果保存:
   # 生成标准校准文件
   # 可选择立即应用校准
    '''
    
    print(integration_code)

def show_usage_tips():
    """显示使用技巧"""
    
    print("\n=== 使用技巧 ===\n")
    
    tips = [
        "确保校准器电池充足，输出稳定",
        "麦克风完全插入校准器，密封良好",
        "选择安静的测量环境，避免干扰",
        "测量过程中保持设备稳定",
        "定期校准，特别是重要测量前",
        "保存校准文件备份，便于追溯",
        "验证校准结果，确保精度符合要求"
    ]
    
    for i, tip in enumerate(tips, 1):
        print(f"{i}. {tip}")

def main():
    """主函数"""
    
    demonstrate_calibration_workflow()
    show_integration_example()
    show_usage_tips()
    
    print("\n=== 演示完成 ===")
    print("\n要使用此功能，请:")
    print("1. 运行 app1.py")
    print("2. 点击 '工具' → '麦克风校准器校准'")
    print("3. 按照界面指导完成校准")

if __name__ == "__main__":
    main()
