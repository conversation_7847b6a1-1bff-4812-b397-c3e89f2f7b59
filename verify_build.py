#!/usr/bin/env python3
"""
验证构建结果的独立脚本
"""

import platform
from pathlib import Path

def verify_build_result():
    """验证构建结果"""
    print("🔍 验证HiFiScan构建结果...")
    
    system = platform.system()
    print(f"📱 当前平台: {system}")
    
    # 检查可执行文件
    possible_paths = [
        Path('dist/HiFiScan.exe'),  # Windows
        Path('dist/HiFiScan'),     # Linux/macOS
        Path('dist/HiFiScan.app'), # macOS app bundle
    ]
    
    exe_path = None
    for path in possible_paths:
        if path.exists():
            exe_path = path
            break
    
    if exe_path:
        print(f"✅ 可执行文件已找到: {exe_path}")
        
        if exe_path.is_file():
            size_mb = exe_path.stat().st_size / (1024 * 1024)
            print(f"📦 文件大小: {size_mb:.1f} MB")
            
            # 检查文件权限
            if exe_path.stat().st_mode & 0o111:
                print("✅ 文件具有执行权限")
            else:
                print("⚠️ 文件缺少执行权限")
        else:
            print("📁 这是一个应用程序包")
        
        # 显示dist目录内容
        dist_files = list(Path('dist').glob('*'))
        print(f"\n📁 dist目录包含 {len(dist_files)} 个项目:")
        
        for file in sorted(dist_files):
            if file.is_file():
                size_mb = file.stat().st_size / (1024 * 1024)
                if size_mb > 1:
                    print(f"   📄 {file.name} ({size_mb:.1f} MB)")
                else:
                    size_kb = file.stat().st_size / 1024
                    print(f"   📄 {file.name} ({size_kb:.1f} KB)")
            else:
                print(f"   📁 {file.name}/")
        
        # 平台特定的使用说明
        print(f"\n🚀 使用说明 ({system}):")
        if system == "Windows":
            print("   双击 HiFiScan.exe 运行")
        elif system == "Darwin":  # macOS
            print("   运行: ./dist/HiFiScan")
            print("   或者: open dist/HiFiScan.app (如果存在)")
        else:  # Linux
            print("   运行: ./dist/HiFiScan")
        
        return True
    else:
        print("❌ 未找到可执行文件")
        print("\n📁 dist目录内容:")
        if Path('dist').exists():
            dist_files = list(Path('dist').glob('*'))
            for file in sorted(dist_files):
                print(f"   - {file.name}")
        else:
            print("   dist目录不存在")
        return False

def test_executable():
    """测试可执行文件"""
    print("\n🧪 测试可执行文件...")
    
    exe_path = None
    possible_paths = [
        Path('dist/HiFiScan.exe'),
        Path('dist/HiFiScan'),
        Path('dist/HiFiScan.app/Contents/MacOS/HiFiScan'),
    ]
    
    for path in possible_paths:
        if path.exists() and path.is_file():
            exe_path = path
            break
    
    if not exe_path:
        print("❌ 未找到可测试的可执行文件")
        return False
    
    try:
        import subprocess
        
        # 尝试运行 --help 或 --version (如果支持)
        print(f"🔍 测试 {exe_path}...")
        
        # 简单的存在性测试
        if exe_path.exists():
            print("✅ 文件存在且可访问")
            return True
        else:
            print("❌ 文件不存在")
            return False
            
    except Exception as e:
        print(f"⚠️ 测试过程中出现错误: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("HiFiScan 构建验证工具")
    print("=" * 50)
    
    # 验证构建结果
    build_success = verify_build_result()
    
    if build_success:
        # 测试可执行文件
        test_success = test_executable()
        
        if test_success:
            print("\n🎉 构建验证成功!")
            print("\n📋 下一步:")
            print("1. 测试运行应用程序")
            print("2. 验证所有功能正常")
            print("3. 在目标平台上测试")
            
            system = platform.system()
            if system != "Windows":
                print(f"\n💡 注意: 当前在 {system} 上构建")
                print("   要创建Windows可执行文件，请在Windows机器上运行构建脚本")
        else:
            print("\n⚠️ 构建成功但测试失败")
    else:
        print("\n❌ 构建验证失败")
        print("请检查构建过程是否有错误")

if __name__ == "__main__":
    main()
