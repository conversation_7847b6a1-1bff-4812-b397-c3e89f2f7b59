# G-VoTest 安装指南

## 系统要求

- **Python**: 3.8 或更高版本
- **操作系统**: Windows 10+, macOS 10.14+, Linux (Ubuntu 18.04+)
- **音频设备**: 支持的音频接口和麦克风

## 快速安装

### 1. 克隆或下载项目
```bash
# 如果使用git
git clone <repository-url>
cd gvotest

# 或者下载并解压项目文件
```

### 2. 创建虚拟环境（推荐）
```bash
# 创建虚拟环境
python -m venv gvotest-env

# 激活虚拟环境
# Windows:
gvotest-env\Scripts\activate
# macOS/Linux:
source gvotest-env/bin/activate
```

### 3. 安装依赖

#### 选项A: 完整安装（推荐）
```bash
pip install -r requirements.txt
```

#### 选项B: 最小安装
```bash
pip install -r requirements-minimal.txt
```

#### 选项C: 开发环境安装
```bash
pip install -r requirements-dev.txt
```

### 4. 运行软件
```bash
python app1.py
```

## 详细安装说明

### 依赖包说明

#### 必需依赖
- **PyQt6** (6.7.1): GUI框架
- **numpy** (2.0.1): 数值计算
- **sounddevice** (0.5.1): 音频录制和播放
- **pyqtgraph** (0.13.7): 实时数据可视化
- **eventkit** (1.0.3): 异步事件处理

#### 可选依赖
- **numba** (0.60.0): 高性能数值计算加速（推荐）

### 平台特定说明

#### Windows
```bash
# 可能需要安装Microsoft Visual C++ Redistributable
# 下载地址: https://aka.ms/vs/17/release/vc_redist.x64.exe

pip install -r requirements.txt
```

#### macOS
```bash
# 可能需要安装Xcode Command Line Tools
xcode-select --install

pip install -r requirements.txt
```

#### Linux (Ubuntu/Debian)
```bash
# 安装系统依赖
sudo apt update
sudo apt install python3-dev python3-pip portaudio19-dev

pip install -r requirements.txt
```

#### Linux (CentOS/RHEL/Fedora)
```bash
# 安装系统依赖
sudo yum install python3-devel python3-pip portaudio-devel
# 或者在较新版本中:
sudo dnf install python3-devel python3-pip portaudio-devel

pip install -r requirements.txt
```

## 故障排除

### 常见问题

#### 1. PyQt6 安装失败
```bash
# 尝试升级pip
pip install --upgrade pip setuptools wheel

# 重新安装PyQt6
pip install PyQt6
```

#### 2. sounddevice 安装失败
```bash
# Windows: 安装Microsoft Visual C++ Build Tools
# macOS: 安装Xcode Command Line Tools
# Linux: 安装portaudio开发包

pip install sounddevice
```

#### 3. numba 安装失败
```bash
# numba是可选的，如果安装失败可以跳过
# 软件会自动检测并在没有numba时使用纯Python实现
pip install --no-deps numba
```

#### 4. 音频设备权限问题
- **macOS**: 在系统偏好设置 → 安全性与隐私 → 隐私 → 麦克风中允许Python访问
- **Windows**: 在设置 → 隐私 → 麦克风中允许应用访问
- **Linux**: 确保用户在audio组中: `sudo usermod -a -G audio $USER`

### 验证安装

运行以下命令验证安装：
```bash
python -c "
import PyQt6
import numpy
import sounddevice
import pyqtgraph
import eventkit
print('所有依赖安装成功!')
"
```

### 测试音频功能
```bash
python -c "
import sounddevice as sd
print('可用音频设备:')
print(sd.query_devices())
"
```

## 更新

更新到最新版本：
```bash
# 更新依赖
pip install -r requirements.txt --upgrade

# 或者更新特定包
pip install --upgrade PyQt6 numpy sounddevice pyqtgraph
```

## 卸载

```bash
# 停用虚拟环境
deactivate

# 删除虚拟环境目录
rm -rf hifiscan-env  # Linux/macOS
rmdir /s hifiscan-env  # Windows
```

## 技术支持

如果遇到安装问题：

1. 检查Python版本: `python --version`
2. 检查pip版本: `pip --version`
3. 查看错误日志
4. 尝试在虚拟环境中安装
5. 检查系统特定的依赖要求

## 开发环境设置

如果您想参与开发：

```bash
# 安装开发依赖
pip install -r requirements-dev.txt

# 安装pre-commit钩子
pre-commit install

# 运行测试
pytest

# 代码格式化
black .
isort .
```
