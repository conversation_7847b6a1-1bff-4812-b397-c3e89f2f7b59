#!/usr/bin/env python3
"""
测试QTimer修复

验证QTimer导入问题是否已修复
"""

import sys
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

def test_qtimer_import():
    """测试QTimer导入"""
    
    print("=== 测试QTimer导入 ===\n")
    
    try:
        from PyQt6 import QtCore as qtcore, QtWidgets as qt
        
        print("1. 测试导入:")
        print("   ✅ QtCore导入成功")
        print("   ✅ QtWidgets导入成功")
        
        print("\n2. 测试QTimer:")
        if hasattr(qtcore, 'QTimer'):
            print("   ✅ qtcore.QTimer 存在")
        else:
            print("   ❌ qtcore.QTimer 不存在")
            
        if hasattr(qt, 'QTimer'):
            print("   ❌ qt.QTimer 存在 (不应该存在)")
        else:
            print("   ✅ qt.QTimer 不存在 (正确)")
        
        print("\n3. 测试QTimer.singleShot:")
        try:
            # 测试QTimer.singleShot调用
            qtcore.QTimer.singleShot(0, lambda: print("   ✅ QTimer.singleShot 调用成功"))
            print("   ✅ QTimer.singleShot 方法可用")
        except Exception as e:
            print(f"   ❌ QTimer.singleShot 调用失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入测试失败: {e}")
        return False

def test_app_imports():
    """测试app1.py中的导入"""
    
    print("\n=== 测试app1.py导入 ===\n")
    
    try:
        # 模拟app1.py的导入
        from PyQt6 import QtCore as qtcore, QtGui as qtgui, QtWidgets as qt
        import numpy as np
        
        print("1. 主要导入:")
        print("   ✅ PyQt6.QtCore as qtcore")
        print("   ✅ PyQt6.QtWidgets as qt")
        print("   ✅ numpy")
        
        print("\n2. 验证QTimer使用:")
        
        # 测试正确的用法
        try:
            qtcore.QTimer.singleShot(0, lambda: None)
            print("   ✅ qtcore.QTimer.singleShot 正常")
        except Exception as e:
            print(f"   ❌ qtcore.QTimer.singleShot 错误: {e}")
        
        # 测试错误的用法（应该失败）
        try:
            qt.QTimer.singleShot(0, lambda: None)
            print("   ❌ qt.QTimer.singleShot 不应该成功")
        except AttributeError:
            print("   ✅ qt.QTimer.singleShot 正确失败 (QTimer不在QtWidgets中)")
        except Exception as e:
            print(f"   ⚠️ qt.QTimer.singleShot 其他错误: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ app1.py导入测试失败: {e}")
        return False

def show_fix_summary():
    """显示修复总结"""
    
    print("\n=== 修复总结 ===\n")
    
    print("问题:")
    print("- 代码中使用了 qt.QTimer.singleShot()")
    print("- 但QTimer在QtCore模块中，不在QtWidgets中")
    print("- 导致 AttributeError: module 'PyQt6.QtWidgets' has no attribute 'QTimer'")
    
    print("\n修复:")
    print("- 将所有 qt.QTimer.singleShot() 改为 qtcore.QTimer.singleShot()")
    print("- 保持导入不变: from PyQt6 import QtCore as qtcore, QtWidgets as qt")
    
    print("\n修复的位置:")
    locations = [
        "simple_calibration_measurement() - UI更新",
        "simple_calibration_measurement() - 错误处理",
        "run_simple_measurement() - 错误处理和按钮启用",
        "analyzeCalibrationData() - 错误处理",
        "analyzeCalibrationData() - 保存按钮启用",
        "updateCalibrationUI() - UI更新"
    ]
    
    for i, location in enumerate(locations, 1):
        print(f"{i}. {location}")
    
    print("\n现在的校准流程:")
    print("1. 点击'开始测量' ✅")
    print("2. 显示录制进度 ✅") 
    print("3. 完成音频分析 ✅")
    print("4. 更新UI界面 ✅ (修复后)")
    print("5. 启用保存按钮 ✅ (修复后)")
    print("6. 保存校准文件 ✅")

def main():
    """主函数"""
    
    print("QTimer修复验证\n")
    
    # 测试QTimer导入
    test1_passed = test_qtimer_import()
    
    # 测试app导入
    test2_passed = test_app_imports()
    
    print(f"\n=== 测试结果 ===")
    print(f"QTimer导入测试: {'✅ 通过' if test1_passed else '❌ 失败'}")
    print(f"app1.py导入测试: {'✅ 通过' if test2_passed else '❌ 失败'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 QTimer修复成功！")
        print("现在校准功能应该完全正常工作了。")
    else:
        print("\n❌ 部分测试失败，需要进一步检查。")
    
    # 显示修复总结
    show_fix_summary()

if __name__ == "__main__":
    main()
