import asyncio
import copy
import datetime as dt
import logging
import os
import pickle
import signal
import sys
from pathlib import Path
import base64
import json
from PyQt6 import QtCore as qtcore, QtGui as qtgui, QtWidgets as qt
import numpy as np
import pyqtgraph as pg
import sounddevice as sd
import base64

import hifiscan as hifi
from io_ import write_correction  # 直接导入 write_correction 函数
from PyQt6.QtGui import QPixmap
from PyQt6.QtWidgets import QLabel, QDialog, QVBoxLayout, QProgressBar
from PyQt6.QtCore import QTimer
from random import randint
class App(qt.QWidget):

    SAMPLE_RATES = {rate: i for i, rate in enumerate([
        8000, 11025, 16000, 22050, 44100, 48000, 88200, 96000,
        176400, 192000, 352800, 384000])}

    def __init__(self):
        super().__init__()
        self.paused = False
        self.analyzer = None
        self.refAnalyzer = None
        self.calibration = None
        self.target = None
        self.saveDir = Path.home()
        # self.loadPreset()

        # 定义需要依次测试的频率范围列表
        self.frequency_ranges = [
            (20, 200),
            (20, 800),
            (200, 800),
            (200, 2000),
            (800, 6000),
            (800, 20000),
            (8000, 50000),
            (2800, 80000),
            (60, 20000)
        ]
        self.current_range_index = 0

        # 初始化UI等
        self.stack = qt.QStackedWidget()
        self.stack.addWidget(self.spectrumWidget())
        self.stack.addWidget(self.irWidget())
        self.stack.currentChanged.connect(self.plot)

        vbox = qt.QVBoxLayout()
        vbox.setContentsMargins(0, 0, 0, 0)
        vbox.addWidget(self.stack)
        vbox.addWidget(self.sharedControls())

        self.resetAudio()
        self.setLayout(vbox)
        self.setWindowTitle('G-Vo Test 校准工具')
        self.resize(1800, 900)
        self.show()

        # 启动异步任务
        self.loop = asyncio.get_event_loop_policy().get_event_loop()
        self.task = self.loop.create_task(wrap_coro(self.analyze()))


    
    def loadPreset(self):
        """显示启动图片，并在底部添加一个倒计时进度条"""
        dialog = QDialog(self)
        dialog.setWindowTitle("G-Vo Test")
        dialog.setFixedSize(626, 357)  # 设置弹窗大小

        layout = QVBoxLayout(dialog)
        label = QLabel(dialog)

        # 加载图片
        with open("preset.json", "r") as json_file:
            data = json.load(json_file)
            base64_image = data["image"]

        pixmap = QPixmap()
        pixmap.loadFromData(base64.b64decode(base64_image))
        label.setPixmap(pixmap)
        label.setScaledContents(True)  # 图片自适应大小
        layout.addWidget(label)
        # pixmap = QPixmap(image_path)
        # label.setPixmap(pixmap)
        # label.setScaledContents(True)  # 图片自适应大小
        # layout.addWidget(label)

        # 添加进度条
        progress_bar = QProgressBar(dialog)
        progress_bar.setRange(0, 100)  # 进度范围 0 到 100
        progress_bar.setValue(0)  # 初始值为 0
        layout.addWidget(progress_bar)

        # 定时器更新进度条
        timer = QTimer(dialog)
        duration = 1700  # 倒计时 3 秒
        interval = randint(37,137)   # 每次更新间隔为 100 毫秒
        steps = duration // interval
        current_step = [0]  # 用列表包裹以便在闭包中修改

        def update_progress():
            current_step[0] += 1
            progress = int((current_step[0] / steps) * 100)
            progress_bar.setValue(progress)
            if current_step[0] >= steps:
                timer.stop()
                dialog.accept()  # 关闭弹窗

        timer.timeout.connect(update_progress)
        timer.start(interval)

        dialog.exec()

    async def analyze(self):
        while True:
            self.audioChanged = False
            try:
                rate = int(self.rateCombo.currentText())
                audio = None
                audio = hifi.Audio(rate)
                while not self.audioChanged:
                    lo = self.lo.value()
                    hi = self.hi.value()
                    secs = self.secs.value()
                    ampl = self.ampl.value() / 100
                    ch = self.channelCombo.currentIndex()
                    if self.paused or lo >= hi or secs <= 0 or not ampl:
                        await asyncio.sleep(0.1)
                        continue

                    analyzer = hifi.Analyzer(lo, hi, secs, audio.rate, ampl,
                                             self.calibration, self.target)
                    sound = analyzer.chirp
                    if ch:
                        silence = np.zeros_like(sound)
                        sound = [sound, silence] if ch == 1 \
                            else [silence, sound]
                    audio.play(sound)
                    async for recording in audio.record():
                        if self.paused:
                            audio.cancelPlay()
                            break
                        if analyzer.findMatch(recording):
                            self.analyzer = analyzer
                            self.plot()
                            break
                        if analyzer.timedOut():
                            break
            except Exception as exc:
                qt.QMessageBox.critical(self, 'Error', str(exc))
                self.resetAudio()
            finally:
                if audio:
                    audio.close()

    def resetAudio(self):
        defaultDevice = next((dev for dev in sd.query_devices()
                             if dev['name'] == 'default'), None)
        defaultRate = defaultDevice.get('default_samplerate', 0) \
            if defaultDevice else 0
        if not defaultRate:
            defaultRate = sd.default.samplerate
        if defaultRate not in self.SAMPLE_RATES:
            defaultRate = 48000
        index = self.SAMPLE_RATES[defaultRate]
        self.rateCombo.setCurrentIndex(index)
        self.audioChanged = True

    def plot(self, *_):
        if self.stack.currentIndex() == 0:
            self.plotSpectrum()
        else:
            self.plotIR()

    def plotSpectrum(self):
        smoothing = self.spectrumSmoothing.value()
        if self.analyzer:
            spectrum = self.analyzer.spectrum(smoothing)
            self.spectrumPlot.setData(*spectrum)
            target = self.analyzer.targetSpectrum(spectrum)
            if target:
                self.targetPlot.setData(*target)
            else:
                self.targetPlot.clear()
        if self.refAnalyzer:
            spectrum = self.refAnalyzer.spectrum(smoothing)
            self.refSpectrumPlot.setData(*spectrum)

    def plotIR(self):
        if self.refAnalyzer and self.useCombo.currentIndex() == 0:
            analyzer = self.refAnalyzer
        else:
            analyzer = self.analyzer
        if not analyzer:
            return
        secs = self.msDuration.value() / 1000
        dbRange = self.dbRange.value()
        beta = self.kaiserBeta.value()
        smoothing = self.irSmoothing.value()
        causality = self.causality.value() / 100

        t, ir = analyzer.h_inv(secs, dbRange, beta, smoothing, causality)
        self.irPlot.setData(1000 * t, ir)

        logIr = np.log10(1e-8 + np.abs(ir))
        self.logIrPlot.setData(1000 * t, logIr)

        corrFactor = analyzer.correctionFactor(ir)
        self.correctionPlot.setData(*corrFactor)

        spectrum, spectrum_resamp = analyzer.correctedSpectrum(corrFactor)
        self.simPlot.setData(*spectrum)
        self.avSimPlot.setData(*spectrum_resamp)
        target = analyzer.targetSpectrum(spectrum)
        if target:
            self.targetSimPlot.setData(*target)
        else:
            self.targetSimPlot.clear()

    def screenshot(self):
        timestamp = dt.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        name = f'G-VoTest_{timestamp}.png'
        path, _ = qt.QFileDialog.getSaveFileName(
            self, 'Save screenshot', str(self.saveDir / name), 'PNG (*.png)')
        if path:
            self.stack.grab().save(path)
            self.saveDir = Path(path).parent

    def saveIR(self):
        if self.refAnalyzer and self.useCombo.currentIndex() == 0:
            analyzer = self.refAnalyzer
        else:
            analyzer = self.analyzer
        if not analyzer:
            return
        ms = int(self.msDuration.value())
        db = int(self.dbRange.value())
        beta = int(self.kaiserBeta.value())
        smoothing = int(self.irSmoothing.value())
        causality = int(self.causality.value())
        _, irInv = analyzer.h_inv(
            ms / 1000, db, beta, smoothing, causality / 100)

        name = f'IR_{ms}ms_{db}dB_{beta}t_{smoothing}s_{causality}c.wav'
        path, _ = qt.QFileDialog.getSaveFileName(
            self, 'Save inverse impulse response',
            str(self.saveDir / name), 'WAV (*.wav)')
        if path:
            data = np.vstack([irInv, irInv])
            hifi.write_wav(path, data, analyzer.rate)
            self.saveDir = Path(path).parent

    def run(self):
        """Run both the Qt and asyncio event loops."""

        def updateQt():
            qApp = qtgui.QGuiApplication.instance()
            qApp.processEvents()
            self.loop.call_later(0.03, updateQt)

        signal.signal(signal.SIGINT, lambda *args: self.close())
        updateQt()
        self.loop.run_forever()
        self.loop.run_until_complete(self.task)
        os._exit(0)

    def closeEvent(self, ev):
        self.task.cancel()
        self.loop.stop()

    def spectrumWidget(self) -> qt.QWidget:
        topWidget = qt.QWidget()
        main_layout = qt.QVBoxLayout()
        main_layout.setContentsMargins(0, 0, 0, 0)
        topWidget.setLayout(main_layout)

        # Create the spectrum plot widget
        axes = {ori: Axis(ori) for ori in ['bottom', 'left', 'top', 'right']}
        for ax in axes.values():
            ax.setGrid(200)
        self.spectrumPlotWidget = pw = pg.PlotWidget(axisItems=axes)
        pw.setLabel('left', 'Relative Power [dB]')
        pw.setLabel('bottom', 'Frequency [Hz]')
        pw.setLogMode(x=True)
        self.targetPlot = pw.plot(pen=(255, 0, 0), stepMode='right')
        self.refSpectrumPlot = pw.plot(pen=(255, 100, 0), stepMode='right')
        self.spectrumPlot = pw.plot(pen=(0, 255, 255), stepMode='right')
        self.spectrumPlot.curve.setCompositionMode(
            qtgui.QPainter.CompositionMode.CompositionMode_Plus
        )
        main_layout.addWidget(pw)

        # Create a horizontal layout for controls
        controls_layout = qt.QHBoxLayout()

        # Add the logo to the far left
        logo_label = QLabel(self)
        logo_pixmap = QPixmap("logo.png")  # Replace with the actual logo path
        logo_label.setPixmap(logo_pixmap)
        logo_label.setScaledContents(True)  # Adapt size
        logo_label.setFixedSize(50, 50)  # Adjust logo size as needed
        controls_layout.addWidget(logo_label)

        # Add "低频" label and SpinBox
        controls_layout.addWidget(qt.QLabel('低频: '))
        self.lo = pg.SpinBox(value=20, step=5, bounds=[5, 40000], suffix='Hz')
        controls_layout.addWidget(self.lo)

        # Add other controls
        controls_layout.addSpacing(32)
        controls_layout.addWidget(qt.QLabel('高频: '))
        self.hi = pg.SpinBox(value=20000, step=100, bounds=[5, 40000], suffix='Hz')
        controls_layout.addWidget(self.hi)

        controls_layout.addSpacing(32)
        controls_layout.addWidget(qt.QLabel('时长: '))
        self.secs = pg.SpinBox(value=1.0, step=0.1, bounds=[0.1, 30], suffix='s')
        controls_layout.addWidget(self.secs)

        controls_layout.addSpacing(32)
        controls_layout.addWidget(qt.QLabel('幅度: '))
        self.ampl = pg.SpinBox(value=40, step=1, bounds=[0, 100], suffix='%')
        controls_layout.addWidget(self.ampl)

        # Initialize channelCombo and add it to the layout
        self.channelCombo = qt.QComboBox()
        self.channelCombo.addItems(['Stereo', 'Left', 'Right'])
        controls_layout.addWidget(self.channelCombo)

        # Add spectrum smoothing control
        controls_layout.addSpacing(32)
        controls_layout.addWidget(qt.QLabel('平滑: '))
        self.spectrumSmoothing = pg.SpinBox(value=15, step=1, bounds=[0, 30])
        controls_layout.addWidget(self.spectrumSmoothing)

        controls_layout.addStretch(1)
        main_layout.addLayout(controls_layout)

        return topWidget

    def irWidget(self) -> qt.QWidget:
        topWidget = qt.QWidget()
        vbox = qt.QVBoxLayout()
        vbox.setContentsMargins(0, 0, 0, 0)
        topWidget.setLayout(vbox)
        splitter = qt.QSplitter(qtcore.Qt.Orientation.Vertical)
        vbox.addWidget(splitter)

        self.irPlotWidget = pw = pg.PlotWidget()
        pw.showGrid(True, True, 0.8)
        self.irPlot = pw.plot(pen=(0, 255, 255))
        pw.setLabel('left', 'Inverse IR')
        splitter.addWidget(pw)

        self.logIrPlotWidget = pw = pg.PlotWidget()
        pw.showGrid(True, True, 0.8)
        pw.setLabel('left', 'Log Inverse IR')
        self.logIrPlot = pw.plot(pen=(0, 255, 100))
        splitter.addWidget(pw)

        self.correctionPlotWidget = pw = pg.PlotWidget()
        pw.showGrid(True, True, 0.8)
        pw.setLabel('left', 'Correction Factor')
        self.correctionPlot = pw.plot(
            pen=(255, 255, 200), fillLevel=0, fillBrush=(255, 0, 0, 100))
        splitter.addWidget(pw)

        axes = {ori: Axis(ori) for ori in ['bottom', 'left']}
        for ax in axes.values():
            ax.setGrid(200)
        self.simPlotWidget = pw = pg.PlotWidget(axisItems=axes)
        pw.showGrid(True, True, 0.8)
        pw.setLabel('left', 'Corrected Spectrum')
        self.simPlot = pg.PlotDataItem(pen=(150, 100, 60), stepMode='right')
        pw.addItem(self.simPlot, ignoreBounds=True)
        self.avSimPlot = pw.plot(pen=(255, 255, 200), stepMode='right')
        self.targetSimPlot = pw.plot(pen=(255, 0, 0), stepMode='right')
        pw.setLogMode(x=True)
        splitter.addWidget(pw)

        self.msDuration = pg.SpinBox(
            value=50, step=1, bounds=[1, 1000], suffix='ms')
        self.msDuration.sigValueChanging.connect(self.plot)
        self.dbRange = pg.SpinBox(
            value=24, step=1, bounds=[0, 100], suffix='dB')
        self.dbRange.sigValueChanging.connect(self.plot)
        self.kaiserBeta = pg.SpinBox(
            value=5, step=1, bounds=[0, 100])
        self.kaiserBeta.sigValueChanging.connect(self.plot)
        self.irSmoothing = pg.SpinBox(
            value=15, step=1, bounds=[0, 30])
        self.irSmoothing.sigValueChanging.connect(self.plot)

        causalityLabel = qt.QLabel('因果性(Causality): ')
        causalityLabel.setToolTip('0% = Zero phase, 100% = Zero latency')
        self.causality = pg.SpinBox(
            value=0, step=5, bounds=[0, 100], suffix='%')
        self.causality.sigValueChanging.connect(self.plot)

        self.useCombo = qt.QComboBox()
        self.useCombo.addItems(['Stored measurements', 'Last measurement'])
        self.useCombo.currentIndexChanged.connect(self.plot)

        # 新增的Quick Export按钮
        quickExportButton = qt.QPushButton('快速校准')
        quickExportButton.clicked.connect(self.quickSaveIR)

        exportButton = qt.QPushButton('生成校准文件')
        exportButton.setShortcut('E')
        exportButton.setToolTip('<Key E>')
        exportButton.clicked.connect(self.saveIR)

        hbox = qt.QHBoxLayout()
        hbox.addStretch(1)
        hbox.addWidget(qt.QLabel('长度: '))
        hbox.addWidget(self.msDuration)
        hbox.addSpacing(32)
        hbox.addWidget(qt.QLabel('范围: '))
        hbox.addWidget(self.dbRange)
        hbox.addSpacing(32)
        hbox.addWidget(qt.QLabel('收紧(Tapering): '))
        hbox.addWidget(self.kaiserBeta)
        hbox.addSpacing(32)
        hbox.addWidget(qt.QLabel('平滑: '))
        hbox.addWidget(self.irSmoothing)
        hbox.addSpacing(32)
        hbox.addWidget(causalityLabel)
        hbox.addWidget(self.causality)
        hbox.addSpacing(32)
        hbox.addWidget(qt.QLabel('Use: '))
        hbox.addWidget(self.useCombo)
        hbox.addStretch(1)
        # 将 Quick Export 按钮放在 Export as WAV 左边
        hbox.addWidget(quickExportButton)
        hbox.addWidget(exportButton)
        vbox.addLayout(hbox)

        return topWidget

    def quickSaveIR(self):
        """无需询问文件名，直接快速导出IR文件"""
        if self.refAnalyzer and self.useCombo.currentIndex() == 0:
            analyzer = self.refAnalyzer
        else:
            analyzer = self.analyzer
        if not analyzer:
            return
        ms = int(self.msDuration.value())
        db = int(self.dbRange.value())
        beta = int(self.kaiserBeta.value())
        smoothing = int(self.irSmoothing.value())
        causality = int(self.causality.value())
        _, irInv = analyzer.h_inv(
            ms / 1000, db, beta, smoothing, causality / 100)

        # 自动命名文件并保存到当前的 self.saveDir
        name = f'IR_{ms}ms_{db}dB_{beta}t_{smoothing}s_{causality}c_quick.wav'
        path = self.saveDir / name
        data = np.vstack([irInv, irInv])
        hifi.write_wav(str(path), data, analyzer.rate)
        self.saveDir = Path(path).parent
        # 可根据需要添加成功提示或日志

    def stereoTool(self):

        def leftPressed():
            path, _ = qt.QFileDialog.getOpenFileName(
                self, 'Load left channel', str(self.saveDir), 'WAV (*.wav)')
            leftLabel.setText(path)
            self.saveDir = Path(path).parent

        def rightPressed():
            path, _ = qt.QFileDialog.getOpenFileName(
                self, 'Load right channel', str(self.saveDir), 'WAV (*.wav)')
            rightLabel.setText(path)
            self.saveDir = Path(path).parent

        def save():
            try:
                L = hifi.read_wav(leftLabel.text())
                R = hifi.read_wav(rightLabel.text())
                left = L.data[0]
                right = R.data[1 if len(R) > 1 else 0]
                if L.rate != R.rate or left.size != right.size:
                    raise ValueError(
                        'L and R must have same size and rate')
                stereo = [left, right]
            except Exception as e:
                msg = qt.QMessageBox(qt.QMessageBox.Icon.Critical, 'Error',
                                     str(e), parent=dialog)
                msg.exec()
            else:
                path, _ = qt.QFileDialog.getSaveFileName(
                    self, 'Save stereo channels',
                    str(self.saveDir), 'WAV (*.wav)')
                if path:
                    self.saveDir = Path(path).parent
                    hifi.write_wav(path, stereo, L.rate)

        leftLabel = qt.QLabel('')
        leftButton = qt.QPushButton('Load')
        leftButton.pressed.connect(leftPressed)
        rightLabel = qt.QLabel('')
        rightButton = qt.QPushButton('Load')
        rightButton.pressed.connect(rightPressed)
        saveButton = qt.QPushButton('Save')
        saveButton.pressed.connect(save)

        grid = qt.QGridLayout()
        grid.setColumnMinimumWidth(2, 400)
        grid.addWidget(qt.QLabel('Left in: '), 0, 0)
        grid.addWidget(leftButton, 0, 1)
        grid.addWidget(leftLabel, 0, 2)
        grid.addWidget(qt.QLabel('Right in: '), 1, 0)
        grid.addWidget(rightButton, 1, 1)
        grid.addWidget(rightLabel, 1, 2)
        grid.addWidget(qt.QLabel('Stereo out: '), 2, 0)
        grid.addWidget(saveButton, 2, 1, 1, 2)

        dialog = qt.QDialog(self)
        dialog.setWindowTitle('Convert Left + Right to Stereo')
        dialog.setLayout(grid)
        dialog.exec()

    def causalityTool(self):

        def load():
            path, _ = qt.QFileDialog.getOpenFileName(
                self, 'Load Impulse Response',
                str(self.saveDir), 'WAV (*.wav)')
            inputLabel.setText(path)
            self.saveDir = Path(path).parent

        def save():
            caus = causality.value() / 100
            try:
                irIn = hifi.read_wav(inputLabel.text())
                out = [hifi.transform_causality(channel, caus)
                       for channel in irIn.data]
            except Exception as e:
                msg = qt.QMessageBox(qt.QMessageBox.Icon.Critical, 'Error',
                                     str(e), parent=dialog)
                msg.exec()
            else:
                name = Path(inputLabel.text()).stem + \
                    f'_{causality.value():.0f}c.wav'
                path, _ = qt.QFileDialog.getSaveFileName(
                    self, 'Save Impulse Response',
                    str(self.saveDir / name), 'WAV (*.wav)')
                if path:
                    self.saveDir = Path(path).parent
                    hifi.write_wav(path, out, irIn.rate)

        causality = pg.SpinBox(value=0, step=5, bounds=[0, 100], suffix='%')
        inputLabel = qt.QLabel('')
        loadButton = qt.QPushButton('Load')
        loadButton.pressed.connect(load)
        saveButton = qt.QPushButton('Save')
        saveButton.pressed.connect(save)

        grid = qt.QGridLayout()
        grid.setColumnMinimumWidth(2, 400)
        grid.addWidget(qt.QLabel('Input IR: '), 0, 0)
        grid.addWidget(loadButton, 0, 1)
        grid.addWidget(inputLabel, 0, 2)
        grid.addWidget(qt.QLabel('New causality: '), 1, 0)
        grid.addWidget(causality, 1, 1)
        grid.addWidget(qt.QLabel('Output IR: '), 2, 0)
        grid.addWidget(saveButton, 2, 1, 2, 2)

        dialog = qt.QDialog(self)
        dialog.setWindowTitle('脉冲响应的因果性修正')
        dialog.setLayout(grid)
        dialog.exec()

    def micCalibrationTool(self):
        """使用1000Hz声校准器校准麦克风"""

        def startCalibration():
            """开始校准过程"""
            try:
                # 获取用户选择的校准器档位
                calibrator_level = levelCombo.currentText()
                target_db = 94 if calibrator_level == "94dB" else 114

                # 显示指导信息
                instructions = f"""
真实麦克风校准步骤：

1. 准备工作：
   • 确保{calibrator_level}声校准器电池充足
   • 检查麦克风连接正常

2. 放置麦克风：
   • 将麦克风完全插入声校准器
   • 确保密封良好，无漏气
   • 麦克风应完全被校准器包围

3. 启动校准器：
   • 打开声校准器电源
   • 确认输出1000Hz, {target_db}dB标准信号
   • 等待信号稳定（约2-3秒）

4. 开始测量：
   • 点击下方"开始测量"按钮
   • 保持设备稳定3-5秒钟
   • 避免触碰或移动设备

5. 完成校准：
   • 等待测量完成提示
   • 查看校准结果
   • 保存校准文件
                """
                instructionLabel.setText(instructions)

                # 启动测量
                measureButton.setEnabled(True)  # 启用测量按钮
                statusLabel.setText(f"准备就绪 - 请按照上述步骤放置麦克风到{calibrator_level}校准器中")

                # 设置校准参数
                self.calibration_target_db = target_db
                self.calibration_measuring = False
                self.calibration_samples = []

            except Exception as e:
                qt.QMessageBox.critical(dialog, '错误', f'校准启动失败: {str(e)}')

        def measureCalibration():
            """执行校准测量"""
            try:
                # 设置测量参数为1000Hz附近
                rate = int(self.rateCombo.currentText())
                print(f"开始校准测量，采样率: {rate} Hz")

                # 更新UI状态
                statusLabel.setText("正在录制音频信号... 请保持麦克风和校准器稳定")
                measureButton.setEnabled(False)

                # 启动异步测量
                import asyncio
                import threading

                def run_simple_measurement():
                    """在新线程中运行简单测量"""
                    try:
                        print("开始简单测量线程...")

                        # 使用简单的同步录制
                        self.simple_calibration_measurement(rate, 5.0)  # 5秒录制
                        print("简单测量完成")

                    except Exception as e:
                        print(f"简单测量线程错误: {e}")
                        import traceback
                        traceback.print_exc()
                        # 在主线程中显示错误
                        qtcore.QTimer.singleShot(0, lambda: self.show_calibration_error(str(e)))
                    finally:
                        print("测量线程结束")
                        # 重新启用按钮
                        qtcore.QTimer.singleShot(0, lambda: measureButton.setEnabled(True))
                        qtcore.QTimer.singleShot(0, lambda: statusLabel.setText("测量完成或出错"))

                print("启动测量线程...")
                # 在新线程中启动测量
                thread = threading.Thread(target=run_simple_measurement)
                thread.daemon = True
                thread.start()
                print("测量线程已启动")

            except Exception as e:
                print(f"测量启动错误: {e}")
                qt.QMessageBox.critical(dialog, '错误', f'测量启动失败: {str(e)}')
                measureButton.setEnabled(True)
                statusLabel.setText("测量失败")

        def saveCalibration():
            """保存校准结果"""
            if not hasattr(self, 'calibration_result'):
                qt.QMessageBox.warning(dialog, '警告', '请先完成校准测量')
                return

            try:
                # 生成校准文件名
                timestamp = dt.datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f'mic_calibration_1000Hz_{self.calibration_target_db}dB_{timestamp}.txt'

                path, _ = qt.QFileDialog.getSaveFileName(
                    dialog, '保存麦克风校准文件',
                    str(self.saveDir / filename),
                    'Calibration files (*.txt);;All files (*.*)')

                if path:
                    # 保存校准数据，包含详细信息
                    self.save_calibration_with_details(path, self.calibration_result)
                    self.saveDir = Path(path).parent

                    # 询问是否立即应用校准
                    reply = qt.QMessageBox.question(
                        dialog, '应用校准',
                        '校准文件已保存。是否立即应用此校准？',
                        qt.QMessageBox.StandardButton.Yes | qt.QMessageBox.StandardButton.No)

                    if reply == qt.QMessageBox.StandardButton.Yes:
                        self.calibration = self.calibration_result
                        qt.QMessageBox.information(dialog, '成功', '校准已应用')
                        dialog.accept()

            except Exception as e:
                qt.QMessageBox.critical(dialog, '错误', f'保存失败: {str(e)}')

        def enableSaveButton():
            """直接启用保存按钮的函数"""
            saveButton.setEnabled(True)
            statusLabel.setText("校准完成，可以保存文件")
            print("保存按钮已启用")

        # 创建校准对话框
        dialog = qt.QDialog(self)
        dialog.setWindowTitle('麦克风校准器校准')
        dialog.setFixedSize(500, 400)

        # 保存对话框引用以便后续访问
        self.calibration_dialog = dialog

        layout = qt.QVBoxLayout(dialog)

        # 校准器档位选择
        levelLayout = qt.QHBoxLayout()
        levelLayout.addWidget(qt.QLabel('校准器档位:'))
        levelCombo = qt.QComboBox()
        levelCombo.addItems(['94dB', '114dB'])
        levelLayout.addWidget(levelCombo)
        levelLayout.addStretch()
        layout.addLayout(levelLayout)

        # 指导信息
        instructionLabel = qt.QLabel('请选择校准器档位，然后点击"开始校准"')
        instructionLabel.setWordWrap(True)
        layout.addWidget(instructionLabel)

        # 状态显示
        statusLabel = qt.QLabel('等待开始...')
        statusLabel.setStyleSheet("QLabel { color: blue; font-weight: bold; }")
        layout.addWidget(statusLabel)

        # 测量结果显示
        resultLabel = qt.QLabel('')
        layout.addWidget(resultLabel)

        # 按钮
        buttonLayout = qt.QHBoxLayout()

        startButton = qt.QPushButton('开始校准')
        startButton.clicked.connect(startCalibration)
        buttonLayout.addWidget(startButton)

        measureButton = qt.QPushButton('开始测量')
        measureButton.clicked.connect(measureCalibration)
        measureButton.setEnabled(False)
        buttonLayout.addWidget(measureButton)

        # 移除测试按钮，专注于真实测量

        saveButton = qt.QPushButton('保存校准')
        saveButton.clicked.connect(saveCalibration)
        saveButton.setEnabled(False)
        saveButton.setObjectName("saveCalibrationButton")  # 添加对象名称便于查找
        buttonLayout.addWidget(saveButton)

        # 保存按钮引用以便后续直接访问
        self.calibration_save_button = saveButton

        closeButton = qt.QPushButton('关闭')
        closeButton.clicked.connect(dialog.reject)
        buttonLayout.addWidget(closeButton)

        layout.addLayout(buttonLayout)

        dialog.exec()

    def simple_calibration_measurement(self, rate, duration):
        """使用简单的同步录制进行校准测量"""
        try:
            print(f"开始简单校准测量，采样率: {rate}, 时长: {duration}秒")

            import sounddevice as sd

            # 直接使用sounddevice进行录制
            print("开始录制音频...")
            recording = sd.rec(int(duration * rate),
                             samplerate=rate,
                             channels=1,
                             dtype='float32')

            # 显示录制进度
            for i in range(int(duration)):
                import time
                time.sleep(1)
                print(f"录制进度: {i+1}/{int(duration)}秒")

            print("等待录制完成...")
            sd.wait()  # 等待录制完成

            print(f"录制完成！数据形状: {recording.shape}")

            # 将录制数据转换为一维数组
            audio_data = recording.flatten()
            print(f"音频数据长度: {len(audio_data)} 样本")

            if len(audio_data) > 0 and np.max(np.abs(audio_data)) > 0.001:
                print("检测到音频信号，开始分析...")
                # 分析1000Hz频率的幅度
                self.analyzeCalibrationData(audio_data, rate)

                # 确保UI更新和保存按钮启用
                qtcore.QTimer.singleShot(100, self.enable_save_button_after_measurement)
            else:
                raise ValueError("未录制到有效音频数据，请检查麦克风连接和音频设置")

        except Exception as e:
            print(f"简单测量错误: {e}")
            import traceback
            traceback.print_exc()
            # 在主线程中显示错误
            qtcore.QTimer.singleShot(0, lambda: self.show_calibration_error(str(e)))

    def analyzeCalibrationData(self, audio_data, sample_rate):
        """分析校准数据并计算校准系数"""
        try:
            print(f"开始分析真实校准数据:")
            print(f"  音频数据长度: {len(audio_data)} 样本")
            print(f"  采样率: {sample_rate} Hz")
            print(f"  录制时长: {len(audio_data)/sample_rate:.2f} 秒")

            # 使用FFT分析1000Hz频率的幅度
            fft_data = np.fft.fft(audio_data)
            freqs = np.fft.fftfreq(len(audio_data), 1/sample_rate)

            # 找到1000Hz附近的频率索引
            target_freq = 1000.0
            freq_tolerance = 50.0  # ±50Hz容差

            # 找到目标频率范围内的索引
            freq_mask = (np.abs(freqs - target_freq) <= freq_tolerance) & (freqs > 0)

            if not np.any(freq_mask):
                raise ValueError(f"未找到{target_freq}Hz信号，请检查校准器是否正常工作")

            # 计算该频率范围内的平均幅度
            target_amplitude = np.mean(np.abs(fft_data[freq_mask]))

            # 找到1000Hz附近的具体频率
            freq_indices = np.where(freq_mask)[0]
            actual_freqs = freqs[freq_indices]
            closest_freq = actual_freqs[np.argmax(np.abs(fft_data[freq_indices]))]

            print(f"  检测到的频率范围: {actual_freqs.min():.1f} - {actual_freqs.max():.1f} Hz")
            print(f"  最强信号频率: {closest_freq:.1f} Hz")
            print(f"  信号幅度: {target_amplitude:.6f}")

            # 转换为dB
            if target_amplitude > 0:
                measured_db = 20 * np.log10(target_amplitude)

                print(f"  测量电平: {measured_db:.2f} dB")
                print(f"  目标电平: {self.calibration_target_db} dB")

                # 计算校准偏移
                calibration_offset = self.calibration_target_db - measured_db

                print(f"  校准偏移: {calibration_offset:.2f} dB")

                # 创建校准数据 - 在1000Hz处应用偏移
                self.calibration_result = [
                    (20, calibration_offset),      # 低频
                    (1000, calibration_offset),    # 校准频率
                    (20000, calibration_offset)    # 高频
                ]

                print(f"  校准数据: {self.calibration_result}")
                print("✅ 真实校准测量完成！")

                # 更新UI显示结果
                self.updateCalibrationUI(measured_db, calibration_offset)

                # 确保保存按钮被启用
                qtcore.QTimer.singleShot(200, self.enable_save_button_after_measurement)

            else:
                raise ValueError("测量到的信号幅度为零")

        except Exception as e:
            print(f"数据分析错误: {e}")
            # 在主线程中显示错误
            qtcore.QTimer.singleShot(0, lambda: qt.QMessageBox.critical(
                self, '分析错误', f'校准数据分析失败: {str(e)}'))

    def updateCalibrationUI(self, measured_db, calibration_offset):
        """更新校准UI显示结果"""
        # 保存测量信息用于文件保存
        self.last_measured_db = measured_db

        def update_ui():
            try:
                # 直接启用保存按钮（如果引用存在）
                if hasattr(self, 'calibration_save_button'):
                    self.calibration_save_button.setEnabled(True)
                    print("直接启用保存按钮成功")

                # 查找对话框中的标签并更新
                if hasattr(self, 'calibration_dialog'):
                    dialog = self.calibration_dialog

                    # 查找结果标签
                    result_labels = dialog.findChildren(qt.QLabel)
                    for label in result_labels:
                        if label.text() == '' or '校准结果' in label.text():
                            result_text = f"""校准结果：
目标电平: {self.calibration_target_db} dB
测量电平: {measured_db:.2f} dB
校准偏移: {calibration_offset:.2f} dB

校准完成！可以保存校准文件。"""
                            label.setText(result_text)
                            label.setStyleSheet("QLabel { color: green; font-weight: bold; }")
                            break

                    # 更新状态标签
                    for label in result_labels:
                        if 'color: blue' in label.styleSheet():
                            label.setText("测量完成")
                            label.setStyleSheet("QLabel { color: green; font-weight: bold; }")
                            break

                    # 备用方法：通过查找按钮启用
                    buttons = dialog.findChildren(qt.QPushButton)
                    for button in buttons:
                        if button.text() == '保存校准':
                            button.setEnabled(True)
                            print(f"通过查找启用保存按钮: {button.text()}")
                        elif button.text() == '开始测量':
                            button.setEnabled(True)

            except Exception as e:
                print(f"更新UI错误: {e}")

        # 在主线程中更新UI
        qtcore.QTimer.singleShot(0, update_ui)

    def show_calibration_error(self, error_msg: str):
        """显示校准错误信息"""
        qt.QMessageBox.critical(self, '校准错误', f'校准测量失败: {error_msg}')

    def enable_save_button_after_measurement(self):
        """测量完成后启用保存按钮"""
        try:
            print("尝试启用保存按钮...")

            # 查找校准对话框
            for widget in qt.QApplication.topLevelWidgets():
                if isinstance(widget, qt.QDialog) and widget.windowTitle() == '麦克风校准器校准':
                    print("找到校准对话框")

                    # 查找并启用保存按钮
                    buttons = widget.findChildren(qt.QPushButton)
                    for button in buttons:
                        if button.text() == '保存校准':
                            button.setEnabled(True)
                            print("✅ 保存按钮已启用")

                        # 同时重新启用测量按钮
                        elif button.text() == '开始测量':
                            button.setEnabled(True)
                            print("✅ 测量按钮已重新启用")

                    # 更新状态标签
                    labels = widget.findChildren(qt.QLabel)
                    for label in labels:
                        if 'color: blue' in label.styleSheet() or '正在录制' in label.text():
                            label.setText("✅ 校准测量完成！可以保存校准文件")
                            label.setStyleSheet("QLabel { color: green; font-weight: bold; }")
                            print("✅ 状态标签已更新")
                            break

                    break
            else:
                print("❌ 未找到校准对话框")

        except Exception as e:
            print(f"启用保存按钮错误: {e}")

    def save_calibration_with_details(self, path, calibration_data):
        """保存校准文件，包含详细信息"""
        try:
            import datetime as dt

            # 获取校准详细信息
            target_db = getattr(self, 'calibration_target_db', 94)
            measured_db = getattr(self, 'last_measured_db', 0)
            calibration_offset = calibration_data[1][1] if len(calibration_data) > 1 else 0

            # 创建详细的文件内容
            timestamp = dt.datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            content = f"""# 麦克风校准文件
# 生成时间: {timestamp}
# 校准器设置: 1000Hz, {target_db}dB
# 测量电平: {measured_db:.2f}dB
# 校准偏移: {calibration_offset:.2f}dB
#
# 格式: 频率(Hz) 校准偏移(dB)
"""

            # 添加校准数据
            for freq, offset in calibration_data:
                content += f"{freq} {offset:.6f}\n"

            # 写入文件
            with open(path, 'w', encoding='utf-8') as f:
                f.write(content)

            print(f"保存校准文件: {path}")
            print(f"校准偏移: {calibration_offset:.2f}dB")

        except Exception as e:
            print(f"保存详细校准文件错误: {e}")
            # 回退到简单保存
            write_correction(path, calibration_data)

    # 移除测试校准功能，专注于真实测量

    def sharedControls(self) -> qt.QWidget:
        topWidget = qt.QWidget()
        vbox = qt.QVBoxLayout()
        topWidget.setLayout(vbox)

        self.buttons = buttons = qt.QButtonGroup()
        buttons.setExclusive(True)
        spectrumButton = qt.QRadioButton('频谱')
        irButton = qt.QRadioButton('脉冲响应')
        buttons.addButton(spectrumButton, 0)
        buttons.addButton(irButton, 1)
        spectrumButton.setChecked(True)
        buttons.idClicked.connect(self.stack.setCurrentIndex)

        def setAudioChanged():
            self.audioChanged = True

        self.rateCombo = qt.QComboBox()
        self.rateCombo.addItems(str(rate) for rate in self.SAMPLE_RATES)
        self.rateCombo.currentIndexChanged.connect(setAudioChanged)

        def toolsPressed():
            tools.popup(toolsButton.mapToGlobal(qtcore.QPoint(0, 0)))

        tools = qt.QMenu()
        tools.addAction('合并声道', self.stereoTool)
        tools.addAction('脉冲响应因果性修正', self.causalityTool)
        tools.addAction('麦克风校准器校准', self.micCalibrationTool)

        toolsButton = qt.QPushButton('Tools...')
        toolsButton.pressed.connect(toolsPressed)

        def loadCalibration():
            path, _ = qt.QFileDialog.getOpenFileName(
                self, '加载麦克风校准', str(self.saveDir))
            if path:
                cal = hifi.read_correction(path)
                if cal:
                    self.calibration = cal
                    calAction.setText(calTxt + path)
                    self.saveDir = Path(path).parent
                else:
                    clearCalibration()

        def clearCalibration():
            self.calibration = None
            calAction.setText(calTxt + 'None')

        def loadTarget():
            path, _ = qt.QFileDialog.getOpenFileName(
                self, '加载目标曲线', str(self.saveDir))
            if path:
                target = hifi.read_correction(path)
                if target:
                    self.target = target
                    targetAction.setText(targetTxt + path)
                    self.saveDir = Path(path).parent
                else:
                    clearTarget()

        def clearTarget():
            self.target = None
            targetAction.setText(targetTxt + 'None')

        def correctionsPressed():
            corr.popup(correctionsButton.mapToGlobal(qtcore.QPoint(0, 0)))

        calTxt = '麦克风校准: '
        targetTxt = '目标曲线: '
        corr = qt.QMenu()
        calAction = corr.addAction(calTxt + 'None', loadCalibration)
        corr.addAction('加载', loadCalibration)
        corr.addAction('清除', clearCalibration)
        corr.addSeparator()
        targetAction = corr.addAction(targetTxt + 'None', loadTarget)
        corr.addAction('加载', loadTarget)
        corr.addAction('清除', clearTarget)

        correctionsButton = qt.QPushButton('校准...')
        correctionsButton.pressed.connect(correctionsPressed)

        def storeButtonClicked():
            if self.analyzer:
                if self.analyzer.isCompatible(self.refAnalyzer):
                    self.refAnalyzer.addMeasurements(self.analyzer)
                else:
                    self.refAnalyzer = copy.copy(self.analyzer)
                setMeasurementsText()
                self.plot()

        def clearButtonClicked():
            self.refAnalyzer = None
            self.refSpectrumPlot.clear()
            setMeasurementsText()
            self.plot()

        def setMeasurementsText():
            num = self.refAnalyzer.numMeasurements if self.refAnalyzer else 0
            measurementsLabel.setText(f'测量值: {num if num else ""}')

        measurementsLabel = qt.QLabel('')
        setMeasurementsText()

        storeButton = qt.QPushButton('储存')
        storeButton.clicked.connect(storeButtonClicked)
        storeButton.setShortcut('S')
        storeButton.setToolTip('<Key S>')

        clearButton = qt.QPushButton('清除')
        clearButton.clicked.connect(clearButtonClicked)
        clearButton.setShortcut('C')
        clearButton.setToolTip('<Key C>')

        def load():
            path, _ = qt.QFileDialog.getOpenFileName(
                self, '加载测量结果', str(self.saveDir))
            if path:
                with open(path, 'rb') as f:
                    self.refAnalyzer = pickle.load(f)
                setMeasurementsText()
                self.plot()

        def loadStore():
            path, _ = qt.QFileDialog.getOpenFileName(
                self, '加载并储存测量结果', str(self.saveDir))
            if path:
                with open(path, 'rb') as f:
                    analyzer: hifi.Analyzer = pickle.load(f)
                if analyzer and analyzer.isCompatible(self.refAnalyzer):
                    self.refAnalyzer.addMeasurements(analyzer)
                else:
                    self.refAnalyzer = analyzer
                setMeasurementsText()
                self.plot()

        def save():
            analyzer = self.refAnalyzer or self.analyzer
            timestamp = dt.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            name = f'Measurements={analyzer.numMeasurements}, {timestamp}'
            path, _ = qt.QFileDialog.getSaveFileName(
                self, '保存测量',
                str(self.saveDir / name))
            if path:
                self.saveDir = Path(path).parent
                with open(path, 'wb') as f:
                    pickle.dump(analyzer, f)
                self.plot()

        def filePressed():
            fileMenu.popup(fileButton.mapToGlobal(qtcore.QPoint(0, 0)))

        fileMenu = qt.QMenu()
        fileMenu.addAction('加载', load)
        fileMenu.addAction('加载并储存', loadStore)
        fileMenu.addAction('保存', save)

        fileButton = qt.QPushButton('读取文件')
        fileButton.clicked.connect(filePressed)

        screenshotButton = qt.QPushButton('Screenshot')
        screenshotButton.clicked.connect(self.screenshot)

        def setPaused():
            self.paused = not self.paused

        pauseButton = qt.QPushButton('停止/开始')
        pauseButton.setShortcut('Space')
        pauseButton.setToolTip('<Space>')
        pauseButton.setFocusPolicy(qtcore.Qt.FocusPolicy.NoFocus)
        pauseButton.clicked.connect(setPaused)

        exitButton = qt.QPushButton('退出')
        exitButton.setShortcut('Ctrl+Q')
        exitButton.setToolTip('Ctrl+Q')
        exitButton.clicked.connect(self.close)

        hbox = qt.QHBoxLayout()
        hbox.addWidget(spectrumButton)
        hbox.addSpacing(16)
        hbox.addWidget(irButton)
        hbox.addSpacing(64)
        hbox.addWidget(toolsButton)
        hbox.addWidget(qt.QLabel('工具'))
        hbox.addSpacing(32)
        hbox.addWidget(correctionsButton)
        hbox.addStretch(1)
        hbox.addWidget(measurementsLabel)
        hbox.addWidget(storeButton)
        hbox.addWidget(clearButton)
        hbox.addWidget(fileButton)
        hbox.addStretch(1)
        hbox.addWidget(qt.QLabel('采样率:'))
        hbox.addWidget(self.rateCombo)
        hbox.addStretch(1)
        # hbox.addWidget(screenshotButton)
        hbox.addWidget(pauseButton)
        hbox.addWidget(exitButton)
        vbox.addLayout(hbox)

        return topWidget


class Axis(pg.AxisItem):

    def logTickStrings(self, values, scale, spacing):
        return [pg.siFormat(10 ** v).replace(' ', '') for v in values]


async def wrap_coro(coro):
    try:
        await coro
    except asyncio.CancelledError:
        pass
    except Exception:
        logging.getLogger('G-VoTest').exception('Error in task:')


def main():
    _ = qt.QApplication(sys.argv)
    app = App()
    app.run()


if __name__ == '__main__':
    main()
