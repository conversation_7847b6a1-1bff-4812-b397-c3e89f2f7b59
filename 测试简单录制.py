#!/usr/bin/env python3
"""
测试简单录制方法

验证新的简单录制方法是否能解决卡住的问题
"""

import sys
import time
import numpy as np
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

def test_simple_recording():
    """测试简单录制方法"""
    
    print("=== 测试简单录制方法 ===\n")
    
    try:
        import sounddevice as sd
        
        print("1. 音频设备信息:")
        default_input = sd.query_devices(kind='input')
        print(f"   默认输入设备: {default_input['name']}")
        print(f"   默认采样率: {default_input['default_samplerate']}")
        
        # 测试参数
        rate = 48000
        duration = 3.0
        
        print(f"\n2. 开始录制测试:")
        print(f"   采样率: {rate} Hz")
        print(f"   时长: {duration} 秒")
        
        # 开始录制
        print("   开始录制音频...")
        recording = sd.rec(int(duration * rate), 
                         samplerate=rate, 
                         channels=1, 
                         dtype='float32')
        
        # 显示进度
        for i in range(int(duration)):
            time.sleep(1)
            print(f"   录制进度: {i+1}/{int(duration)}秒")
        
        print("   等待录制完成...")
        sd.wait()  # 等待录制完成
        
        print(f"   录制完成！数据形状: {recording.shape}")
        
        # 分析录制结果
        audio_data = recording.flatten()
        print(f"   音频数据长度: {len(audio_data)} 样本")
        print(f"   最大值: {np.max(np.abs(audio_data)):.6f}")
        print(f"   RMS值: {np.sqrt(np.mean(audio_data**2)):.6f}")
        
        if np.max(np.abs(audio_data)) > 0.001:
            print("   ✅ 检测到音频信号")
            
            # 简单频谱分析
            fft_data = np.fft.fft(audio_data)
            freqs = np.fft.fftfreq(len(audio_data), 1/rate)
            
            # 检查1000Hz附近
            target_freq = 1000.0
            freq_tolerance = 50.0
            freq_mask = (np.abs(freqs - target_freq) <= freq_tolerance) & (freqs > 0)
            
            if np.any(freq_mask):
                target_amplitude = np.mean(np.abs(fft_data[freq_mask]))
                measured_db = 20 * np.log10(target_amplitude)
                
                print(f"   检测到1000Hz信号")
                print(f"   信号幅度: {target_amplitude:.6f}")
                print(f"   测量电平: {measured_db:.2f} dB")
                
                # 模拟校准计算
                target_db = 94
                calibration_offset = target_db - measured_db
                print(f"   校准偏移: {calibration_offset:.2f} dB")
                
                print("   ✅ 简单录制和分析成功！")
                return True
            else:
                print("   ⚠️ 未检测到1000Hz信号，但录制正常")
                return True
        else:
            print("   ❌ 未检测到音频信号")
            return False
            
    except Exception as e:
        print(f"❌ 简单录制测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_threading_recording():
    """测试在线程中进行录制"""
    
    print("\n=== 测试线程录制 ===\n")
    
    try:
        import threading
        import sounddevice as sd
        
        result = {'success': False, 'error': None, 'data': None}
        
        def recording_thread():
            """录制线程函数"""
            try:
                print("   线程开始录制...")
                rate = 48000
                duration = 2.0
                
                recording = sd.rec(int(duration * rate), 
                                 samplerate=rate, 
                                 channels=1, 
                                 dtype='float32')
                
                print("   线程等待录制完成...")
                sd.wait()
                
                audio_data = recording.flatten()
                print(f"   线程录制完成，数据长度: {len(audio_data)}")
                
                result['success'] = True
                result['data'] = audio_data
                
            except Exception as e:
                print(f"   线程录制错误: {e}")
                result['error'] = str(e)
        
        print("1. 启动录制线程...")
        thread = threading.Thread(target=recording_thread)
        thread.daemon = True
        thread.start()
        
        print("2. 等待线程完成...")
        thread.join(timeout=5.0)  # 最多等待5秒
        
        if result['success']:
            print("   ✅ 线程录制成功")
            data = result['data']
            print(f"   数据长度: {len(data)}")
            print(f"   最大值: {np.max(np.abs(data)):.6f}")
            return True
        elif result['error']:
            print(f"   ❌ 线程录制失败: {result['error']}")
            return False
        else:
            print("   ❌ 线程超时或未完成")
            return False
            
    except Exception as e:
        print(f"❌ 线程测试失败: {e}")
        return False

def simulate_app_measurement():
    """模拟应用程序中的测量过程"""
    
    print("\n=== 模拟应用测量过程 ===\n")
    
    try:
        import threading
        import sounddevice as sd
        
        class MockCalibrator:
            def __init__(self):
                self.calibration_target_db = 94
                self.calibration_result = None
                
            def simple_calibration_measurement(self, rate, duration):
                """模拟简单校准测量"""
                print(f"开始简单校准测量，采样率: {rate}, 时长: {duration}秒")
                
                # 直接使用sounddevice进行录制
                print("开始录制音频...")
                recording = sd.rec(int(duration * rate), 
                                 samplerate=rate, 
                                 channels=1, 
                                 dtype='float32')
                
                # 显示录制进度
                for i in range(int(duration)):
                    time.sleep(1)
                    print(f"录制进度: {i+1}/{int(duration)}秒")
                
                print("等待录制完成...")
                sd.wait()
                
                print(f"录制完成！数据形状: {recording.shape}")
                
                # 分析数据
                audio_data = recording.flatten()
                if len(audio_data) > 0 and np.max(np.abs(audio_data)) > 0.001:
                    print("检测到音频信号，开始分析...")
                    self.analyze_calibration_data(audio_data, rate)
                else:
                    raise ValueError("未录制到有效音频数据")
                    
            def analyze_calibration_data(self, audio_data, sample_rate):
                """分析校准数据"""
                print("分析校准数据...")
                
                # FFT分析
                fft_data = np.fft.fft(audio_data)
                freqs = np.fft.fftfreq(len(audio_data), 1/sample_rate)
                
                # 找到1000Hz
                target_freq = 1000.0
                freq_tolerance = 50.0
                freq_mask = (np.abs(freqs - target_freq) <= freq_tolerance) & (freqs > 0)
                
                if np.any(freq_mask):
                    target_amplitude = np.mean(np.abs(fft_data[freq_mask]))
                    measured_db = 20 * np.log10(target_amplitude)
                    calibration_offset = self.calibration_target_db - measured_db
                    
                    self.calibration_result = [
                        (20, calibration_offset),
                        (1000, calibration_offset),
                        (20000, calibration_offset)
                    ]
                    
                    print(f"测量电平: {measured_db:.2f} dB")
                    print(f"校准偏移: {calibration_offset:.2f} dB")
                    print("✅ 校准分析完成")
                else:
                    raise ValueError("未找到1000Hz信号")
        
        # 模拟测量过程
        calibrator = MockCalibrator()
        
        def run_measurement():
            try:
                calibrator.simple_calibration_measurement(48000, 3.0)
                print("✅ 模拟测量成功")
            except Exception as e:
                print(f"❌ 模拟测量失败: {e}")
        
        print("1. 启动模拟测量线程...")
        thread = threading.Thread(target=run_measurement)
        thread.daemon = True
        thread.start()
        
        print("2. 等待测量完成...")
        thread.join()
        
        if calibrator.calibration_result:
            print(f"3. 校准结果: {calibrator.calibration_result}")
            return True
        else:
            print("3. 未获得校准结果")
            return False
            
    except Exception as e:
        print(f"❌ 模拟测试失败: {e}")
        return False

def main():
    """主函数"""
    
    print("简单录制方法测试\n")
    
    # 测试简单录制
    test1_passed = test_simple_recording()
    
    # 测试线程录制
    test2_passed = test_threading_recording()
    
    # 模拟应用测量
    test3_passed = simulate_app_measurement()
    
    print(f"\n=== 测试结果总结 ===")
    print(f"简单录制测试: {'✅ 通过' if test1_passed else '❌ 失败'}")
    print(f"线程录制测试: {'✅ 通过' if test2_passed else '❌ 失败'}")
    print(f"模拟应用测试: {'✅ 通过' if test3_passed else '❌ 失败'}")
    
    if test1_passed and test2_passed and test3_passed:
        print("\n🎉 所有测试通过！新的简单录制方法应该能解决卡住的问题。")
    else:
        print("\n❌ 部分测试失败，需要进一步调试。")

if __name__ == "__main__":
    main()
